# 阶梯递增价格计算说明

## 功能概述

新增了阶梯递增价格计算功能，支持"先分阶梯，阶梯内递增"的复杂计费模式。这种模式常见于国际物流报价单，如：

```
CountryCode	重量段(KG)	首重	首重价格	续重	续重价格	挂号费
GH	0.001 - 15	0.1	142.1	0.1	26.1	0
GH	15.001 - 30	0.1	142.1	0.1	26.1	0
KE	0.001 - 15	0.1	105.1	0.1	18.1	0
KE	15.001 - 30	0.1	105.1	0.1	18.1	0
```

## 数据结构

### 数据库字段

在 `logistics_product_price` 表中新增字段：
```sql
ALTER TABLE logistics_product_price 
ADD COLUMN tiered_incremental_prices TEXT COMMENT '阶梯递增价格配置(JSON格式)';
```

### 配置格式

阶梯递增价格配置使用JSON格式：

```json
[
  {
    "tierStart": 1,        // 0.001kg = 1g
    "tierEnd": 15000,      // 15kg = 15000g  
    "firstWeight": 100,    // 0.1kg = 100g
    "firstPrice": 14210,   // 142.1元 = 14210分
    "additionalWeight": 100, // 0.1kg = 100g
    "additionalPrice": 2610, // 26.1元 = 2610分
    "registrationFee": 0   // 挂号费(分)
  },
  {
    "tierStart": 15001,
    "tierEnd": 30000,
    "firstWeight": 100,
    "firstPrice": 14210,
    "additionalWeight": 100,
    "additionalPrice": 2610,
    "registrationFee": 0
  }
]
```

## 计算逻辑

### 计算流程

1. **匹配阶梯**: 根据包裹重量找到对应的阶梯
2. **阶梯内递增计算**:
   - 首重: `firstWeight` = `firstPrice`
   - 剩余重量: 总重量 - 首重
   - 续重次数: 向上取整(剩余重量 ÷ 续重单位)
   - 续重费用: 续重次数 × 续重单价
   - 总费用: 首重费用 + 续重费用 + 挂号费

### 计算示例

#### 示例1: GH国家，重量1.5kg (1500g)

1. **匹配阶梯**: 1500g 落在第一个阶梯 (1-15000g)
2. **计算过程**:
   - 首重: 100g = 142.1元 = 14210分
   - 剩余重量: 1500g - 100g = 1400g
   - 续重次数: 1400g ÷ 100g = 14次
   - 续重费用: 14 × 26.1元 = 14 × 2610分 = 36540分
   - 总费用: 14210 + 36540 = 50750分 = 507.5元

#### 示例2: KE国家，重量20kg (20000g)

1. **匹配阶梯**: 20000g 落在第二个阶梯 (15001-30000g)
2. **计算过程**:
   - 首重: 100g = 105.1元 = 10510分
   - 剩余重量: 20000g - 100g = 19900g
   - 续重次数: 19900g ÷ 100g = 199次
   - 续重费用: 199 × 18.1元 = 199 × 1810分 = 360190分
   - 总费用: 10510 + 360190 = 370700分 = 3707元

## 代码实现

### 1. 计算工具类

`TieredIncrementalPriceCalculator` 类负责阶梯递增价格计算：

```java
public static TieredIncrementalResult calculateTieredIncrementalPrice(
    String tieredIncrementalPricesJson, 
    Integer weightGrams, 
    Integer defaultRegistrationFee) {
    
    // 1. 解析配置
    List<TieredIncrementalItem> tieredItems = parseTieredIncrementalPrices(tieredIncrementalPricesJson);
    
    // 2. 匹配阶梯
    TieredIncrementalItem matchedTier = findMatchingTier(tieredItems, weightGrams);
    
    // 3. 计算费用
    BigDecimal baseFee = calculateIncrementalFee(matchedTier, weightGrams);
    
    // 4. 处理挂号费
    BigDecimal registrationFee = ...
    
    // 5. 返回结果
    return new TieredIncrementalResult(baseFee, registrationFee, ...);
}
```

### 2. 集成到运费计算服务

在 `ShippingQuoteServiceImpl` 中集成阶梯递增价格计算：

```java
// 根据价格类型和配置选择计算方式
if (StrUtil.isNotBlank(priceRule.getTieredIncrementalPrices())) {
    // 阶梯递增价格计算
    TieredIncrementalResult result = TieredIncrementalPriceCalculator.calculateTieredIncrementalPrice(
        priceRule.getTieredIncrementalPrices(), 
        chargeableWeight, 
        priceRule.getRegistrationFee()
    );
    
    baseFee = result.getBaseFee();
    registrationFee = result.getRegistrationFee();
    
} else if ("TIERED".equals(priceRule.getPriceType())) {
    // 纯阶梯价格计算
    ...
} else {
    // 首重续重计算
    ...
}
```

## 数据导入

### Excel导入格式

Excel导入时，可以使用以下格式：

| 国家代码 | 重量段(KG) | 首重(KG) | 首重价格(元) | 续重(KG) | 续重价格(元) | 挂号费(元) |
|---------|-----------|---------|------------|---------|------------|----------|
| GH      | 0.001-15  | 0.1     | 142.1      | 0.1     | 26.1       | 0        |
| GH      | 15.001-30 | 0.1     | 142.1      | 0.1     | 26.1       | 0        |

### 导入转换逻辑

```java
// 伪代码
List<TieredIncrementalItem> items = new ArrayList<>();

for (ExcelRow row : excelRows) {
    TieredIncrementalItem item = new TieredIncrementalItem();
    
    // 解析重量段
    String[] weightRange = row.getWeightRange().split("-");
    double minKg = Double.parseDouble(weightRange[0].trim());
    double maxKg = Double.parseDouble(weightRange[1].trim());
    
    // 转换为克
    item.setTierStart((int)(minKg * 1000));
    item.setTierEnd((int)(maxKg * 1000));
    
    // 设置首重和续重
    item.setFirstWeight((int)(row.getFirstWeight() * 1000));
    item.setFirstPrice((int)(row.getFirstPrice() * 100));
    item.setAdditionalWeight((int)(row.getAdditionalWeight() * 1000));
    item.setAdditionalPrice((int)(row.getAdditionalPrice() * 100));
    item.setRegistrationFee((int)(row.getRegistrationFee() * 100));
    
    items.add(item);
}

// 转换为JSON
String tieredIncrementalPricesJson = JSON.toJSONString(items);
```

## 测试验证

### 测试用例1: GH国家，1.5kg

```java
String tieredIncrementalPricesJson = "[{\"tierStart\":1,\"tierEnd\":15000,\"firstWeight\":100,\"firstPrice\":14210,\"additionalWeight\":100,\"additionalPrice\":2610,\"registrationFee\":0},{\"tierStart\":15001,\"tierEnd\":30000,\"firstWeight\":100,\"firstPrice\":14210,\"additionalWeight\":100,\"additionalPrice\":2610,\"registrationFee\":0}]";

TieredIncrementalResult result = TieredIncrementalPriceCalculator.calculateTieredIncrementalPrice(
    tieredIncrementalPricesJson, 1500, 0);

// 预期结果: 基础运费=50750分, 挂号费=0分
assertEquals(new BigDecimal("50750"), result.getBaseFee());
assertEquals(new BigDecimal("0"), result.getRegistrationFee());
```

### 测试用例2: KE国家，20kg

```java
String tieredIncrementalPricesJson = "[{\"tierStart\":1,\"tierEnd\":15000,\"firstWeight\":100,\"firstPrice\":10510,\"additionalWeight\":100,\"additionalPrice\":1810,\"registrationFee\":0},{\"tierStart\":15001,\"tierEnd\":30000,\"firstWeight\":100,\"firstPrice\":10510,\"additionalWeight\":100,\"additionalPrice\":1810,\"registrationFee\":0}]";

TieredIncrementalResult result = TieredIncrementalPriceCalculator.calculateTieredIncrementalPrice(
    tieredIncrementalPricesJson, 20000, 0);

// 预期结果: 基础运费=370700分, 挂号费=0分
assertEquals(new BigDecimal("370700"), result.getBaseFee());
assertEquals(new BigDecimal("0"), result.getRegistrationFee());
```

## 注意事项

1. **重量单位转换**:
   - 数据库存储使用克(g)为单位
   - Excel导入时需要将千克(kg)转换为克(g)
   - 前端显示时需要将克(g)转换为千克(kg)

2. **价格单位转换**:
   - 数据库存储使用分为单位
   - Excel导入时需要将元转换为分
   - 前端显示时需要将分转换为元

3. **计算精度**:
   - 使用BigDecimal进行精确计算
   - 向上取整计算续重次数
   - 最终结果保留整数(分)

4. **兼容性**:
   - 系统会优先检查阶梯递增价格配置
   - 如果存在则使用阶梯递增计算
   - 否则回退到原有的计算方式

## 扩展建议

1. **管理界面**:
   - 提供专门的阶梯递增价格配置界面
   - 支持批量导入和编辑
   - 提供可视化的价格曲线图

2. **性能优化**:
   - 考虑缓存常用国家的价格配置
   - 优化JSON解析性能
   - 批量计算时复用已解析的配置

3. **功能扩展**:
   - 支持更复杂的阶梯递增规则
   - 添加特殊时段价格调整
   - 支持多币种计算
