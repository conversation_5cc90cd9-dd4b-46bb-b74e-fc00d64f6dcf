# 挂号费计算修复验证

## 问题描述

从日志中发现，虽然计算出了挂号费2700分，但在总费用计算中没有包含挂号费：

```
阶梯价格计算结果: 基础运费=3475分, 挂号费=2700分, 使用阶梯挂号费=true
费用明细: 运费=3475分, 操作费=0分, 服务费=0分, 清关费=0分
总费用: 3475分  // ❌ 缺少挂号费2700分
```

**正确的总费用应该是**: 3475 + 2700 = 6175分

## 修复内容

### 1. 修复新版本计算方法

在 `setFreightInfo` 方法中添加挂号费设置：
```java
// 设置挂号费（如果有的话）
if (feeResult.getRegistrationFee() != null && feeResult.getRegistrationFee().compareTo(BigDecimal.ZERO) > 0) {
    feeDetail.setAdditionalFee(feeResult.getRegistrationFee().toString());
    log.info("挂号费: {}分", feeResult.getRegistrationFee());
}
```

在 `calculateTotalFee` 方法中包含挂号费：
```java
// 添加挂号费到总费用计算中
BigDecimal additionalFee = BigDecimal.ZERO;
if (feeDetail.getAdditionalFee() != null && !feeDetail.getAdditionalFee().equals("0")) {
    additionalFee = new BigDecimal(feeDetail.getAdditionalFee());
}

BigDecimal total = freight.add(operationFee).add(serviceFee).add(customsFee).add(additionalFee);
```

### 2. 修复旧版本计算方法

在 `calculateFees` 方法中：
```java
// 设置挂号费
feeDetail.setAdditionalFee(registrationFee.toString());

// 计算总费用（已经包含挂号费）
BigDecimal totalFee = baseFee.add(registrationFee).add(operationFee).add(serviceFee).add(customsFee);
```

### 3. 统一金额单位

所有金额字段都改为直接返回分为单位：
```java
feeDetail.setFreight(baseFee.toString());           // 不再使用formatMoney
feeDetail.setOperationFee(operationFee.toString()); // 不再使用formatMoney
feeDetail.setTotal(totalFee.toString());            // 不再使用formatMoney
```

## 预期的修复后日志

重新测试后，应该看到：

```
=== 设置附加费用 ===
操作费: 0分
服务费: 0分
清关费: 0分
燃油费: 0分, 空运附加费: 0分
=== 附加费用设置完成 ===

设置运费信息: 基础运费=3475分, 挂号费=2700分
挂号费: 2700分

=== 计算总费用 ===
费用明细: 运费=3475分, 操作费=0分, 服务费=0分, 清关费=0分, 挂号费=2700分
总费用: 3475 + 0 + 0 + 0 + 2700 = 6175分
=== 总费用计算完成 ===

运费计算完成: 总费用=6175
```

## 预期的API返回结果

```json
{
  "feeDetail": {
    "weight": 250,
    "currency": "CNY",
    "volumeWeight": 0,
    "chargeableWeight": 250,
    "total": "6175",           // ✅ 包含挂号费的总费用
    "freight": "3475",         // 基础运费
    "customsFee": "0",
    "fuelFee": "0",
    "airSurcharge": "0",
    "operationFee": "0",
    "serviceFee": "0",
    "additionalFee": "2700",   // ✅ 挂号费
    "feeFirst": "13100",
    "feeContinue": "0",
    "weightFirst": 1,
    "weightContinue": 0,
    "needVolumeCal": false,
    "volumeBase": 8000,
    "currentTotal": "6175"     // ✅ 当前总费用
  }
}
```

## 验证步骤

### 1. 重新测试API
```bash
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250}'
```

### 2. 检查关键日志
```bash
tail -f application.log | grep -E "(挂号费|总费用|费用明细)"
```

### 3. 验证计算逻辑

根据阶梯配置：
```json
{"tierStart":200,"tierEnd":450,"unitPrice":13900,"registrationFee":2700}
```

对于250g的包裹：
- 基础运费：13900 × 0.25 = 3475分
- 挂号费：2700分（来自阶梯配置）
- 总费用：3475 + 2700 = 6175分

### 4. 验证不同重量段

测试其他重量段，确保挂号费正确计算：

```bash
# 测试100g（应该使用第2个阶梯）
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":100}'

# 预期：基础运费=1310分，挂号费=2500分，总费用=3810分

# 测试500g（应该使用第4个阶梯）
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":500}'

# 预期：基础运费=6950分，挂号费=5200分，总费用=12150分
```

## 常见问题排查

### Q1: 如果挂号费仍然没有包含在总费用中？
检查：
1. `additionalFee` 字段是否正确设置
2. `calculateTotalFee` 方法是否正确读取 `additionalFee`
3. 是否有其他地方覆盖了总费用

### Q2: 如果金额单位仍然是小数？
检查：
1. 是否还有地方调用 `formatMoney` 方法
2. 数据库中的金额是否确实是分为单位
3. 前端是否正确处理分为单位的金额

### Q3: 如果不同重量段的挂号费不正确？
检查：
1. 阶梯配置中的 `registrationFee` 字段
2. `findMatchingTier` 方法是否正确匹配阶梯
3. 阶梯范围是否有重叠或遗漏

## 总结

修复后的计算流程：
1. ✅ 正确计算基础运费
2. ✅ 正确获取阶梯配置中的挂号费
3. ✅ 将挂号费设置到 `additionalFee` 字段
4. ✅ 在总费用计算中包含挂号费
5. ✅ 所有金额以分为单位返回

这样确保了挂号费能够正确计算并包含在总费用中。
