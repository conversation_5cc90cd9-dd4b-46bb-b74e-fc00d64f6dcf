# 价格类型枚举使用说明

## 概述

为了规范代码，我们将价格类型从字符串常量统一为枚举 `AgentLogisticsPriceTypeEnum`。这样可以避免硬编码字符串，提高代码的可维护性和类型安全性。

## 枚举定义

### AgentLogisticsPriceTypeEnum

```java
public enum AgentLogisticsPriceTypeEnum implements IntArrayValuable {
    /**
     * 首重续重计算
     * 传统的首重+续重递增计算方式
     */
    INCREMENTAL(1, "INCREMENTAL", "首重续重计算"),

    /**
     * 纯阶梯价格计算
     * 按重量段使用不同单价的阶梯计算方式
     */
    TIERED(2, "TIERED", "纯阶梯价格计算"),

    /**
     * 阶梯递增价格计算
     * 先分阶梯，阶梯内使用首重续重递增计算的混合方式
     */
    TIERED_INCREMENTAL(3, "TIERED_INCREMENTAL", "阶梯递增价格计算");
}
```

### 字段说明

- **type**: 类型编码（数字），用于数据库存储和内部处理
- **code**: 类型代码（字符串），用于API传输和配置文件
- **name**: 类型名称（中文），用于界面显示

## 使用方法

### 1. 获取枚举值

```java
// 根据类型编码获取
AgentLogisticsPriceTypeEnum priceType = AgentLogisticsPriceTypeEnum.valueOf(1);

// 根据类型代码获取
AgentLogisticsPriceTypeEnum priceType = AgentLogisticsPriceTypeEnum.valueOfCode("INCREMENTAL");
```

### 2. 类型判断

```java
// 旧方式（已废弃）
if ("TIERED".equals(priceRule.getPriceType())) {
    // 处理阶梯价格
}

// 新方式（推荐）
if (AgentLogisticsPriceTypeEnum.TIERED.getCode().equals(priceRule.getPriceType())) {
    // 处理阶梯价格
}
```

### 3. 类型验证

```java
// 验证类型编码是否有效
boolean isValid = AgentLogisticsPriceTypeEnum.isValid(type);

// 验证类型代码是否有效
boolean isValid = AgentLogisticsPriceTypeEnum.isValidCode(code);
```

### 4. 在VO中使用

```java
@Schema(description = "价格类型", example = "INCREMENTAL", 
        allowableValues = {"INCREMENTAL", "TIERED", "TIERED_INCREMENTAL"})
private String priceType;
```

## 修改的文件列表

### 1. 枚举类
- `AgentLogisticsPriceTypeEnum.java` - 新增枚举定义

### 2. 服务层
- `ShippingQuoteServiceImpl.java` - 运费计算服务
- `LogisticsProductServiceImpl.java` - 物流产品服务

### 3. 控制器层
- `LogisticsProductController.java` - 物流产品控制器

### 4. VO类
- `LogisticsProductPriceImportExcelVO.java` - Excel导入VO
- `LogisticsProductPriceBatchCreateReqVO.java` - 批量创建请求VO

## 计算逻辑优先级

系统会按以下优先级选择计算方式：

1. **阶梯递增价格** (`TIERED_INCREMENTAL`)
   - 条件：`tieredIncrementalPrices` 字段不为空
   - 适用：复杂的阶梯+递增混合计费

2. **纯阶梯价格** (`TIERED`)
   - 条件：`priceType = "TIERED"` 且 `tieredPrices` 字段不为空
   - 适用：按重量段使用不同单价

3. **首重续重价格** (`INCREMENTAL`)
   - 条件：默认方式
   - 适用：传统的首重+续重计算

## 数据库存储

### 当前存储方式
数据库中的 `price_type` 字段存储字符串代码：
- `"INCREMENTAL"` - 首重续重计算
- `"TIERED"` - 纯阶梯价格计算
- `"TIERED_INCREMENTAL"` - 阶梯递增价格计算

### 未来优化建议
可以考虑将数据库字段改为存储数字编码，以节省存储空间：
- `1` - 首重续重计算
- `2` - 纯阶梯价格计算
- `3` - 阶梯递增价格计算

## 迁移指南

### 对于现有代码

1. **替换硬编码字符串**：
   ```java
   // 旧代码
   if ("TIERED".equals(priceType)) { ... }
   
   // 新代码
   if (AgentLogisticsPriceTypeEnum.TIERED.getCode().equals(priceType)) { ... }
   ```

2. **更新验证逻辑**：
   ```java
   // 旧代码
   if (!Arrays.asList("TIERED", "INCREMENTAL").contains(priceType)) { ... }
   
   // 新代码
   if (!AgentLogisticsPriceTypeEnum.isValidCode(priceType)) { ... }
   ```

3. **更新示例数据**：
   ```java
   // 旧代码
   .priceType("INCREMENTAL")
   
   // 新代码
   .priceType(AgentLogisticsPriceTypeEnum.INCREMENTAL.getCode())
   ```

### 对于新功能

1. **使用枚举常量**：避免直接使用字符串
2. **类型安全**：利用枚举的类型安全特性
3. **IDE支持**：享受IDE的自动补全和重构支持

## 测试验证

### 单元测试示例

```java
@Test
public void testPriceTypeEnum() {
    // 测试枚举值获取
    assertEquals(AgentLogisticsPriceTypeEnum.INCREMENTAL, 
                 AgentLogisticsPriceTypeEnum.valueOf(1));
    assertEquals(AgentLogisticsPriceTypeEnum.TIERED, 
                 AgentLogisticsPriceTypeEnum.valueOfCode("TIERED"));
    
    // 测试类型验证
    assertTrue(AgentLogisticsPriceTypeEnum.isValid(1));
    assertTrue(AgentLogisticsPriceTypeEnum.isValidCode("INCREMENTAL"));
    assertFalse(AgentLogisticsPriceTypeEnum.isValidCode("INVALID"));
}
```

### 集成测试

```java
@Test
public void testPriceCalculationWithEnum() {
    LogisticsProductPriceDO priceRule = new LogisticsProductPriceDO();
    priceRule.setPriceType(AgentLogisticsPriceTypeEnum.INCREMENTAL.getCode());
    
    // 测试运费计算逻辑
    // ...
}
```

## 注意事项

1. **向后兼容**：现有数据库数据无需修改，枚举代码与原字符串值保持一致
2. **API兼容**：前端传递的参数格式不变
3. **扩展性**：新增价格类型时，只需在枚举中添加新值
4. **类型安全**：编译时就能发现类型错误，减少运行时异常

## 最佳实践

1. **统一使用枚举**：所有涉及价格类型的地方都使用枚举
2. **避免硬编码**：不要直接使用字符串常量
3. **完善注释**：为每个枚举值添加详细的业务说明
4. **单元测试**：为枚举相关的逻辑编写充分的测试用例

通过使用枚举，我们的代码变得更加规范、安全和易于维护。
