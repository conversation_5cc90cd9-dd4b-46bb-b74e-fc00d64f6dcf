# 运费计算日志分析指南

## 概述

本文档帮助开发人员通过日志快速定位运费计算中的问题，特别是针对"重量250g超过最大限制0g"这类数据配置问题。

## 日志级别说明

### INFO级别 - 正常流程日志
- `=== 开始查询运费报价 ===` - 查询开始
- `查询参数: 国家=US, 重量=250g` - 输入参数
- `查询到X条价格规则` - 数据库查询结果
- `查询到X个产品信息` - 产品信息获取结果
- `=== 运费查询完成 ===` - 查询结束

### WARN级别 - 业务警告日志
- `价格规则数据异常` - 数据配置问题
- `重量检查失败` - 重量限制问题
- `尺寸检查失败` - 尺寸限制问题
- `部分产品未找到或未启用` - 产品状态问题

### ERROR级别 - 系统错误日志
- `运费查询失败` - 系统异常
- `计算运费失败` - 计算过程异常

## 常见问题分析

### 重量限制业务逻辑说明

#### 新的重量限制逻辑
- **最小重量限制**: `null` 或 `≤0` 表示无最小重量限制
- **最大重量限制**: `null` 或 `≤0` 表示无最大重量限制
- **有效限制**: 只有当重量值 `>0` 时才进行限制检查

#### 日志示例
```
重量限制配置: 最小=无限制, 最大=无限制  // max_weight = 0 或 null
重量限制配置: 最小=100g, 最大=无限制    // min_weight = 100, max_weight = 0
重量限制配置: 最小=无限制, 最大=20000g  // min_weight = null, max_weight = 20000
```

### 问题1: "重量限制配置异常"

#### 日志特征
```
价格规则数据异常 (priceRuleId=123): 最大重量限制异常: -100g (不能为负数)
价格规则数据异常 (priceRuleId=456): 重量限制逻辑错误: 最小重量(1000g) > 最大重量(500g)
```

#### 问题原因
1. **负数重量**: 重量限制设置为负数
2. **逻辑错误**: 最小重量大于最大重量
3. **数据导入问题**: Excel导入时数据处理错误

#### 解决方案
```sql
-- 检查负数重量限制
SELECT id, product_id, country_code, min_weight, max_weight
FROM logistics_product_price
WHERE min_weight < 0 OR max_weight < 0;

-- 检查逻辑错误的重量限制
SELECT id, product_id, country_code, min_weight, max_weight
FROM logistics_product_price
WHERE min_weight > 0 AND max_weight > 0 AND min_weight > max_weight;

-- 修复负数重量（设置为无限制）
UPDATE logistics_product_price
SET min_weight = NULL, max_weight = NULL
WHERE min_weight < 0 OR max_weight < 0;
```

### 问题2: "产品未找到或未启用"

#### 日志特征
```
部分产品未找到或未启用: missingIds=[1, 2, 3]
物流产品不存在: productId=1
```

#### 问题原因
1. **产品状态错误**: `logistics_product` 表中 `status` 不为 0（启用状态）
2. **数据不一致**: 价格规则引用了不存在的产品ID
3. **软删除问题**: 产品被软删除但价格规则仍然存在

#### 解决方案
```sql
-- 检查产品状态
SELECT id, name_zh, status 
FROM logistics_product 
WHERE id IN (1, 2, 3);

-- 启用产品
UPDATE logistics_product 
SET status = 0 
WHERE id IN (1, 2, 3);
```

### 问题3: "阶梯价格配置为空"

#### 日志特征
```
价格规则数据异常 (priceRuleId=456): 阶梯价格类型但阶梯价格配置为空
```

#### 问题原因
1. **价格类型配置错误**: `price_type` 为 'TIERED' 但 `tiered_prices` 为空
2. **数据迁移问题**: 从旧系统迁移时数据丢失

#### 解决方案
```sql
-- 检查阶梯价格配置
SELECT id, price_type, tiered_prices, first_price, first_unit
FROM logistics_product_price 
WHERE price_type = 'TIERED' AND (tiered_prices IS NULL OR tiered_prices = '');

-- 修复方案1: 改为首重续重模式
UPDATE logistics_product_price 
SET price_type = 'INCREMENTAL'
WHERE price_type = 'TIERED' AND (tiered_prices IS NULL OR tiered_prices = '');

-- 修复方案2: 补充阶梯价格配置
UPDATE logistics_product_price 
SET tiered_prices = '[{"minWeight":0,"maxWeight":500,"price":1500},{"minWeight":501,"maxWeight":1000,"price":2000}]'
WHERE id = 456;
```

## 日志查看命令

### 实时查看运费计算日志
```bash
# 查看所有运费相关日志
tail -f application.log | grep -E "(运费|shipping|quote)"

# 只看错误和警告
tail -f application.log | grep -E "(ERROR|WARN).*运费"

# 查看特定国家的查询
tail -f application.log | grep "国家=US"
```

### 历史日志分析
```bash
# 查看今天的运费查询统计
grep "开始查询运费报价" application.log | grep "$(date +%Y-%m-%d)" | wc -l

# 查看失败的查询
grep "重量检查失败\|尺寸检查失败\|价格规则数据异常" application.log

# 查看特定时间段的日志
sed -n '/2025-07-21 12:00:00/,/2025-07-21 13:00:00/p' application.log | grep 运费
```

## 数据验证SQL

### 检查价格规则完整性
```sql
-- 检查重量限制异常的规则（基于新的业务逻辑）
SELECT
    id, product_id, country_code,
    min_weight, max_weight,
    CASE
        WHEN min_weight < 0 THEN '最小重量为负数'
        WHEN max_weight < 0 THEN '最大重量为负数'
        WHEN min_weight > 0 AND max_weight > 0 AND min_weight > max_weight THEN '最小重量大于最大重量'
        ELSE '正常'
    END as issue,
    CASE
        WHEN min_weight IS NULL OR min_weight <= 0 THEN '无最小重量限制'
        ELSE CONCAT(min_weight, 'g')
    END as min_limit,
    CASE
        WHEN max_weight IS NULL OR max_weight <= 0 THEN '无最大重量限制'
        ELSE CONCAT(max_weight, 'g')
    END as max_limit
FROM logistics_product_price
WHERE min_weight < 0
   OR max_weight < 0
   OR (min_weight > 0 AND max_weight > 0 AND min_weight > max_weight);
```

### 检查产品状态
```sql
-- 检查被价格规则引用但未启用的产品
SELECT DISTINCT 
    p.id, p.name_zh, p.status,
    COUNT(lpp.id) as price_rule_count
FROM logistics_product p
LEFT JOIN logistics_product_price lpp ON p.id = lpp.product_id
WHERE p.status != 0 AND lpp.id IS NOT NULL
GROUP BY p.id, p.name_zh, p.status;
```

### 检查价格配置
```sql
-- 检查价格配置异常的规则
SELECT 
    id, product_id, price_type,
    first_price, first_unit, tiered_prices,
    CASE 
        WHEN price_type = 'TIERED' AND (tiered_prices IS NULL OR tiered_prices = '') THEN '阶梯价格配置为空'
        WHEN price_type != 'TIERED' AND (first_price IS NULL OR first_price <= 0) THEN '首重价格异常'
        WHEN price_type != 'TIERED' AND (first_unit IS NULL OR first_unit <= 0) THEN '首重重量异常'
        ELSE '正常'
    END as issue
FROM logistics_product_price 
WHERE (price_type = 'TIERED' AND (tiered_prices IS NULL OR tiered_prices = ''))
   OR (price_type != 'TIERED' AND (first_price IS NULL OR first_price <= 0))
   OR (price_type != 'TIERED' AND (first_unit IS NULL OR first_unit <= 0));
```

## 快速修复脚本

### 修复重量限制异常的问题
```sql
-- 备份原数据
CREATE TABLE logistics_product_price_backup AS
SELECT * FROM logistics_product_price
WHERE min_weight < 0 OR max_weight < 0
   OR (min_weight > 0 AND max_weight > 0 AND min_weight > max_weight);

-- 修复负数重量限制（设置为无限制）
UPDATE logistics_product_price
SET min_weight = NULL
WHERE min_weight < 0;

UPDATE logistics_product_price
SET max_weight = NULL
WHERE max_weight < 0;

-- 修复逻辑错误的重量限制（保留最大重量，清除最小重量）
UPDATE logistics_product_price
SET min_weight = NULL
WHERE min_weight > 0 AND max_weight > 0 AND min_weight > max_weight;
```

### 批量启用产品
```sql
-- 启用所有有价格规则的产品
UPDATE logistics_product 
SET status = 0 
WHERE id IN (
    SELECT DISTINCT product_id 
    FROM logistics_product_price 
    WHERE status = 1
) AND status != 0;
```

## 监控建议

### 设置告警
1. **数据异常告警**: 当出现"价格规则数据异常"日志时发送告警
2. **查询失败告警**: 当运费查询失败率超过5%时发送告警
3. **性能告警**: 当查询响应时间超过2秒时发送告警

### 定期检查
1. **每日检查**: 运行数据验证SQL，确保没有新的数据问题
2. **每周检查**: 分析运费查询日志，优化常见问题
3. **每月检查**: 审查价格规则配置，清理无效数据

## 总结

通过详细的日志记录和系统的分析方法，可以快速定位和解决运费计算中的各种问题。关键是：

1. **看日志级别**: ERROR > WARN > INFO
2. **找关键字**: "数据异常"、"检查失败"、"未找到"
3. **查数据库**: 验证配置的正确性
4. **修复数据**: 使用提供的SQL脚本
5. **验证结果**: 重新测试确保问题解决
