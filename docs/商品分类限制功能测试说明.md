# 商品分类限制功能测试说明

## 功能概述

新增了商品分类限制检查功能，根据物流产品的 `categoryRestrictions` 配置，检查用户请求的商品分类是否被禁止运输。

## 业务逻辑

### 检查规则
1. **如果没有提供分类ID**：跳过检查，允许运输
2. **如果产品没有分类限制配置**：跳过检查，允许运输
3. **如果请求的分类ID在任何一个配置项的 `blockList` 中**：禁止运输
4. **`allowList` 暂时不做强制检查**：根据业务需求可以调整

### 配置格式
```json
[
  {
    "id": 25,
    "allowList": [],
    "blockList": [198, 197, 196, 195, 194, 193, 192, 191, 190, 189, 188, 187, 186]
  },
  {
    "id": 21,
    "allowList": [177, 174],
    "blockList": [176, 175, 173, 172, 171, 170, 169, 168, 167, 166]
  },
  {
    "id": 18,
    "allowList": [146, 145, 143, 142],
    "blockList": [144, 141]
  }
]
```

## 测试用例

### 用例1: 无分类限制
```bash
# 请求不包含分类ID
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250}'

# 预期结果: ✅ 通过，跳过分类检查
```

### 用例2: 分类在禁止列表中
```bash
# 请求包含被禁止的分类ID（假设198在blockList中）
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250,"categoryIds":[198]}'

# 预期结果: ❌ 失败，返回不可用
```

### 用例3: 分类不在禁止列表中
```bash
# 请求包含允许的分类ID（假设177在allowList中或不在blockList中）
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250,"categoryIds":[177]}'

# 预期结果: ✅ 通过，继续运费计算
```

### 用例4: 多个分类，其中一个被禁止
```bash
# 请求包含多个分类ID，其中一个被禁止
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250,"categoryIds":[177,198]}'

# 预期结果: ❌ 失败，因为198在blockList中
```

## 预期日志输出

### 成功通过的日志
```
=== 开始分类限制检查 ===
分类限制配置: [{"id":25,"allowList":[],"blockList":[198,197,196]}]
请求的分类ID: [177]
解析到1个分类限制配置
检查分类ID: 177
分类限制检查通过
分类检查通过
```

### 检查失败的日志
```
=== 开始分类限制检查 ===
分类限制配置: [{"id":25,"allowList":[],"blockList":[198,197,196]}]
请求的分类ID: [198]
解析到1个分类限制配置
检查分类ID: 198
分类ID 198 在分类 25 的禁止列表中
分类限制检查失败: 商品分类 [198] 被禁止运输
分类检查失败: 商品分类 [198] 被禁止运输
```

### 跳过检查的日志
```
未提供分类ID，跳过分类限制检查
分类检查通过
```

## 预期API返回结果

### 成功的返回
```json
{
  "code": 0,
  "data": [
    {
      "id": "1",
      "name": "云途全球专线挂号（标快带电）",
      "available": true,
      "restrictions": {
        "categoryRestrictions": [
          {
            "name": "分类25",
            "allowList": [],
            "blockList": ["198", "197", "196"],
            "allowlist": [],
            "blocklist": ["198", "197", "196"]
          }
        ]
      }
    }
  ]
}
```

### 失败的返回
```json
{
  "code": 0,
  "data": [
    {
      "id": "1",
      "name": "云途全球专线挂号（标快带电）",
      "available": false,
      "unavailableReason": "商品分类 [198] 被禁止运输",
      "restrictions": {
        "categoryRestrictions": [
          {
            "name": "分类25",
            "allowList": [],
            "blockList": ["198", "197", "196"],
            "allowlist": [],
            "blocklist": ["198", "197", "196"]
          }
        ]
      }
    }
  ]
}
```

## 数据库测试数据

### 准备测试数据
```sql
-- 更新产品的分类限制配置
UPDATE logistics_product 
SET category_restrictions = '[{"id":25,"allowList":[],"blockList":[198,197,196,195,194,193,192,191,190,189,188,187,186]},{"id":21,"allowList":[177,174],"blockList":[176,175,173,172,171,170,169,168,167,166]}]'
WHERE id = 1;

-- 查看配置
SELECT id, name_zh, category_restrictions 
FROM logistics_product 
WHERE id = 1;
```

## 测试步骤

### 1. 准备数据
确保数据库中有正确的分类限制配置

### 2. 测试无分类请求
```bash
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250}'
```
预期：成功返回运费方案

### 3. 测试被禁止的分类
```bash
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250,"categoryIds":[198]}'
```
预期：返回不可用，原因为分类被禁止

### 4. 测试允许的分类
```bash
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250,"categoryIds":[177]}'
```
预期：成功返回运费方案

### 5. 查看详细日志
```bash
tail -f application.log | grep -E "(分类限制|分类检查|CategoryRestriction)"
```

## 注意事项

1. **性能考虑**：分类检查在重量和尺寸检查之后进行，避免不必要的计算
2. **异常处理**：配置格式错误时会记录日志并返回检查失败
3. **兼容性**：同时提供 `allowList/blockList` 和 `allowlist/blocklist` 字段
4. **扩展性**：可以根据业务需求调整 `allowList` 的检查逻辑

## 常见问题

### Q1: 如果分类限制配置格式错误怎么办？
A: 系统会记录错误日志并返回检查失败，确保安全性

### Q2: allowList 的作用是什么？
A: 目前只做展示，不做强制检查。可以根据业务需求调整为强制检查

### Q3: 如何添加新的分类限制？
A: 更新数据库中对应产品的 `category_restrictions` 字段即可

### Q4: 分类检查的性能如何？
A: 检查在内存中进行，性能很好。如果分类限制配置很大，可以考虑缓存优化
