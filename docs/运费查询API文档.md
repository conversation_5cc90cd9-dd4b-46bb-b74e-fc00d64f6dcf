# 运费查询API文档

## 接口概述

运费查询接口用于前端用户查询不同物流方案的运费报价，支持按国家、重量、商品分类、包裹尺寸等条件进行查询。

## 接口信息

- **接口地址**: `POST /app-api/agent/shipping-calculation/quote`
- **接口描述**: 查询运费报价
- **权限要求**: 无需特殊权限（用户端接口）

## 请求参数

### 请求体 (JSON)

```json
{
  "countryCode": "US",           // 必填：目标国家编码，如 US、GB、DE 等
  "weight": 250,                 // 必填：重量(克)，必须大于0
  "categoryIds": [1, 2, 3],      // 可选：商品分类ID列表，对应AgentCategoryDO的ID
  "length": 30,                  // 可选：长度(cm)
  "width": 20,                   // 可选：宽度(cm)  
  "height": 5,                   // 可选：高度(cm)
  "postalCode": "10001",         // 可选：邮编
  "stateProvince": "New York",   // 可选：州/省
  "city": "New York"             // 可选：城市
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| countryCode | String | 是 | 目标国家编码，如US、GB、DE等 |
| weight | Integer | 是 | 重量(克)，必须大于0 |
| categoryIds | List<Long> | 否 | 商品分类ID列表，用于检查分类限制 |
| length | BigDecimal | 否 | 长度(cm)，如果不填则不考虑体积 |
| width | BigDecimal | 否 | 宽度(cm) |
| height | BigDecimal | 否 | 高度(cm) |
| postalCode | String | 否 | 邮编，用于偏远地区判断 |
| stateProvince | String | 否 | 州/省 |
| city | String | 否 | 城市 |

## 响应结果

### 成功响应

```json
{
  "code": 0,
  "data": [
    {
      "id": "1920529007397355522",
      "name": "美国专线小包p",
      "iconUrl": "https://img1.cnfans.com/xxx.png",
      "features": "1、该线路为三角运输，商业清关，运输时间多3-5天；\n2、无退件和重派服务...",
      "transitTime": "12-20",
      "taxInclude": true,
      "lineTips": [],
      "feeDetail": {
        "weight": 250,
        "length": 30,
        "width": 20,
        "height": 5,
        "currency": "USD",
        "volumeWeight": 19,
        "chargeableWeight": 250,
        "total": "25.23",
        "freight": "21.27",
        "customsFee": "0.00",
        "fuelFee": "0.00",
        "airSurcharge": "0.00",
        "operationFee": "2.37",
        "serviceFee": "1.58",
        "feeFirst": "16.62",
        "feeContinue": "4.65",
        "additionalFee": null,
        "weightFirst": 500,
        "weightContinue": 200,
        "needVolumeCal": false,
        "volumeBase": 8000,
        "discount": null,
        "originalTotal": null,
        "currentTotal": "25.23"
      },
      "restrictions": {
        "minWeight": 0,
        "maxWeight": 20000,
        "dimensionRestriction": "请参考具体产品限制",
        "volumeWeightRule": "包裹将会被计算体积重，体积重(kg)=长*宽*高(cm)/8000。实重和体积重两者取高者计费。",
        "categoryRestrictions": []
      },
      "available": true,
      "unavailableReason": null,
      "sort": 15,
      "minDeclareValue": 500,
      "maxDeclareValue": 12000,
      "defaultDeclareType": "Weight",
      "declarePerKg": 1200,
      "declareRatio": "0.2",
      "iossEnabled": false,
      "freeInsure": false,
      "addressMaxLength": null,
      "tariffRate": 0,
      "prepayTariff": false,
      "logisticsTimeliness": {
        "deliveryRate": 98.1,
        "timelinessInfos": [
          {
            "timeInterval": "8-12",
            "rate": "2.2"
          },
          {
            "timeInterval": "13-16", 
            "rate": "84.6"
          },
          {
            "timeInterval": "17-20",
            "rate": "10.3"
          },
          {
            "timeInterval": ">20",
            "rate": "2.9"
          }
        ]
      }
    }
  ],
  "msg": "操作成功"
}
```

### 响应字段说明

#### 主要字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 物流产品ID |
| name | String | 物流产品名称 |
| iconUrl | String | 图标URL |
| features | String | 产品特色描述 |
| transitTime | String | 运输时效，如"12-20" |
| taxInclude | Boolean | 是否包税 |
| available | Boolean | 是否可用 |
| unavailableReason | String | 不可用原因 |
| sort | Integer | 排序值 |

#### 费用详情 (feeDetail)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| weight | Integer | 实际重量(g) |
| volumeWeight | Integer | 体积重(g) |
| chargeableWeight | Integer | 计费重量(g) |
| currency | String | 货币单位，默认USD |
| total | String | 总费用 |
| freight | String | 基础运费 |
| operationFee | String | 操作费 |
| serviceFee | String | 服务费 |
| customsFee | String | 清关费 |
| fuelFee | String | 燃油费 |
| needVolumeCal | Boolean | 是否需要体积计算 |
| volumeBase | Integer | 体积重基数，默认8000 |

#### 限制信息 (restrictions)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| minWeight | Integer | 最小重量(g) |
| maxWeight | Integer | 最大重量(g) |
| dimensionRestriction | String | 尺寸限制描述 |
| volumeWeightRule | String | 体积重规则描述 |
| categoryRestrictions | Array | 分类限制列表(暂时为空) |

#### 申报相关

| 字段名 | 类型 | 说明 |
|--------|------|------|
| minDeclareValue | Integer | 最小申报价值(分) |
| maxDeclareValue | Integer | 最大申报价值(分) |
| defaultDeclareType | String | 默认申报类型 |
| declarePerKg | Integer | 每公斤申报价值(分) |
| declareRatio | String | 申报比例 |

#### 时效信息 (logisticsTimeliness)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| deliveryRate | BigDecimal | 妥投率 |
| timelinessInfos | Array | 时效分布信息 |

## 使用示例

### 基础查询（仅国家和重量）

```bash
curl -X POST "http://localhost:8080/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{
    "countryCode": "US",
    "weight": 250
  }'
```

### 完整查询（包含尺寸和分类）

```bash
curl -X POST "http://localhost:8080/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{
    "countryCode": "US",
    "weight": 250,
    "categoryIds": [1, 2],
    "length": 30,
    "width": 20,
    "height": 5,
    "postalCode": "10001"
  }'
```

### JavaScript 调用示例

```javascript
// 基础查询
const response = await fetch('/app-api/agent/shipping-calculation/quote', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    countryCode: 'US',
    weight: 250
  })
});

const result = await response.json();
console.log('运费方案:', result.data);

// 完整查询
const fullResponse = await fetch('/app-api/agent/shipping-calculation/quote', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    countryCode: 'US',
    weight: 250,
    categoryIds: [1, 2],
    length: 30,
    width: 20,
    height: 5,
    postalCode: '10001'
  })
});

const fullResult = await fullResponse.json();
console.log('完整运费方案:', fullResult.data);
```

## 注意事项

1. **重量单位**: 请求参数中的重量单位为克(g)
2. **尺寸单位**: 长宽高单位为厘米(cm)
3. **金额单位**: 响应中的金额单位为美元(USD)，已转换为小数格式
4. **体积重计算**: 当提供长宽高时，会自动计算体积重并与实重比较取大值
5. **排序**: 返回结果按照产品的sort字段升序排列
6. **可用性**: available字段表示该方案是否可用，不可用时会在unavailableReason中说明原因
7. **分类限制**: categoryRestrictions字段暂时返回空数组，后续会根据商品分类进行限制检查

## 错误处理

- 当没有找到可用的物流方案时，返回空数组
- 当参数验证失败时，返回相应的错误信息
- 系统异常时会记录日志并返回空数组，确保接口稳定性
