# 价格规则时间条件说明

## 功能概述

在运费查询时，系统现在会检查价格规则的生效时间和失效时间，确保只返回当前时间有效的价格规则。

## 时间条件逻辑

### 生效时间 (effective_time)
- **未设置生效时间** (`NULL`)：价格规则立即生效
- **设置了生效时间**：当前时间必须 >= 生效时间，价格规则才生效

### 失效时间 (expire_time)
- **未设置失效时间** (`NULL`)：价格规则永不失效
- **设置了失效时间**：当前时间必须 < 失效时间，价格规则才有效

### 组合逻辑

价格规则有效的条件：
```
(effective_time IS NULL OR current_time >= effective_time) 
AND 
(expire_time IS NULL OR current_time < expire_time)
```

## 实现细节

### SQL查询条件

```sql
SELECT * FROM logistics_product_price 
WHERE country_code = ? 
  AND status = 1 
  AND (effective_time IS NULL OR effective_time <= NOW()) 
  AND (expire_time IS NULL OR expire_time > NOW())
```

### 代码实现

```java
LocalDateTime now = LocalDateTime.now();

List<LogisticsProductPriceDO> prices = logisticsProductPriceMapper.selectList(
    new LambdaQueryWrapperX<LogisticsProductPriceDO>()
        .eq(LogisticsProductPriceDO::getCountryCode, countryCode)
        .eq(LogisticsProductPriceDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
        // 生效时间检查
        .and(wrapper -> wrapper
            .isNull(LogisticsProductPriceDO::getEffectiveTime)
            .or()
            .le(LogisticsProductPriceDO::getEffectiveTime, now)
        )
        // 失效时间检查
        .and(wrapper -> wrapper
            .isNull(LogisticsProductPriceDO::getExpireTime)
            .or()
            .gt(LogisticsProductPriceDO::getExpireTime, now)
        )
);
```

## 使用场景

### 场景1: 促销价格
```sql
-- 设置双11促销价格，仅在特定时间段有效
INSERT INTO logistics_product_price (
    product_id, country_code, price_type,
    first_unit, first_price,
    effective_time, expire_time,
    status
) VALUES (
    1, 'US', 'INCREMENTAL',
    500, 8000,  -- 促销价格
    '2024-11-11 00:00:00',  -- 双11开始
    '2024-11-12 00:00:00',  -- 双11结束
    1
);
```

### 场景2: 新价格预设
```sql
-- 设置下月生效的新价格
INSERT INTO logistics_product_price (
    product_id, country_code, price_type,
    first_unit, first_price,
    effective_time,  -- 下月1号生效
    status
) VALUES (
    1, 'US', 'INCREMENTAL',
    500, 12000,  -- 新价格
    '2024-12-01 00:00:00',
    1
);
```

### 场景3: 临时停用
```sql
-- 临时停用某个价格规则
UPDATE logistics_product_price 
SET expire_time = NOW()  -- 立即失效
WHERE id = 123;
```

## 测试用例

### 测试数据准备

```sql
-- 清理测试数据
DELETE FROM logistics_product_price WHERE country_code = 'TEST';

-- 插入测试数据
INSERT INTO logistics_product_price (
    product_id, country_code, price_type,
    first_unit, first_price,
    effective_time, expire_time,
    status, create_time, update_time
) VALUES 
-- 1. 未来生效的价格（应该被过滤）
(1, 'TEST', 'INCREMENTAL', 500, 10000, 
 DATE_ADD(NOW(), INTERVAL 1 DAY), NULL, 1, NOW(), NOW()),

-- 2. 已过期的价格（应该被过滤）
(1, 'TEST', 'INCREMENTAL', 500, 8000, 
 DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 1, NOW(), NOW()),

-- 3. 当前有效的价格（应该被返回）
(1, 'TEST', 'INCREMENTAL', 500, 12000, 
 DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 1 DAY), 1, NOW(), NOW()),

-- 4. 无时间限制的价格（应该被返回）
(1, 'TEST', 'INCREMENTAL', 500, 15000, 
 NULL, NULL, 1, NOW(), NOW());
```

### 测试API调用

```bash
# 测试运费查询，应该只返回有效的价格规则
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"TEST","weight":1000}'
```

### 预期结果

- 只返回当前时间有效的价格规则
- 未来生效和已过期的价格规则被过滤掉
- 日志中显示过滤后的价格规则数量

## 日志输出

### 正常情况
```
查询国家US的价格规则
当前时间: 2024-11-15T10:30:00
查询SQL: SELECT * FROM logistics_product_price WHERE country_code = 'US' AND status = 1 AND (effective_time IS NULL OR effective_time <= '2024-11-15T10:30:00') AND (expire_time IS NULL OR expire_time > '2024-11-15T10:30:00')
查询结果: 找到2条有效价格规则
```

### 调试模式
```
价格规则ID=123, 产品ID=1, 生效时间=2024-11-01T00:00:00, 失效时间=null
价格规则ID=124, 产品ID=2, 生效时间=null, 失效时间=2024-12-31T23:59:59
```

## 管理建议

### 1. 价格规则管理
- **提前设置**：新价格可以提前配置，设置未来的生效时间
- **平滑切换**：通过时间控制实现价格的平滑切换
- **临时调整**：通过设置失效时间快速停用价格规则

### 2. 监控告警
- 监控即将失效的价格规则
- 确保每个国家至少有一个有效的价格规则
- 定期清理过期的价格规则

### 3. 数据维护
```sql
-- 查询即将在7天内失效的价格规则
SELECT id, product_id, country_code, expire_time
FROM logistics_product_price 
WHERE expire_time IS NOT NULL 
  AND expire_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)
  AND status = 1;

-- 查询没有有效价格规则的国家
SELECT DISTINCT country_code 
FROM logistics_product_price p1
WHERE NOT EXISTS (
    SELECT 1 FROM logistics_product_price p2 
    WHERE p2.country_code = p1.country_code
      AND p2.status = 1
      AND (p2.effective_time IS NULL OR p2.effective_time <= NOW())
      AND (p2.expire_time IS NULL OR p2.expire_time > NOW())
);
```

## 注意事项

1. **时区问题**：确保服务器时间和数据库时间一致
2. **性能影响**：时间条件查询可能影响性能，建议在时间字段上建立索引
3. **数据一致性**：避免同一产品和国家有重叠的有效时间段
4. **缓存策略**：考虑缓存有效的价格规则，定期刷新

## 索引建议

```sql
-- 为时间字段创建索引以提高查询性能
CREATE INDEX idx_logistics_product_price_time 
ON logistics_product_price (country_code, status, effective_time, expire_time);
```

通过这些时间条件的控制，系统可以更灵活地管理价格规则的生效周期，支持促销活动、价格调整等业务需求。
