# 运费计算集成到订单价格计算说明

## 功能概述

将独立的运费计算功能集成到代购订单的价格计算流程中，实现订单级别的运费计算和分摊。

## 集成架构

### 原有架构
```
订单价格计算 → 商品价格计算 → 优惠计算 → 总价计算
```

### 新架构
```
订单价格计算 → 商品价格计算 → 优惠计算 → 运费计算 → 运费分摊 → 总价计算
                                                ↓
                                        运输方案选择
```

## 核心流程

### 1. 计算入口
在 `AgentDeliveryPriceCalculator.calculate()` 方法中集成运费计算：

```java
@Override
public void calculate(AgentPriceCalculateReqBO param, AgentPriceCalculateRespBO result) {
    // 1. 验证计算条件
    // 2. 获取收件地址
    // 3. 检查包邮条件
    // 4. 计算包裹信息
    // 5. 查询运费方案
    // 6. 设置运输方案
    // 7. 计算并分摊运费
}
```

### 2. 包裹信息计算
```java
private PackageInfo calculatePackageInfo(List<Item> items) {
    // 计算总重量（包含10%包装重量）
    // 计算总体积
    // 设置最小重量（100g）
}
```

### 3. 运费方案查询
```java
private List<AppShippingQuoteRespVO> queryShippingQuotes(...) {
    // 构建运费查询请求
    // 设置目的地国家
    // 设置包裹重量和尺寸
    // 设置商品分类
    // 调用运费查询服务
}
```

### 4. 运费分摊策略
```java
private void allocateShippingFeeToItems(...) {
    // 优先按重量分摊
    // 其次按商品价值分摊
    // 最后平均分摊
}
```

## 数据流转

### 输入数据
```java
AgentPriceCalculateReqBO {
    Long userId;           // 用户ID
    Long addressId;        // 收货地址ID
    List<Item> items;      // 商品列表
}
```

### 中间数据
```java
PackageInfo {
    Integer weight;        // 总重量(g)
    Integer volume;        // 总体积(cm³)
    Integer length/width/height; // 尺寸(cm)
}
```

### 输出数据
```java
AgentPriceCalculateRespBO {
    Price price;                    // 价格信息（包含运费）
    List<TransportPlan> transportPlans; // 运输方案列表
    List<Item> items;              // 商品列表（包含分摊的运费）
}
```

## 关键特性

### 1. 包邮逻辑
- **全局包邮**：系统配置的包邮条件
- **活动包邮**：营销活动的包邮条件
- **满额包邮**：订单金额达到包邮门槛

### 2. 地址处理
- 支持多种地址格式
- 国家代码自动识别
- 国家名称到代码的映射

### 3. 分类限制
- 从商品信息中提取分类ID
- 支持分类限制检查
- 自动过滤不可运输的商品分类

### 4. 运费分摊
- **按重量分摊**：根据商品重量比例分摊运费
- **按价值分摊**：根据商品价值比例分摊运费
- **平均分摊**：无重量和价值信息时平均分摊

### 5. 方案选择
- 自动过滤不可用方案
- 优选价格最低的方案
- 支持多种选择策略

## 配置说明

### 包装重量系数
```java
// 在计算包裹重量时增加10%的包装重量
totalWeight += (int) (item.getWeight() * item.getCount() * 1.1);
```

### 最小重量设置
```java
// 设置最小重量为100g
packageInfo.setWeight(Math.max(totalWeight, 100));
```

### 国家代码映射
```java
private String mapCountryNameToCode(String countryName) {
    switch (countryName) {
        case "美国": case "United States": return "US";
        case "英国": case "United Kingdom": return "GB";
        case "加拿大": case "Canada": return "CA";
        // ... 更多映射
    }
}
```

## 测试用例

### 测试用例1：正常运费计算
```java
AgentPriceCalculateReqBO request = new AgentPriceCalculateReqBO();
request.setUserId(123L);
request.setAddressId(456L);
request.setItems(Arrays.asList(
    new Item().setSkuId(1L).setWeight(500).setCount(2),
    new Item().setSkuId(2L).setWeight(300).setCount(1)
));

// 预期结果：
// - 总重量：(500*2 + 300*1) * 1.1 = 1430g
// - 查询到运费方案
// - 运费按重量分摊到商品
```

### 测试用例2：包邮条件
```java
// 设置全局包邮或满额包邮
AgentConfigDO config = new AgentConfigDO();
config.setDeliveryExpressFreeEnabled(true);

// 预期结果：
// - 跳过运费计算
// - 运费为0
```

### 测试用例3：无可用运费方案
```java
// 设置无法运输的目的地或商品分类
request.setAddressId(invalidAddressId);

// 预期结果：
// - 运费计算失败
// - 不影响订单其他计算
```

## 错误处理

### 1. 地址信息缺失
```java
if (address == null) {
    log.warn("无法获取收件地址，跳过运费计算");
    return;
}
```

### 2. 运费查询失败
```java
try {
    return shippingQuoteService.getAppShippingQuotes(shippingReqVO);
} catch (Exception e) {
    log.error("查询运费方案失败", e);
    return new ArrayList<>();
}
```

### 3. 分摊计算异常
```java
// 使用最后一个商品承担剩余运费，避免精度问题
if (i == items.size() - 1) {
    itemShippingFee = totalShippingFee - allocatedFee;
}
```

## 性能优化

### 1. 缓存策略
- 运费方案可以按国家和重量范围缓存
- 地址信息可以缓存用户常用地址

### 2. 异步处理
- 运费计算可以异步进行
- 不阻塞订单主流程

### 3. 降级策略
- 运费计算失败时使用默认运费
- 保证订单流程的可用性

## 监控指标

### 1. 计算成功率
- 运费计算成功/失败次数
- 各个步骤的耗时统计

### 2. 方案覆盖率
- 有可用运费方案的订单比例
- 各国家的方案覆盖情况

### 3. 分摊准确性
- 运费分摊的精度损失
- 分摊策略的使用分布

## 扩展建议

### 1. 智能方案选择
- 根据用户偏好选择运费方案
- 考虑时效、价格、可靠性等因素

### 2. 动态包装
- 根据商品特性动态计算包装重量
- 支持不同包装方式的重量系数

### 3. 多仓库支持
- 支持从不同仓库发货
- 计算最优的仓库分配和运费

### 4. 实时运费
- 集成物流商的实时运费API
- 提供更准确的运费计算

通过这个集成，代购订单系统现在具备了完整的运费计算能力，能够为用户提供准确的运费信息和多样的运输选择。
