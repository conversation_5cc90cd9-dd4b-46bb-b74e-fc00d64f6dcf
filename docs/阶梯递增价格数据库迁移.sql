-- 阶梯递增价格功能数据库迁移脚本

-- 1. 添加阶梯递增价格配置字段
ALTER TABLE logistics_product_price 
ADD COLUMN tiered_incremental_prices TEXT COMMENT '阶梯递增价格配置(JSON格式)';

-- 2. 创建测试数据 - GH国家
INSERT INTO logistics_product_price (
    product_id, country_code, 
    tiered_incremental_prices,
    status, create_time, update_time
) VALUES (
    1, 'GH',
    '[{"tierStart":1,"tierEnd":15000,"firstWeight":100,"firstPrice":14210,"additionalWeight":100,"additionalPrice":2610,"registrationFee":0},{"tierStart":15001,"tierEnd":30000,"firstWeight":100,"firstPrice":14210,"additionalWeight":100,"additionalPrice":2610,"registrationFee":0}]',
    1, NOW(), NOW()
);

-- 3. 创建测试数据 - <PERSON><PERSON>国家
INSERT INTO logistics_product_price (
    product_id, country_code, 
    tiered_incremental_prices,
    status, create_time, update_time
) VALUES (
    1, 'KE',
    '[{"tierStart":1,"tierEnd":15000,"firstWeight":100,"firstPrice":10510,"additionalWeight":100,"additionalPrice":1810,"registrationFee":0},{"tierStart":15001,"tierEnd":30000,"firstWeight":100,"firstPrice":10510,"additionalWeight":100,"additionalPrice":1810,"registrationFee":0}]',
    1, NOW(), NOW()
);

-- 4. 查看测试数据
SELECT 
    id, product_id, country_code, 
    tiered_incremental_prices,
    status, create_time
FROM logistics_product_price 
WHERE country_code IN ('GH', 'KE')
ORDER BY country_code, id;

-- 5. 验证JSON格式
SELECT 
    id, country_code,
    JSON_VALID(tiered_incremental_prices) as is_valid_json,
    JSON_LENGTH(tiered_incremental_prices) as tier_count
FROM logistics_product_price 
WHERE tiered_incremental_prices IS NOT NULL;

-- 6. 数据转换示例 - 将现有的首重续重数据转换为阶梯递增格式
-- 注意：这只是示例，实际使用时需要根据具体业务需求调整

-- 示例：将单一的首重续重配置转换为阶梯递增配置
UPDATE logistics_product_price 
SET tiered_incremental_prices = CONCAT(
    '[{"tierStart":1,"tierEnd":30000,',
    '"firstWeight":', IFNULL(first_unit, 500), ',',
    '"firstPrice":', IFNULL(first_price, 0), ',',
    '"additionalWeight":', IFNULL(additional_unit, 500), ',',
    '"additionalPrice":', IFNULL(additional_price, 0), ',',
    '"registrationFee":', IFNULL(registration_fee, 0), '}]'
)
WHERE country_code = 'TEST' 
  AND first_unit IS NOT NULL 
  AND first_price IS NOT NULL
  AND tiered_incremental_prices IS NULL;

-- 7. 清理测试数据（如果需要）
-- DELETE FROM logistics_product_price WHERE country_code IN ('GH', 'KE', 'TEST');

-- 8. 索引优化（可选）
-- 如果查询频繁，可以考虑添加索引
-- ALTER TABLE logistics_product_price ADD INDEX idx_country_tiered_incremental (country_code, tiered_incremental_prices(100));

-- 9. 数据完整性检查
-- 检查是否有配置了阶梯递增价格但JSON格式错误的记录
SELECT 
    id, country_code, product_id,
    tiered_incremental_prices
FROM logistics_product_price 
WHERE tiered_incremental_prices IS NOT NULL 
  AND JSON_VALID(tiered_incremental_prices) = 0;

-- 10. 统计信息
SELECT 
    '总记录数' as type, COUNT(*) as count
FROM logistics_product_price
UNION ALL
SELECT 
    '有阶梯递增配置' as type, COUNT(*) as count
FROM logistics_product_price 
WHERE tiered_incremental_prices IS NOT NULL
UNION ALL
SELECT 
    '有纯阶梯配置' as type, COUNT(*) as count
FROM logistics_product_price 
WHERE price_type = 'TIERED' AND tiered_prices IS NOT NULL
UNION ALL
SELECT 
    '有首重续重配置' as type, COUNT(*) as count
FROM logistics_product_price 
WHERE first_unit IS NOT NULL AND first_price IS NOT NULL;
