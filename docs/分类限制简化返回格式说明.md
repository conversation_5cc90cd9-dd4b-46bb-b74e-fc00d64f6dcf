# 分类限制简化返回格式说明

## 修改概述

根据需求，分类限制信息现在直接返回数据库中的原始JSON格式，不进行任何转换。前端可以直接解析并转换为相应的分类名称。

## 返回格式

### 直接返回原始JSON

在 `restrictions` 对象中的 `categoryRestrictions` 字段直接返回数据库中的原始JSON字符串：

```json
{
  "restrictions": {
    "minWeight": 0,
    "maxWeight": 0,
    "dimensionRestriction": "请参考具体产品限制",
    "volumeWeightRule": "2",
    "categoryRestrictions": "[{\"id\":25,\"allowList\":[],\"blockList\":[198,197,196,195,194,193,192,191,190,189,188,187,186]},{\"id\":21,\"allowList\":[177,174],\"blockList\":[176,175,173,172,171,170,169,168,167,166]}]"
  }
}
```

### 原始JSON格式

`categoryRestrictions` 字段包含的原始JSON格式如下：

```json
[
  {
    "id": 25,
    "allowList": [],
    "blockList": [198, 197, 196, 195, 194, 193, 192, 191, 190, 189, 188, 187, 186]
  },
  {
    "id": 21,
    "allowList": [177, 174],
    "blockList": [176, 175, 173, 172, 171, 170, 169, 168, 167, 166]
  },
  {
    "id": 18,
    "allowList": [146, 145, 143, 142],
    "blockList": [144, 141]
  }
]
```

## 前端使用方式

### 1. 解析原始JSON

```javascript
// 获取分类限制配置
const categoryRestrictions = response.data[0].restrictions.categoryRestrictions;

if (categoryRestrictions) {
  // 解析JSON字符串
  const restrictions = JSON.parse(categoryRestrictions);
  
  // 遍历处理
  restrictions.forEach(restriction => {
    console.log('分类ID:', restriction.id);
    console.log('允许列表:', restriction.allowList);
    console.log('禁止列表:', restriction.blockList);
  });
}
```

### 2. 转换为显示名称

```javascript
// 假设有分类名称映射
const categoryNameMap = {
  25: '服饰',
  21: '电子产品', 
  18: '食品',
  // ... 更多分类映射
};

const subCategoryNameMap = {
  198: '奢侈品牌服饰',
  197: '国际品牌服饰',
  177: '普通电子产品',
  174: '数码配件',
  // ... 更多子分类映射
};

// 转换为显示格式
function formatCategoryRestrictions(categoryRestrictionsJson) {
  if (!categoryRestrictionsJson) return [];
  
  const restrictions = JSON.parse(categoryRestrictionsJson);
  
  return restrictions.map(restriction => ({
    categoryId: restriction.id,
    categoryName: categoryNameMap[restriction.id] || `分类${restriction.id}`,
    allowList: restriction.allowList.map(id => ({
      id: id,
      name: subCategoryNameMap[id] || `子分类${id}`
    })),
    blockList: restriction.blockList.map(id => ({
      id: id,
      name: subCategoryNameMap[id] || `子分类${id}`
    }))
  }));
}

// 使用示例
const formattedRestrictions = formatCategoryRestrictions(categoryRestrictions);
console.log('格式化后的分类限制:', formattedRestrictions);
```

### 3. React组件示例

```jsx
import React from 'react';

const CategoryRestrictions = ({ categoryRestrictions }) => {
  // 解析分类限制
  const parseRestrictions = (restrictionsJson) => {
    if (!restrictionsJson) return [];
    
    try {
      return JSON.parse(restrictionsJson);
    } catch (error) {
      console.error('解析分类限制失败:', error);
      return [];
    }
  };

  const restrictions = parseRestrictions(categoryRestrictions);

  if (restrictions.length === 0) {
    return <div className="no-restrictions">无分类限制</div>;
  }

  return (
    <div className="category-restrictions">
      <h4>分类限制</h4>
      {restrictions.map(restriction => (
        <div key={restriction.id} className="restriction-item">
          <h5>分类 {restriction.id}</h5>
          
          {restriction.allowList.length > 0 && (
            <div className="allow-list">
              <span className="label">允许：</span>
              {restriction.allowList.map(id => (
                <span key={id} className="tag allow">
                  子分类{id}
                </span>
              ))}
            </div>
          )}
          
          {restriction.blockList.length > 0 && (
            <div className="block-list">
              <span className="label">禁止：</span>
              {restriction.blockList.map(id => (
                <span key={id} className="tag block">
                  子分类{id}
                </span>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default CategoryRestrictions;
```

## 测试验证

### 测试API返回

```bash
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250,"categoryIds":[198]}'
```

### 预期返回

```json
{
  "code": 0,
  "data": [
    {
      "restrictions": {
        "categoryRestrictions": "[{\"id\":25,\"allowList\":[],\"blockList\":[198,197,196]}]"
      },
      "available": false,
      "unavailableReason": "商品分类 [198] 被禁止运输"
    }
  ]
}
```

### 成功的返回（分类不被禁止）

```bash
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250,"categoryIds":[177]}'
```

```json
{
  "code": 0,
  "data": [
    {
      "restrictions": {
        "categoryRestrictions": "[{\"id\":25,\"allowList\":[],\"blockList\":[198,197,196]},{\"id\":21,\"allowList\":[177,174],\"blockList\":[176,175]}]"
      },
      "available": true,
      "feeDetail": {
        "total": "6175"
      }
    }
  ]
}
```

## 数据库配置示例

### 更新产品的分类限制配置

```sql
-- 设置分类限制配置
UPDATE logistics_product 
SET category_restrictions = '[{"id":25,"allowList":[],"blockList":[198,197,196,195,194,193,192,191,190,189,188,187,186]},{"id":21,"allowList":[177,174],"blockList":[176,175,173,172,171,170,169,168,167,166]}]'
WHERE id = 1;

-- 查看配置
SELECT id, name_zh, category_restrictions 
FROM logistics_product 
WHERE id = 1;
```

## 优势

### 1. 简单直接
- 后端不需要进行任何转换处理
- 直接返回数据库原始数据
- 减少代码复杂度

### 2. 性能优化
- 无需额外的数据处理
- 减少内存使用
- 提高响应速度

### 3. 灵活性
- 前端可以根据需要自由处理数据
- 支持多语言显示
- 便于扩展新的字段

### 4. 数据一致性
- 直接使用数据库数据，避免转换错误
- 保证数据的准确性

## 注意事项

1. **JSON格式验证**：后端会验证JSON格式的正确性
2. **空值处理**：如果没有分类限制配置，字段值为null
3. **前端解析**：前端需要做好JSON解析的异常处理
4. **数据类型**：原始JSON中的ID都是数字类型

## 字段说明

- **id**: 分类ID（数字类型）
- **allowList**: 允许的子分类ID数组（数字类型）
- **blockList**: 禁止的子分类ID数组（数字类型）

前端在解析时需要注意数据类型的正确处理。
