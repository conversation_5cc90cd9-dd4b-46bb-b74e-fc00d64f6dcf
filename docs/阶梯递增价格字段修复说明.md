# 阶梯递增价格字段修复说明

## 问题分析

从后端日志可以看出，前端传递的参数中包含了 `tieredIncrementalPrices` 字段：

```json
{
  "tieredIncrementalPrices": "[{\"tierStart\":1,\"tierEnd\":2000,\"firstWeight\":500,\"firstPrice\":3150,\"additionalWeight\":500,\"additionalPrice\":550,\"registrationFee\":0},{\"tierStart\":2000,\"tierEnd\":10000,\"firstWeight\":500,\"firstPrice\":3150,\"additionalWeight\":500,\"additionalPrice\":650,\"registrationFee\":0}]"
}
```

但是在INSERT SQL中没有包含这个字段：

```sql
INSERT INTO agent_logistics_product_price (
  product_id, country_code, timeliness_info, charge_type, price_type, 
  first_unit, first_price, additional_unit, additional_price, 
  min_weight, max_weight, fuel_fee_rate, registration_fee, 
  operation_fee, service_fee, customs_fee, prepay_tariff, 
  tariff_rate, sort, status, create_time, update_time, 
  creator, updater, tenant_id
) VALUES (...)
```

**注意**：SQL中缺少了 `tiered_incremental_prices` 字段！

## 根本原因

通过数据库查询确认，`agent_logistics_product_price` 表中确实存在 `tiered_incremental_prices` 字段：

```
| tiered_incremental_prices | text         | YES  |     | NULL              |                   |
```

问题在于：**Controller的VO类中缺少 `tieredIncrementalPrices` 字段**

## 修复方案

### 1. 修复的文件

在 `LogisticsProductPriceBatchCreateReqVO.java` 中添加缺失的字段：

```java
@Schema(description = "阶梯递增价格配置JSON", 
        example = "[{\"tierStart\":1,\"tierEnd\":15000,\"firstWeight\":100,\"firstPrice\":14210,\"additionalWeight\":100,\"additionalPrice\":2610,\"registrationFee\":0}]")
private String tieredIncrementalPrices;
```

### 2. 数据流程

1. **前端** → 发送包含 `tieredIncrementalPrices` 的JSON
2. **Controller** → 接收到 `LogisticsProductPriceBatchCreateReqVO`
3. **Service** → 通过 `BeanUtils.copyProperties()` 转换为 `LogisticsProductPriceDO`
4. **MyBatis-Plus** → 自动生成INSERT SQL

### 3. 修复前后对比

**修复前**：
- VO中没有 `tieredIncrementalPrices` 字段
- `BeanUtils.copyProperties()` 无法复制该字段
- 数据库中该字段为 `NULL`

**修复后**：
- VO中包含 `tieredIncrementalPrices` 字段
- `BeanUtils.copyProperties()` 正确复制该字段
- 数据库中该字段包含正确的JSON数据

## 验证步骤

### 1. 重新编译并启动服务

```bash
mvn clean compile
```

### 2. 重新测试创建接口

使用相同的测试数据：

```json
{
  "productId": 2,
  "countryCodes": ["JP"],
  "priceType": "TIERED_INCREMENTAL",
  "tieredIncrementalPrices": "[{\"tierStart\":1,\"tierEnd\":2000,\"firstWeight\":500,\"firstPrice\":3150,\"additionalWeight\":500,\"additionalPrice\":550,\"registrationFee\":0},{\"tierStart\":2000,\"tierEnd\":10000,\"firstWeight\":500,\"firstPrice\":3150,\"additionalWeight\":500,\"additionalPrice\":650,\"registrationFee\":0}]"
}
```

### 3. 检查数据库

```sql
SELECT id, product_id, country_code, price_type, tiered_incremental_prices 
FROM agent_logistics_product_price 
WHERE country_code = 'JP' AND product_id = 2
ORDER BY id DESC LIMIT 1;
```

### 4. 预期结果

- INSERT SQL应该包含 `tiered_incremental_prices` 字段
- 数据库中该字段应该包含完整的JSON配置
- 运费计算应该能够正确使用阶梯递增价格

## 相关文件清单

### 已修复的文件

1. **LogisticsProductPriceBatchCreateReqVO.java** - 添加了 `tieredIncrementalPrices` 字段

### 已确认正确的文件

1. **LogisticsProductPriceDO.java** - 实体类包含该字段 ✅
2. **LogisticsProductPriceImportExcelVO.java** - Excel导入VO包含该字段 ✅
3. **数据库表结构** - 包含 `tiered_incremental_prices` 字段 ✅

### 其他相关文件

1. **TieredIncrementalPriceCalculator.java** - 计算工具类 ✅
2. **ShippingQuoteServiceImpl.java** - 运费计算服务 ✅
3. **AgentLogisticsPriceTypeEnum.java** - 价格类型枚举 ✅

## 测试用例

### 测试用例1：创建阶梯递增价格

```bash
curl -X POST "http://localhost:48080/admin-api/agent/logistics-product/logistics-product-price/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "productId": 2,
    "countryCodes": ["JP"],
    "priceType": "TIERED_INCREMENTAL",
    "tieredIncrementalPrices": "[{\"tierStart\":1,\"tierEnd\":2000,\"firstWeight\":500,\"firstPrice\":3150,\"additionalWeight\":500,\"additionalPrice\":550,\"registrationFee\":0}]",
    "status": 0
  }'
```

### 测试用例2：运费计算

```bash
curl -X POST "http://localhost:48080/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{
    "countryCode": "JP",
    "weight": 1500
  }'
```

预期返回包含正确计算的运费。

## 注意事项

1. **字段命名一致性**：确保VO中的字段名与实体类中的字段名完全一致
2. **JSON格式验证**：前端传递的JSON格式必须正确
3. **类型安全**：使用枚举而不是硬编码字符串
4. **向后兼容**：修复不影响现有的TIERED和INCREMENTAL类型

## 总结

这是一个典型的"数据传输对象(DTO)字段缺失"问题。虽然数据库表结构、实体类、计算逻辑都是正确的，但由于Controller的VO类中缺少字段，导致数据无法正确传递到数据库。

修复后，阶梯递增价格功能应该能够正常工作。
