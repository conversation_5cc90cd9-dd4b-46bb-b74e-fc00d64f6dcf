# 分类限制返回格式说明

## 修改概述

根据需求，分类限制信息现在直接按照数据库的原始格式返回给前端，前端可以直接转换为相应的名称。

## 返回格式

### 新增字段：categoryRestrictionsRaw

在 `restrictions` 对象中新增了 `categoryRestrictionsRaw` 字段，直接返回数据库中的原始JSON格式：

```json
{
  "restrictions": {
    "minWeight": 0,
    "maxWeight": 0,
    "dimensionRestriction": "请参考具体产品限制",
    "volumeWeightRule": "2",
    "categoryRestrictions": [
      {
        "name": "分类25",
        "allowList": [],
        "blockList": ["198", "197", "196"],
        "allowlist": [],
        "blocklist": ["198", "197", "196"]
      }
    ],
    "categoryRestrictionsRaw": "[{\"id\":25,\"allowList\":[],\"blockList\":[198,197,196,195,194,193,192,191,190,189,188,187,186]},{\"id\":21,\"allowList\":[177,174],\"blockList\":[176,175,173,172,171,170,169,168,167,166]}]"
  }
}
```

### 原始JSON格式

`categoryRestrictionsRaw` 字段包含的原始JSON格式如下：

```json
[
  {
    "id": 25,
    "allowList": [],
    "blockList": [198, 197, 196, 195, 194, 193, 192, 191, 190, 189, 188, 187, 186]
  },
  {
    "id": 21,
    "allowList": [177, 174],
    "blockList": [176, 175, 173, 172, 171, 170, 169, 168, 167, 166]
  },
  {
    "id": 18,
    "allowList": [146, 145, 143, 142],
    "blockList": [144, 141]
  }
]
```

## 前端使用方式

### 1. 解析原始JSON

```javascript
// 获取原始分类限制配置
const categoryRestrictionsRaw = response.data[0].restrictions.categoryRestrictionsRaw;

if (categoryRestrictionsRaw) {
  // 解析JSON
  const categoryRestrictions = JSON.parse(categoryRestrictionsRaw);
  
  // 遍历处理
  categoryRestrictions.forEach(restriction => {
    console.log('分类ID:', restriction.id);
    console.log('允许列表:', restriction.allowList);
    console.log('禁止列表:', restriction.blockList);
  });
}
```

### 2. 转换为显示名称

```javascript
// 假设有分类名称映射
const categoryNameMap = {
  25: '服饰',
  21: '电子产品',
  18: '食品',
  // ... 更多分类映射
};

const subCategoryNameMap = {
  198: '奢侈品牌服饰',
  197: '国际品牌服饰',
  177: '普通电子产品',
  174: '数码配件',
  // ... 更多子分类映射
};

// 转换为显示格式
function formatCategoryRestrictions(categoryRestrictionsRaw) {
  if (!categoryRestrictionsRaw) return [];
  
  const restrictions = JSON.parse(categoryRestrictionsRaw);
  
  return restrictions.map(restriction => ({
    categoryId: restriction.id,
    categoryName: categoryNameMap[restriction.id] || `分类${restriction.id}`,
    allowList: restriction.allowList.map(id => ({
      id: id,
      name: subCategoryNameMap[id] || `子分类${id}`
    })),
    blockList: restriction.blockList.map(id => ({
      id: id,
      name: subCategoryNameMap[id] || `子分类${id}`
    }))
  }));
}

// 使用示例
const formattedRestrictions = formatCategoryRestrictions(categoryRestrictionsRaw);
console.log('格式化后的分类限制:', formattedRestrictions);
```

### 3. Vue组件示例

```vue
<template>
  <div class="category-restrictions">
    <h4>分类限制</h4>
    <div v-if="!categoryRestrictions.length" class="no-restrictions">
      无分类限制
    </div>
    <div v-else>
      <div v-for="restriction in categoryRestrictions" :key="restriction.categoryId" class="restriction-item">
        <h5>{{ restriction.categoryName }}</h5>
        
        <div v-if="restriction.allowList.length" class="allow-list">
          <span class="label">允许：</span>
          <span v-for="item in restriction.allowList" :key="item.id" class="tag allow">
            {{ item.name }}
          </span>
        </div>
        
        <div v-if="restriction.blockList.length" class="block-list">
          <span class="label">禁止：</span>
          <span v-for="item in restriction.blockList" :key="item.id" class="tag block">
            {{ item.name }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    categoryRestrictionsRaw: {
      type: String,
      default: null
    }
  },
  computed: {
    categoryRestrictions() {
      return this.formatCategoryRestrictions(this.categoryRestrictionsRaw);
    }
  },
  methods: {
    formatCategoryRestrictions(rawData) {
      if (!rawData) return [];
      
      try {
        const restrictions = JSON.parse(rawData);
        return restrictions.map(restriction => ({
          categoryId: restriction.id,
          categoryName: this.getCategoryName(restriction.id),
          allowList: restriction.allowList.map(id => ({
            id: id,
            name: this.getSubCategoryName(id)
          })),
          blockList: restriction.blockList.map(id => ({
            id: id,
            name: this.getSubCategoryName(id)
          }))
        }));
      } catch (error) {
        console.error('解析分类限制配置失败:', error);
        return [];
      }
    },
    
    getCategoryName(categoryId) {
      // 这里可以从store或API获取分类名称
      const categoryMap = this.$store.getters.categoryMap;
      return categoryMap[categoryId] || `分类${categoryId}`;
    },
    
    getSubCategoryName(subCategoryId) {
      // 这里可以从store或API获取子分类名称
      const subCategoryMap = this.$store.getters.subCategoryMap;
      return subCategoryMap[subCategoryId] || `子分类${subCategoryId}`;
    }
  }
}
</script>
```

## 兼容性说明

### 保留原有字段

为了保持兼容性，原有的 `categoryRestrictions` 数组字段仍然保留，包含转换后的格式：

```json
{
  "categoryRestrictions": [
    {
      "name": "分类25",
      "allowList": [],
      "blockList": ["198", "197", "196"],
      "allowlist": [],
      "blocklist": ["198", "197", "196"]
    }
  ]
}
```

### 推荐使用方式

- **新开发的前端**：建议使用 `categoryRestrictionsRaw` 字段
- **现有前端**：可以继续使用 `categoryRestrictions` 字段，或逐步迁移到新字段

## 优势

### 1. 性能优化
- 后端不需要进行复杂的名称查询和转换
- 减少数据库查询次数
- 降低响应时间

### 2. 灵活性
- 前端可以根据需要选择显示格式
- 支持多语言显示
- 便于缓存分类名称映射

### 3. 数据一致性
- 直接使用数据库原始数据，避免转换错误
- 前端可以实时获取最新的分类信息

## 测试验证

### 测试API返回

```bash
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250,"categoryIds":[198]}'
```

### 预期返回

```json
{
  "code": 0,
  "data": [
    {
      "restrictions": {
        "categoryRestrictionsRaw": "[{\"id\":25,\"allowList\":[],\"blockList\":[198,197,196]}]"
      },
      "available": false,
      "unavailableReason": "商品分类 [198] 被禁止运输"
    }
  ]
}
```

## 注意事项

1. **JSON格式验证**：后端会验证JSON格式的正确性，格式错误时返回null
2. **空值处理**：如果没有分类限制配置，`categoryRestrictionsRaw` 为null
3. **前端解析**：前端需要做好JSON解析的异常处理
4. **数据类型**：原始JSON中的ID都是数字类型，前端需要注意类型转换
