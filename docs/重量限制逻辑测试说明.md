# 重量限制逻辑测试说明

## 新的业务逻辑

### 重量限制规则
1. **最小重量限制**：
   - `null` 或 `≤0` → 无最小重量限制
   - `>0` → 有最小重量限制

2. **最大重量限制**：
   - `null` 或 `≤0` → 无最大重量限制  
   - `>0` → 有最大重量限制

## 测试用例

### 用例1: 无任何重量限制
```sql
-- 数据配置
min_weight = NULL, max_weight = NULL
-- 或者
min_weight = 0, max_weight = 0

-- 测试结果
重量250g → ✅ 通过
重量50000g → ✅ 通过
重量1g → ✅ 通过
```

### 用例2: 只有最小重量限制
```sql
-- 数据配置
min_weight = 100, max_weight = NULL
-- 或者
min_weight = 100, max_weight = 0

-- 测试结果
重量250g → ✅ 通过 (250 >= 100)
重量50g → ❌ 失败 "重量50g低于最小限制100g"
重量100g → ✅ 通过 (100 >= 100)
```

### 用例3: 只有最大重量限制
```sql
-- 数据配置
min_weight = NULL, max_weight = 20000
-- 或者
min_weight = 0, max_weight = 20000

-- 测试结果
重量250g → ✅ 通过 (250 <= 20000)
重量25000g → ❌ 失败 "重量25000g超过最大限制20000g"
重量20000g → ✅ 通过 (20000 <= 20000)
```

### 用例4: 有最小和最大重量限制
```sql
-- 数据配置
min_weight = 100, max_weight = 20000

-- 测试结果
重量250g → ✅ 通过 (100 <= 250 <= 20000)
重量50g → ❌ 失败 "重量50g低于最小限制100g"
重量25000g → ❌ 失败 "重量25000g超过最大限制20000g"
重量100g → ✅ 通过
重量20000g → ✅ 通过
```

## 日志验证

### 正常情况的日志
```
检查重量限制详情: priceRuleId=123, 重量=250g, 最小重量=null, 最大重量=0
该产品无最大重量限制
该产品无最小重量限制
重量检查通过
重量限制配置: 最小=无限制, 最大=无限制
```

### 有限制的日志
```
检查重量限制详情: priceRuleId=456, 重量=250g, 最小重量=100, 最大重量=20000
重量检查通过
重量限制配置: 最小=100g, 最大=20000g
```

### 超限的日志
```
检查重量限制详情: priceRuleId=789, 重量=50g, 最小重量=100, 最大重量=20000
重量检查失败: 重量50g低于最小限制100g
```

## 数据库测试数据

### 创建测试数据
```sql
-- 清理测试数据
DELETE FROM logistics_product_price WHERE country_code = 'TEST';

-- 插入测试数据
INSERT INTO logistics_product_price (
    product_id, country_code, min_weight, max_weight, 
    first_price, first_unit, status, create_time
) VALUES 
-- 无限制
(1, 'TEST', NULL, NULL, 1000, 500, 1, NOW()),
(1, 'TEST2', 0, 0, 1000, 500, 1, NOW()),

-- 只有最小限制
(2, 'TEST', 100, NULL, 1000, 500, 1, NOW()),
(2, 'TEST2', 100, 0, 1000, 500, 1, NOW()),

-- 只有最大限制  
(3, 'TEST', NULL, 20000, 1000, 500, 1, NOW()),
(3, 'TEST2', 0, 20000, 1000, 500, 1, NOW()),

-- 有最小和最大限制
(4, 'TEST', 100, 20000, 1000, 500, 1, NOW());
```

### 测试API调用
```bash
# 测试无限制 (应该成功)
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"TEST","weight":250}'

# 测试最小重量限制 (应该失败)
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"TEST","weight":50}'

# 测试最大重量限制 (应该失败)
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"TEST","weight":25000}'
```

## 验证步骤

### 1. 检查现有数据
```sql
-- 查看当前US国家的价格规则
SELECT id, product_id, min_weight, max_weight, status
FROM logistics_product_price 
WHERE country_code = 'US';
```

### 2. 测试原来失败的请求
```bash
# 这个请求原来失败，现在应该成功
curl -X POST "/app-api/agent/shipping-calculation/quote" \
  -H "Content-Type: application/json" \
  -d '{"countryCode":"US","weight":250}'
```

### 3. 查看日志输出
应该看到类似的日志：
```
检查重量限制详情: priceRuleId=X, 重量=250g, 最小重量=0, 最大重量=0
该产品无最大重量限制
该产品无最小重量限制
重量检查通过
重量限制配置: 最小=无限制, 最大=无限制
```

### 4. 验证返回结果
返回的JSON中应该有：
```json
{
  "available": true,
  "feeDetail": {
    "total": "XX.XX"
  },
  "restrictions": {
    "minWeight": 0,
    "maxWeight": 0
  }
}
```

## 注意事项

1. **向后兼容**: 现有的 `max_weight = 0` 数据不需要修改，系统会自动识别为无限制
2. **数据一致性**: 建议定期检查是否有负数重量限制的异常数据
3. **业务理解**: 0值表示无限制是更符合业务直觉的设计
4. **日志监控**: 关注"重量限制配置"日志，确保业务逻辑正确执行

## 常见问题

### Q: 为什么0表示无限制而不是真的限制为0？
A: 在物流业务中，重量限制为0没有实际意义，因为任何包裹都有重量。0更适合表示"无限制"的语义。

### Q: 如果真的需要限制最小重量为0怎么办？
A: 可以设置一个很小的正数，比如1g，实际业务中这样的需求很少见。

### Q: 数据库中的历史数据需要迁移吗？
A: 不需要，新的逻辑向后兼容，现有的0值会被正确识别为无限制。
