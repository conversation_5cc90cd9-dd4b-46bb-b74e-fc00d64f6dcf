# OneBound SKU转换逻辑说明

## 问题分析

### 原始问题
OneBound返回的SKU数据格式与我们系统需要的格式不匹配：

1. **属性解析不正确**：没有正确解析`properties_name`字段
2. **属性ID不统一**：相同属性类型使用了不同的ID
3. **属性分组缺失**：前端无法正确显示属性选择器

### OneBound原始数据格式
```json
{
  "sku_id": "1001",
  "price": "17.90",
  "orginal_price": "39.90", 
  "quantity": "100",
  "properties_name": "颜色分类:【雅黑-限时特惠】;是否无线:否;键数:104键;套餐类型:官方标配"
}
```

### 期望的转换结果
```json
{
  "id": 1001,
  "properties": [
    {
      "propertyId": 1,
      "propertyName": "颜色分类",
      "valueId": 1,
      "valueName": "雅黑-限时特惠"
    },
    {
      "propertyId": 2,
      "propertyName": "是否无线", 
      "valueId": 1,
      "valueName": "否"
    },
    {
      "propertyId": 3,
      "propertyName": "键数",
      "valueId": 1,
      "valueName": "104键"
    },
    {
      "propertyId": 4,
      "propertyName": "套餐类型",
      "valueId": 1,
      "valueName": "官方标配"
    }
  ],
  "price": 1790,
  "marketPrice": 3990,
  "stock": 100
}
```

## 解决方案

### 1. 统一属性映射策略

#### 属性名称映射
```java
// 构建全局属性名称到ID的映射
Map<String, Long> propertyNameToIdMap = buildPropertyNameToIdMap(allSkus);

// 示例映射结果：
// "颜色分类" -> 1L
// "是否无线" -> 2L  
// "键数" -> 3L
// "套餐类型" -> 4L
```

#### 属性值映射
```java
// 构建每个属性下的值到ID的映射
Map<String, Map<String, Long>> propertyValueToIdMap = buildPropertyValueToIdMap(allSkus);

// 示例映射结果：
// "颜色分类" -> {"雅黑-限时特惠": 1L, "白键-悬浮炫光": 2L, "黑键-悬浮炫光": 3L}
// "是否无线" -> {"否": 1L}
// "键数" -> {"104键": 1L}
// "套餐类型" -> {"官方标配": 1L, "鼠标+送鼠标垫": 2L}
```

### 2. 属性解析逻辑

#### 解析步骤
1. **分割属性对**：按`;`分割`properties_name`
2. **解析键值对**：按`:`分割每个属性对
3. **清理属性值**：移除`【】`等特殊字符
4. **映射ID**：使用统一的属性和值ID映射

#### 代码实现
```java
default List<AppProductPropertyValueDetailRespVO> parseSkuPropertiesV2(
    OneBoundDetailRespDTO.Sku currentSku, 
    List<OneBoundDetailRespDTO.Sku> allSkus) {
    
    // 构建全局映射
    Map<String, Long> propertyNameToIdMap = buildPropertyNameToIdMap(allSkus);
    Map<String, Map<String, Long>> propertyValueToIdMap = buildPropertyValueToIdMap(allSkus);
    
    // 解析当前SKU属性
    String[] propertyPairs = currentSku.getPropertiesName().split(";");
    for (String pair : propertyPairs) {
        String[] parts = pair.split(":", 2);
        String propertyName = parts[0].trim();
        String valueName = cleanPropertyValue(parts[1].trim());
        
        // 使用统一的ID映射
        Long propertyId = propertyNameToIdMap.get(propertyName);
        Long valueId = propertyValueToIdMap.get(propertyName).get(valueName);
        
        // 创建属性对象
        AppProductPropertyValueDetailRespVO property = new AppProductPropertyValueDetailRespVO();
        property.setPropertyId(propertyId);
        property.setPropertyName(propertyName);
        property.setValueId(valueId);
        property.setValueName(valueName);
    }
}
```

### 3. 特殊字符处理

#### 清理规则
```java
default String cleanPropertyValue(String value) {
    // 移除【】符号
    value = value.replaceAll("[【】\\[\\]]", "");
    // 移除多余空格
    value = value.trim();
    return value;
}
```

#### 处理示例
- `【雅黑-限时特惠】` → `雅黑-限时特惠`
- `【白键-悬浮炫光】` → `白键-悬浮炫光`

## 转换效果对比

### 转换前（问题状态）
```json
{
  "properties": [
    {
      "propertyId": 1,
      "propertyName": "属性1", 
      "valueId": 1,
      "valueName": "1627207"
    }
  ]
}
```

### 转换后（正确状态）
```json
{
  "properties": [
    {
      "propertyId": 1,
      "propertyName": "颜色分类",
      "valueId": 1, 
      "valueName": "雅黑-限时特惠"
    },
    {
      "propertyId": 2,
      "propertyName": "是否无线",
      "valueId": 1,
      "valueName": "否"
    },
    {
      "propertyId": 3,
      "propertyName": "键数", 
      "valueId": 1,
      "valueName": "104键"
    },
    {
      "propertyId": 4,
      "propertyName": "套餐类型",
      "valueId": 1,
      "valueName": "官方标配"
    }
  ]
}
```

## 前端展示效果

### 属性选择器结构
```
颜色分类:
[雅黑-限时特惠] [白键-悬浮炫光] [黑键-悬浮炫光] [台金-悬浮炫光]

是否无线:
[否]

键数:
[104键]

套餐类型:
[官方标配] [鼠标+送鼠标垫]
```

### SKU组合逻辑
- 每个属性组合对应一个唯一的SKU
- 相同属性类型的不同值可以互相切换
- 价格和库存根据选择的组合动态更新

## 测试验证

### 测试用例
创建了`OneBoundSkuConvertTest`测试类，包含：

1. **模拟数据创建**：创建5个不同属性组合的SKU
2. **转换验证**：验证转换后的属性ID一致性
3. **分组验证**：验证属性按类型正确分组

### 运行测试
```bash
# 运行SKU转换测试
mvn test -Dtest=OneBoundSkuConvertTest
```

## 注意事项

### 1. 属性ID一致性
- 相同属性名称必须使用相同的propertyId
- 相同属性值必须使用相同的valueId
- 确保前端能正确识别属性分组

### 2. 异常处理
- 解析失败时提供默认属性
- 处理空值和异常格式
- 记录详细的错误日志

### 3. 性能考虑
- 一次性构建全局映射，避免重复计算
- 使用HashMap提高查找效率
- 合理设置缓存策略

## 总结

新的SKU转换逻辑解决了以下问题：

1. ✅ **正确解析属性**：准确解析OneBound的`properties_name`字段
2. ✅ **统一属性ID**：相同属性类型使用一致的ID
3. ✅ **支持属性分组**：前端可以正确显示属性选择器
4. ✅ **清理特殊字符**：移除不必要的格式字符
5. ✅ **异常容错**：提供完善的错误处理机制

转换后的数据完全符合前端展示需求，用户可以正常选择商品规格并查看对应的价格和库存信息。
