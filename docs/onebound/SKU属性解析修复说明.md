# OneBound SKU属性解析修复说明

## 问题分析

### 发现的问题
通过分析真实的OneBound返回数据（`docs/onebound/淘宝详情-662092201146.txt`），发现了属性解析的关键问题：

1. **属性名称显示错误**：应该显示"颜色分类"和"套餐类型"，但显示成了其他内容
2. **属性值前缀问题**：属性值包含不必要的前缀信息
3. **数据结构理解错误**：没有正确利用OneBound提供的`props_list`字段

### OneBound数据结构分析

#### props_list字段（关键）
```json
"props_list": {
  "1627207:16990330706": "颜色分类:【雅黑-限时特惠】",
  "1627207:10679502937": "颜色分类:【白键-悬浮炫光】", 
  "1627207:10679502939": "颜色分类:【黑键-悬浮炫光】",
  "1627207:10679502941": "颜色分类:【合金-悬浮炫光】",
  "1627207:16990333835": "颜色分类:【雅黑-限时特惠】+鼠标+送鼠标垫",
  "5919063:6536025": "套餐类型:官方标配"
}
```

#### SKU的properties字段
```json
{
  "properties": "1627207:16990330706;5919063:6536025",
  "properties_name": "1627207:16990330706:颜色分类:【雅黑-限时特惠】;5919063:6536025:套餐类型:官方标配"
}
```

#### 数据关系
- **properties**: 包含属性ID组合 `"1627207:16990330706;5919063:6536025"`
- **props_list**: 提供ID到完整属性信息的映射
- **正确解析方式**: 通过properties中的ID去props_list中查找对应的属性名称和值

## 解决方案

### 1. 添加props_list字段支持

#### 修改OneBoundDetailRespDTO
```java
@Data
public static class Item {
    // ... 其他字段
    
    /**
     * 属性映射列表 - 属性ID到属性名称的映射
     */
    @JsonProperty("props_list")
    private Map<String, String> propsList;
}
```

### 2. 重新设计属性解析逻辑

#### 新的解析流程
```java
default List<AppProductPropertyValueDetailRespVO> parseSkuPropertiesV3(
        OneBoundDetailRespDTO.Sku currentSku, 
        List<OneBoundDetailRespDTO.Sku> allSkus, 
        Map<String, String> propsList) {
    
    // 1. 解析properties: "1627207:16990330706;5919063:6536025"
    String[] propertyIds = currentSku.getProperties().split(";");
    
    // 2. 通过props_list查找每个ID对应的完整信息
    for (String propertyId : propertyIds) {
        String fullPropertyInfo = propsList.get(propertyId.trim());
        // fullPropertyInfo = "颜色分类:【雅黑-限时特惠】"
        
        // 3. 解析属性名称和值
        String[] parts = fullPropertyInfo.split(":", 2);
        String propertyName = parts[0].trim();  // "颜色分类"
        String valueName = cleanPropertyValue(parts[1].trim()); // "雅黑-限时特惠"
    }
}
```

### 3. 统一属性ID映射

#### 全局属性映射构建
```java
// 构建属性名称到ID的映射
default Map<String, Long> buildPropertyNameToIdMapV3(List<OneBoundDetailRespDTO.Sku> allSkus, Map<String, String> propsList) {
    Map<String, Long> propertyNameToIdMap = new HashMap<>();
    long propertyIdCounter = 1L;
    
    // 遍历props_list，提取所有属性名称
    for (String fullPropertyInfo : propsList.values()) {
        if (fullPropertyInfo.contains(":")) {
            String propertyName = fullPropertyInfo.split(":", 2)[0].trim();
            if (!propertyNameToIdMap.containsKey(propertyName)) {
                propertyNameToIdMap.put(propertyName, propertyIdCounter++);
            }
        }
    }
    
    return propertyNameToIdMap;
    // 结果: {"颜色分类": 1L, "套餐类型": 2L}
}
```

## 修复效果对比

### 修复前（错误）
```json
{
  "properties": [
    {
      "propertyId": 1,
      "propertyName": "属性1",
      "valueId": 1, 
      "valueName": "1627207"
    }
  ]
}
```

### 修复后（正确）
```json
{
  "properties": [
    {
      "propertyId": 1,
      "propertyName": "颜色分类",
      "valueId": 1,
      "valueName": "雅黑-限时特惠"
    },
    {
      "propertyId": 2,
      "propertyName": "套餐类型", 
      "valueId": 1,
      "valueName": "官方标配"
    }
  ]
}
```

### 前端展示效果
```
颜色分类:
[雅黑-限时特惠] [白键-悬浮炫光] [黑键-悬浮炫光] [合金-悬浮炫光] [雅黑-限时特惠+鼠标+送鼠标垫]

套餐类型:
[官方标配]
```

## 特殊字符处理

### 清理规则
```java
default String cleanPropertyValue(String value) {
    // 移除【】符号: 【雅黑-限时特惠】 → 雅黑-限时特惠
    value = value.replaceAll("[【】\\[\\]]", "");
    // 移除多余空格
    value = value.trim();
    return value;
}
```

### 处理示例
- `【雅黑-限时特惠】` → `雅黑-限时特惠`
- `【白键-悬浮炫光】` → `白键-悬浮炫光`
- `【合金-悬浮炫光】` → `合金-悬浮炫光`

## 测试验证

### 1. 真实数据测试
创建了`OneBoundRealDataTest`测试类：
- 使用真实的OneBound返回数据
- 验证属性解析的正确性
- 输出JSON格式便于前端测试

### 2. 运行测试
```bash
# 运行真实数据测试
mvn test -Dtest=OneBoundRealDataTest

# 运行SKU转换测试
mvn test -Dtest=OneBoundSkuConvertTest
```

### 3. 期望输出
```
=== 属性分组验证 ===
颜色分类:
  - 雅黑-限时特惠 (出现1次)
  - 白键-悬浮炫光 (出现1次)
  - 黑键-悬浮炫光 (出现1次)
  - 合金-悬浮炫光 (出现1次)
  - 雅黑-限时特惠+鼠标+送鼠标垫 (出现1次)
套餐类型:
  - 官方标配 (出现5次)
```

## 关键改进点

### 1. 数据源正确性
- ✅ 使用props_list作为权威数据源
- ✅ 正确解析属性ID到属性信息的映射关系
- ✅ 避免从properties_name中提取错误信息

### 2. 属性名称准确性
- ✅ 属性名称显示为"颜色分类"、"套餐类型"等正确名称
- ✅ 移除属性值中的特殊字符和前缀
- ✅ 保持属性值的完整语义信息

### 3. ID一致性保证
- ✅ 相同属性类型使用统一的propertyId
- ✅ 相同属性值使用统一的valueId
- ✅ 支持前端正确的属性分组显示

### 4. 异常处理完善
- ✅ props_list为空时的降级处理
- ✅ 属性解析失败时的默认值设置
- ✅ 详细的错误日志记录

## 总结

通过深入分析OneBound的真实数据结构，发现了props_list字段的重要性，并重新设计了属性解析逻辑：

1. **根本原因**：之前没有正确利用OneBound提供的props_list映射关系
2. **解决方案**：通过properties中的ID去props_list中查找完整的属性信息
3. **修复效果**：属性名称和值现在完全正确，支持前端正常的属性选择器显示

修复后的系统能够正确解析OneBound返回的SKU属性信息，为用户提供准确的商品规格选择功能。
