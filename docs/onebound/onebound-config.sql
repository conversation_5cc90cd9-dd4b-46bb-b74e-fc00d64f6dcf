-- OneBound API集成配置SQL脚本
-- 执行此脚本来配置OneBound API相关的系统配置

-- 1. 配置商品搜索策略为OneBound
INSERT INTO system_config (category, name, config_key, value, type, visible, remark, creator, create_time, updater, update_time, deleted) 
VALUES ('商品搜索', '搜索策略', 'product.search.strategy', 'onebound', 1, 1, '商品搜索策略：crawler=爬虫，onebound=万邦API', 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE 
    value = 'onebound',
    updater = 'admin',
    update_time = NOW();

-- 2. 配置OneBound API密钥（请替换为实际的密钥）
INSERT INTO system_config (category, name, config_key, value, type, visible, remark, creator, create_time, updater, update_time, deleted) 
VALUES ('OneBound', 'API密钥', 'onebound.key', 't8551216351', 1, 0, 'OneBound API密钥', 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE 
    value = 't8551216351',
    updater = 'admin',
    update_time = NOW();

-- 3. 配置OneBound API密钥（请替换为实际的密钥）
INSERT INTO system_config (category, name, config_key, value, type, visible, remark, creator, create_time, updater, update_time, deleted) 
VALUES ('OneBound', 'API密钥', 'onebound.secret', '63516f02', 1, 0, 'OneBound API密钥', 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE 
    value = '63516f02',
    updater = 'admin',
    update_time = NOW();

-- 查询配置结果
SELECT * FROM system_config WHERE config_key IN ('product.search.strategy', 'onebound.key', 'onebound.secret');

-- 如果需要切换回爬虫策略，执行以下SQL：
-- UPDATE system_config SET value = 'crawler' WHERE config_key = 'product.search.strategy';
