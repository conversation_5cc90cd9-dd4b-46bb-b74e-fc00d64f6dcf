# 运费计算详细日志说明

## 概述

现在运费计算过程已经添加了非常详细的日志，可以完整跟踪每一步的计算过程。本文档说明如何通过日志分析运费计算问题。

## 日志结构

### 1. 查询阶段日志
```
=== 开始查询运费报价 ===
查询参数: 国家=US, 重量=250g, 长宽高=[null,null,null], 分类=null
查询到1条价格规则
价格规则详情: productId=1, priceId=12, 重量限制=[0-0]g, 状态=1, 价格类型=TIERED
重量限制配置: 最小=无限制, 最大=无限制
```

### 2. 产品信息日志
```
查询到1个产品信息
产品详情: id=1, name=云途全球专线挂号（标快带电）, status=0, sort=2
```

### 3. 运费计算详细过程

#### 3.1 计算开始
```
--- 开始计算产品运费 ---
产品: id=1, name=云途全球专线挂号（标快带电）
价格规则: id=12, 重量限制=[0-0]g, 价格类型=TIERED
```

#### 3.2 重量检查
```
检查重量限制: 请求重量=250g, 限制范围=[0-0]g
该产品无最大重量限制
该产品无最小重量限制
重量检查通过
```

#### 3.3 体积重计算
```
体积重计算: 实重=250g, 体积重=0g, 计费重量=250g
基础费用信息: 实重=250g, 体积重=0g, 计费重量=250g, 货币=CNY(分)
```

#### 3.4 运费计算核心过程

##### 阶梯价格计算
```
=== 开始计算基础运费 ===
价格类型: TIERED, 计费重量: 250g
使用阶梯价格计算
阶梯价格配置: [{"tierStart":0,"tierEnd":500,"unitPrice":13100,"registrationFee":null}]
挂号费配置: 0

=== 开始阶梯价格计算 ===
输入参数: 重量=250g, 默认挂号费=0分
阶梯价格配置: [{"tierStart":0,"tierEnd":500,"unitPrice":13100,"registrationFee":null}]
解析到1个阶梯配置
阶梯1: 重量范围=[0-500]g, 单价=13100分, 挂号费=null分
匹配到阶梯: 重量范围=[0-500]g, 单价=13100分

--- 阶梯基础运费计算 ---
计算详情: 重量=250g = 0.250kg, 单价=13100分/kg
基础运费: 13100 × 0.250 = 3275分
--- 阶梯基础运费计算完成 ---

使用默认挂号费: 0分
=== 阶梯价格计算完成 ===
最终结果: 基础运费=3275分, 挂号费=0分, 使用阶梯挂号费=false

阶梯价格计算结果: 基础运费=3275分, 挂号费=0分, 使用阶梯挂号费=false
=== 基础运费计算完成 ===
```

##### 首重续重计算（如果是INCREMENTAL类型）
```
使用首重续重计算
首重: 500g = 1310分, 续重: 100g = 655分

--- 首重续重详细计算 ---
首重费用: 500g = 1310分
重量250g未超过首重500g，无续重费用
首重续重总费用: 1310分
--- 首重续重计算完成 ---

首重续重计算结果: 基础运费=1310分, 挂号费=0分
```

#### 3.5 费用设置过程
```
设置运费信息: 基础运费=3275分
首重信息: 1g = 131分
续重信息: 0g = 0分

=== 设置附加费用 ===
操作费: 0分
服务费: 0分
清关费: 0分
燃油费: 0分, 空运附加费: 0分
=== 附加费用设置完成 ===

=== 计算总费用 ===
费用明细: 运费=3275分, 操作费=0分, 服务费=0分, 清关费=0分
总费用: 3275分
=== 总费用计算完成 ===
```

#### 3.6 计算完成
```
运费计算完成: 总费用=3275
--- 产品运费计算完成 ---
结果: 可用=true, 总费用=3275
```

## 问题分析

### 问题1: 为什么总费用是0.35而不是3275？

从您提供的日志看到：
```
运费计算完成: 总费用=0.35
```

但按照新的逻辑，应该返回分为单位，即3275分。

**可能的原因**：
1. **formatMoney方法仍在使用**：虽然我们已经废弃了formatMoney方法，但可能某些地方仍在调用
2. **数据库中的金额单位问题**：可能数据库中存储的不是分而是元
3. **阶梯价格配置问题**：单价可能配置错误

### 问题2: 如何验证计算是否正确？

#### 2.1 检查阶梯价格配置
```sql
SELECT id, product_id, tiered_prices, first_price, first_unit 
FROM logistics_product_price 
WHERE id = 12;
```

#### 2.2 验证计算逻辑
假设阶梯配置为：
```json
[{"tierStart":0,"tierEnd":500,"unitPrice":13100,"registrationFee":null}]
```

计算过程：
- 重量：250g = 0.25kg
- 单价：13100分/kg
- 基础运费：13100 × 0.25 = 3275分
- 总费用：3275分（约32.75元）

#### 2.3 检查返回的JSON
正确的返回应该是：
```json
{
  "feeDetail": {
    "total": "3275",
    "freight": "3275",
    "currency": "CNY"
  }
}
```

而不是：
```json
{
  "feeDetail": {
    "total": "0.35",
    "freight": "34.75",
    "currency": "CNY"
  }
}
```

## 调试建议

### 1. 重新测试并查看完整日志
```bash
# 查看运费计算的完整日志
tail -f application.log | grep -E "(阶梯价格|基础运费|总费用|运费计算)"
```

### 2. 检查数据库配置
```sql
-- 检查价格规则配置
SELECT 
    id, product_id, price_type, 
    tiered_prices, 
    first_price, first_unit, additional_price, additional_unit,
    registration_fee, operation_fee, service_fee, customs_fee
FROM logistics_product_price 
WHERE id = 12;
```

### 3. 验证金额单位
确认数据库中存储的金额单位：
- 如果是分：13100表示131.00元
- 如果是元：131表示131.00元

### 4. 检查是否有formatMoney调用
```bash
# 搜索代码中是否还有formatMoney的调用
grep -r "formatMoney" yudao-module-mall/yudao-module-agent-biz/src/
```

## 预期的正确日志

重新测试后，应该看到类似这样的日志：
```
=== 开始阶梯价格计算 ===
输入参数: 重量=250g, 默认挂号费=0分
阶梯价格配置: [{"tierStart":0,"tierEnd":500,"unitPrice":13100}]
匹配到阶梯: 重量范围=[0-500]g, 单价=13100分
基础运费: 13100 × 0.250 = 3275分
=== 阶梯价格计算完成 ===
最终结果: 基础运费=3275分, 挂号费=0分

=== 计算总费用 ===
费用明细: 运费=3275分, 操作费=0分, 服务费=0分, 清关费=0分
总费用: 3275分
=== 总费用计算完成 ===

运费计算完成: 总费用=3275
```

## 总结

现在的日志系统可以完整跟踪运费计算的每一步，包括：
1. 阶梯价格解析和匹配
2. 基础运费计算详情
3. 附加费用设置
4. 总费用汇总

通过这些日志，可以快速定位计算错误的具体环节。
