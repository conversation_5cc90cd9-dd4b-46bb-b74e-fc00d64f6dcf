# 时间字段null值更新修复说明

## 问题描述

当前端更新价格信息时，如果将生效时间和失效时间设置为 `null`，数据库中的这两个字段不会被清除，仍然保留原来的值。

### 问题原因

MyBatis-Plus的 `updateById` 方法默认使用 `FieldStrategy.NOT_NULL` 策略，即只更新非 `null` 字段。当前端传递：

```json
{
  "effectiveTime": null,
  "expireTime": null
}
```

这些 `null` 值会被忽略，不会更新到数据库。

## 解决方案

### 方案选择

我们选择了**方案2：使用@TableField注解配置更新策略**，因为：

1. **代码简洁**：只需要在实体类字段上添加注解
2. **维护性好**：不需要修改业务逻辑代码
3. **性能优秀**：仍然使用 `updateById` 方法
4. **可控性强**：只对特定字段生效

### 具体修改

#### 1. 实体类字段注解

在 `LogisticsProductPriceDO.java` 中为时间字段添加注解：

```java
/**
 * 生效时间
 */
@TableField(updateStrategy = FieldStrategy.IGNORED)
private LocalDateTime effectiveTime;

/**
 * 失效时间
 */
@TableField(updateStrategy = FieldStrategy.IGNORED)
private LocalDateTime expireTime;
```

#### 2. 添加必要的导入

```java
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
```

#### 3. 增强日志记录

在 `updateLogisticsProductPrice` 方法中添加日志：

```java
log.info("更新价格规则: ID={}, 生效时间={}, 失效时间={}", 
        logisticsProductPrice.getId(), 
        logisticsProductPrice.getEffectiveTime(), 
        logisticsProductPrice.getExpireTime());
```

## 字段更新策略说明

### FieldStrategy枚举值

- **NOT_NULL**（默认）：非 `null` 判断，只更新非 `null` 字段
- **NOT_EMPTY**：非空判断，更新非 `null` 且非空字符串的字段
- **IGNORED**：忽略判断，无论字段值是什么都会更新
- **NEVER**：永不更新该字段

### 我们的选择

对于时间字段使用 `IGNORED` 策略：
- ✅ 允许设置为 `null`（清除时间限制）
- ✅ 允许设置具体时间值
- ✅ 强制更新，不忽略 `null` 值

## 测试验证

### 测试用例1：清除时间限制

```json
PUT /admin-api/agent/logistics-product/logistics-product-price/update
{
  "id": 14,
  "effectiveTime": null,
  "expireTime": null,
  // ... 其他字段
}
```

**预期结果**：
- 数据库中 `effective_time` 和 `expire_time` 字段被设置为 `NULL`
- 日志显示：`更新价格规则: ID=14, 生效时间=null, 失效时间=null`

### 测试用例2：设置时间限制

```json
PUT /admin-api/agent/logistics-product/logistics-product-price/update
{
  "id": 14,
  "effectiveTime": "2024-12-01T00:00:00",
  "expireTime": "2024-12-31T23:59:59",
  // ... 其他字段
}
```

**预期结果**：
- 数据库中时间字段被正确更新
- 日志显示具体的时间值

### 测试用例3：部分时间设置

```json
PUT /admin-api/agent/logistics-product/logistics-product-price/update
{
  "id": 14,
  "effectiveTime": "2024-12-01T00:00:00",
  "expireTime": null,
  // ... 其他字段
}
```

**预期结果**：
- `effective_time` 被设置为指定时间
- `expire_time` 被设置为 `NULL`

## 验证SQL

### 更新前查询

```sql
SELECT id, effective_time, expire_time 
FROM agent_logistics_product_price 
WHERE id = 14;
```

### 更新后查询

```sql
SELECT id, effective_time, expire_time 
FROM agent_logistics_product_price 
WHERE id = 14;
```

### 预期的UPDATE SQL

```sql
UPDATE agent_logistics_product_price 
SET effective_time = NULL, 
    expire_time = NULL,
    update_time = NOW()
WHERE id = 14;
```

## 其他字段考虑

### 当前使用IGNORED策略的字段

- `effectiveTime` - 生效时间
- `expireTime` - 失效时间

### 可能需要考虑的字段

如果将来有其他字段也需要支持 `null` 值更新，可以考虑添加相同的注解：

```java
@TableField(updateStrategy = FieldStrategy.IGNORED)
private String zoneCode;  // 区域代码，可能需要清空

@TableField(updateStrategy = FieldStrategy.IGNORED)
private String transitTime;  // 运输时间，可能需要清空

@TableField(updateStrategy = FieldStrategy.IGNORED)
private String tieredPrices;  // 阶梯价格，切换价格类型时需要清空

@TableField(updateStrategy = FieldStrategy.IGNORED)
private String tieredIncrementalPrices;  // 阶梯递增价格，切换价格类型时需要清空
```

## 注意事项

### 1. 数据一致性

使用 `IGNORED` 策略时要特别注意：
- 确保前端传递的 `null` 值是有意的
- 避免意外清空重要数据

### 2. 业务逻辑

- 清空时间限制意味着价格规则永久有效
- 设置时间限制要确保逻辑合理（生效时间 < 失效时间）

### 3. 前端处理

前端在发送更新请求时：
- 明确区分"不更新"和"设置为null"
- 提供用户友好的时间清除操作

## 总结

通过使用 `@TableField(updateStrategy = FieldStrategy.IGNORED)` 注解，我们解决了时间字段 `null` 值无法更新的问题。这个方案：

- ✅ **简洁高效**：只需要添加注解，不改变业务逻辑
- ✅ **精确控制**：只对特定字段生效，不影响其他字段
- ✅ **向后兼容**：不影响现有功能
- ✅ **易于维护**：代码清晰，容易理解

现在前端可以正确地清除或设置价格规则的时间限制了。
