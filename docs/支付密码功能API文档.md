# 支付密码功能API文档

## 概述

本文档描述了支付密码功能的API接口，包括支付密码的设置、修改、重置和验证功能。支付密码用于用户使用余额支付时的安全验证。

## 基础信息

- **基础URL**: `/member/user` (用户模块)
- **基础URL**: `/pay/order` (支付模块)
- **认证方式**: Bearer Token
- **Content-Type**: application/json

## API接口列表

### 1. 设置支付密码

**接口地址**: `PUT /member/user/set-pay-password`

**接口描述**: 用户首次设置支付密码

**请求参数**:
```json
{
  "payPassword": "123456",
  "code": "1234"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| payPassword | String | 是 | 支付密码，长度6-20位 |
| code | String | 是 | 短信验证码，长度4-6位 |

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

**错误码**:
- `1_004_001_006`: 支付密码未设置
- `1_004_001_008`: 新支付密码不能与旧密码相同

---

### 2. 修改支付密码

**接口地址**: `PUT /member/user/update-pay-password`

**接口描述**: 用户通过旧支付密码修改新支付密码

**请求参数**:
```json
{
  "oldPayPassword": "123456",
  "newPayPassword": "654321"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| oldPayPassword | String | 是 | 旧支付密码，长度6-20位 |
| newPayPassword | String | 是 | 新支付密码，长度6-20位 |

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

**错误码**:
- `1_004_001_006`: 支付密码未设置
- `1_004_001_007`: 支付密码错误
- `1_004_001_008`: 新支付密码不能与旧密码相同

---

### 3. 重置支付密码

**接口地址**: `PUT /member/user/reset-pay-password`

**接口描述**: 用户忘记支付密码时通过短信验证码重置

**认证要求**: 无需登录认证

**请求参数**:
```json
{
  "mobile": "15601691300",
  "code": "1234",
  "payPassword": "123456"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mobile | String | 是 | 手机号，格式：1[3-9]xxxxxxxxx |
| code | String | 是 | 短信验证码，长度4-6位 |
| payPassword | String | 是 | 新支付密码，长度6-20位 |

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

**错误码**:
- `1_004_001_001`: 手机号未注册用户

---

### 4. 验证支付密码

**接口地址**: `POST /member/user/verify-pay-password`

**接口描述**: 验证用户支付密码是否正确

**请求参数**:
```json
{
  "payPassword": "123456"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| payPassword | String | 是 | 支付密码，长度6-20位 |

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

**错误码**:
- `1_004_001_006`: 支付密码未设置
- `1_004_001_009`: 支付密码已被锁定
- `1_004_001_010`: 支付密码错误次数限制

---

### 5. 提交支付订单（含支付密码验证）

**接口地址**: `POST /pay/order/submit`

**接口描述**: 提交支付订单，使用余额支付时需要验证支付密码

**请求参数**:
```json
{
  "id": 1024,
  "channelCode": "wallet",
  "payPassword": "123456",
  "currency": "CNY"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 支付单编号 |
| channelCode | String | 是 | 支付渠道，余额支付为"wallet" |
| payPassword | String | 否 | 支付密码，余额支付时必填，长度6-20位 |
| currency | String | 否 | 币别，默认USD |
| channelExtras | Map | 否 | 支付渠道的额外参数 |
| displayMode | String | 否 | 展示模式 |
| returnUrl | String | 否 | 回跳地址 |
| cancelUrl | String | 否 | 取消跳转地址 |

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "status": 10,
    "displayMode": "url",
    "displayContent": "https://example.com/pay"
  },
  "msg": "操作成功"
}
```

**错误码**:
- `1_007_007_005`: 使用余额支付需要验证支付密码

## 错误码说明

### 用户模块错误码 (1_004_001_xxx)
- `1_004_001_000`: 用户不存在
- `1_004_001_001`: 手机号未注册用户
- `1_004_001_006`: 支付密码未设置
- `1_004_001_007`: 支付密码错误
- `1_004_001_008`: 新支付密码不能与旧密码相同
- `1_004_001_009`: 支付密码已被锁定，请X分钟后再试
- `1_004_001_010`: 支付密码错误，已错误X次，请注意账户安全

### 支付模块错误码 (1_007_007_xxx)
- `1_007_007_005`: 使用余额支付需要验证支付密码

## 使用流程

### 首次设置支付密码
1. 用户发送短信验证码
2. 调用设置支付密码接口，传入密码和验证码

### 修改支付密码
1. 调用修改支付密码接口，传入旧密码和新密码

### 忘记密码重置
1. 用户发送短信验证码
2. 调用重置支付密码接口，传入手机号、验证码和新密码

### 余额支付流程
1. 用户选择余额支付
2. 前端提示输入支付密码
3. 调用提交支付订单接口，传入支付密码
4. 系统验证支付密码后处理支付

## 安全防护机制

系统内置了支付密码安全防护机制，防止暴力破解：

### 配置参数
```yaml
yudao:
  member:
    pay-password-security:
      enabled: true # 是否启用支付密码安全防护
      max-error-count: 5 # 最大错误次数，超过此次数将被锁定
      lock-time-minutes: 30 # 锁定时间，单位：分钟
      reset-time-minutes: 60 # 错误计数重置时间，单位：分钟
```

### 防护规则
1. **错误次数限制**：支付密码连续错误达到配置的最大次数后，账户将被锁定
2. **锁定机制**：账户锁定后，在锁定时间内无法进行支付密码验证
3. **自动重置**：在重置时间内没有错误尝试，错误计数将自动重置为0
4. **日志记录**：所有错误尝试和锁定事件都会记录警告日志

### 管理员解锁
管理员可以通过以下接口手动解锁用户：
```
PUT /member/user/unlock-pay-password?id={userId}
```

## 注意事项

1. 支付密码采用BCrypt加密存储，与登录密码使用相同的加密方式
2. 余额支付时必须验证支付密码，其他支付方式不需要
3. 支付密码长度限制为6-20位
4. 重置支付密码接口无需登录认证，其他接口需要Bearer Token认证
5. 短信验证码的场景码请参考系统配置
6. 支付密码错误次数达到上限后会被锁定，需要等待锁定时间结束或管理员手动解锁
