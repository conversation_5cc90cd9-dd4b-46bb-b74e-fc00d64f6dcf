member.user.not.exists=L'utilisateur n'existe pas
member.user.mobile.not.exists=Le numéro de mobile n'est pas enregistré
member.user.mobile.used=Échec de la modification du numéro de mobile, le numéro ({}) est déjà utilisé
member.user.point.not.enough=Solde de points utilisateur insuffisant
member.user.email.not.exists=Email is not registered
member.user.change.password.not.match=Mot de passe ancien incorrect

member.auth.login.bad.credentials=Échec de la connexion, identifiant ou mot de passe incorrect
member.auth.login.user.disabled=Échec de la connexion, compte désactivé
member.auth.social.user.not.found=Échec de la connexion, impossible de récupérer les informations de connexion tierces
member.auth.mobile.used=Le numéro de mobile est déjà utilisé

member.address.not.exists=L'adresse utilisateur n'existe pas

member.tag.not.exists=Le tag utilisateur n'existe pas
member.tag.name.exists=Le tag utilisateur existe déjà
member.tag.has.user=Le tag utilisateur est associé à des utilisateurs et ne peut pas être supprimé

member.point.record.biz.not.support=Type de transaction de points utilisateur non pris en charge

member.sign.in.config.not.exists=La règle de pointage n'existe pas
member.sign.in.config.exists=La règle de pointage existe déjà

member.sign.in.record.today.exists=Vous avez déjà pointé aujourd'hui, ne recommencez pas

member.level.not.exists=Le niveau utilisateur n'existe pas
member.level.name.exists=Le nom du niveau utilisateur [{}] est déjà utilisé
member.level.value.exists=La valeur du niveau utilisateur [{}] est déjà utilisée par [{}]
member.level.experience.min=L'expérience d'amélioration doit être supérieure à celle du niveau précédent [{}] [{}]
member.level.experience.max=L'expérience d'amélioration doit être inférieure à celle du niveau suivant [{}] [{}]
member.level.has.user=Le niveau utilisateur est associé à des utilisateurs et ne peut pas être supprimé

member.experience.biz.not.support=Type de transaction d'expérience utilisateur non pris en charge

member.group.not.exists=Le groupe d'utilisateurs n'existe pas
member.group.has.user=Le groupe d'utilisateurs est associé à des utilisateurs et ne peut pas être supprimé
