package cn.iocoder.yudao.module.member.controller.app.ticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 工单评分 Request VO")
@Data
public class AppTicketRateReqVO {

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "工单编号不能为空")
    private Long id;

    @Schema(description = "评分", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    @NotNull(message = "评分不能为空")
    @Range(min = 1, max = 5, message = "评分必须在1-5分之间")
    private Integer rating;

    @Schema(description = "评分备注", example = "服务很好，问题解决及时")
    @Length(max = 500, message = "评分备注长度不能超过500个字符")
    private String ratingComment;

}
