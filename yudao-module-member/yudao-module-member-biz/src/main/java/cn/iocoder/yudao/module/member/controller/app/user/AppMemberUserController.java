package cn.iocoder.yudao.module.member.controller.app.user;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthLoginEmailReqVO;
import cn.iocoder.yudao.module.member.controller.app.user.vo.*;
import cn.iocoder.yudao.module.member.convert.user.MemberUserConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.level.MemberLevelDO;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.service.level.MemberLevelService;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 用户个人中心")
@RestController
@RequestMapping("/member/user")
@Validated
@Slf4j
public class AppMemberUserController {

    @Resource
    private MemberUserService userService;
    @Resource
    private MemberLevelService levelService;

    @GetMapping("/get")
    @Operation(summary = "获得基本信息")
    public CommonResult<AppMemberUserInfoRespVO> getUserInfo() {
        MemberUserDO user = userService.getUser(getLoginUserId());
        MemberLevelDO level = levelService.getLevel(user.getLevelId());
        return success(MemberUserConvert.INSTANCE.convert2(user, level));
    }

    @PutMapping("/update")
    @Operation(summary = "修改基本信息")
    public CommonResult<Boolean> updateUser(@RequestBody @Valid AppMemberUserUpdateReqVO reqVO) {
        userService.updateUser(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/update-mobile")
    @Operation(summary = "修改用户手机")
    public CommonResult<Boolean> updateUserMobile(@RequestBody @Valid AppMemberUserUpdateMobileReqVO reqVO) {
        userService.updateUserMobile(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/update-mobile-by-weixin")
    @Operation(summary = "基于微信小程序的授权码，修改用户手机")
    public CommonResult<Boolean> updateUserMobileByWeixin(@RequestBody @Valid AppMemberUserUpdateMobileByWeixinReqVO reqVO) {
        userService.updateUserMobileByWeixin(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/update-password")
    @Operation(summary = "修改用户密码", description = "用户修改密码时使用")
    public CommonResult<Boolean> updateUserPassword(@RequestBody @Valid AppMemberUserUpdatePasswordReqVO reqVO) {
        userService.updateUserPassword(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/update-user-password")
    @Operation(summary = "修改用户密码", description = "通过旧密码修改密码") //ding 新增
    public CommonResult<Boolean> updatePasswordUseOld(@RequestBody @Valid AppMemberUserUpdatePasswordUseOldReqVO reqVO) {
        userService.updateUserPasswordUseOld(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/reset-password")
    @Operation(summary = "重置密码", description = "用户忘记密码时使用")
    @PermitAll
    public CommonResult<Boolean> resetUserPassword(@RequestBody @Valid AppMemberUserResetPasswordReqVO reqVO) {
        userService.resetUserPassword(reqVO);
        return success(true);
    }


    @PutMapping("/reset-password-mail")
    @Operation(summary = "邮件重置密码", description = "用户忘记密码时使用")
    @PermitAll
    public CommonResult<Boolean> resetUserPasswordByEmail(@RequestBody @Valid AppMemberUserResetPasswordMailReqVO reqVO) {
        userService.resetUserPasswordMail(reqVO);
        return success(true);
    }

    @PutMapping("/delete")
    @Operation(summary = "删除用户", description = "用户删除数据使用")
    @PermitAll
    public CommonResult<Boolean> deleteAccount(@RequestBody @Valid AppAuthLoginEmailReqVO reqVO) {
        userService.deleteUser(reqVO);
        return success(true);
    }

    @PutMapping("/set-pay-password")
    @Operation(summary = "设置支付密码")
    public CommonResult<Boolean> setPayPassword(@RequestBody @Valid AppMemberUserSetPayPasswordReqVO reqVO) {
        userService.setPayPassword(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/update-pay-password")
    @Operation(summary = "修改支付密码")
    public CommonResult<Boolean> updatePayPassword(@RequestBody @Valid AppMemberUserUpdatePayPasswordReqVO reqVO) {
        userService.updatePayPassword(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/reset-pay-password")
    @Operation(summary = "重置支付密码", description = "用户忘记支付密码时使用")
    @PermitAll
    public CommonResult<Boolean> resetPayPassword(@RequestBody @Valid AppMemberUserResetPayPasswordReqVO reqVO) {
        userService.resetPayPassword(reqVO);
        return success(true);
    }

    @PostMapping("/verify-pay-password")
    @Operation(summary = "验证支付密码")
    public CommonResult<Boolean> verifyPayPassword(@RequestBody @Valid AppMemberUserVerifyPayPasswordReqVO reqVO) {
        boolean result = userService.verifyPayPassword(getLoginUserId(), reqVO.getPayPassword());
        return success(result);
    }

    @PutMapping("/update-auto-compensate-diff")
    @Operation(summary = "修改自动补款授权", description = "自动补款用户订单支付金额不足且有账户余额时使用")
    public CommonResult<Boolean> updateAutoCompensateDiff(@RequestBody @Valid AppMemberAutoCompensateDiffReqVO reqVO) {
        userService.updateAutoCompensateDiff(getLoginUserId(),reqVO);
        return success(true);
    }

}