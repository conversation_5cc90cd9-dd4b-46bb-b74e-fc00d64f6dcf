package cn.iocoder.yudao.module.member.controller.admin.ticket.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 用户工单新增工单信息 Request VO")
@Data
public class TicketMessageSaveReqVO {

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "25623")
    @NotNull(message = "工单编号不能为空")
    private Long ticketId;
    /**
     * 回复类型
     */
    @Schema(description = "回复类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "回复类型不能为空")
    private Integer messageType;
    /**
     * 回复内容
     */
    @Schema(description = "回复内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "回复内容不能为空")
    private String content;
    /**
     * 附件URL列表
     */
    private List<String> attachmentUrls;
//    /**
//     * 回复人编号
//     */
//    @Schema(description = "回复人编号", example = "4290")
//    @NotNull(message = "回复人编号不能为空")
//    private Long replierId;
//    /**
//     * 回复人名称
//     */
//    private String replierName;
}
