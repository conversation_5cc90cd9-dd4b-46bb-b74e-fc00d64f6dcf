package cn.iocoder.yudao.module.member.controller.app.subscribe.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @program: ruoyi-vue-pro
 * @description: 订阅
 * @author: Ding<PERSON><PERSON><PERSON>
 * @create: 2025-03-07 18:24
 **/

@Schema(description = "用户 APP - 用户订阅 Request VO")
@Data
public class AppUnsubscribeReqVO {


    @Schema(description = "用户名", example = "李四")
    private String name;

    @Schema(description = "用户邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "Email can not be empty.")
    private String email;

    @Schema(description = "token", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "Token can not be empty.")
    private String token;

}
