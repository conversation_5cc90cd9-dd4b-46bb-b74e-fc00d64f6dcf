package cn.iocoder.yudao.module.member.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "用户 APP - 用户咨询新增/修改 Request VO")
@Data
public class AppMemberInquirySaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21872")
    private Long id;

    @Schema(description = "用户名", example = "芋艿")
    private String name;

    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "邮箱不能为空")
    private String email;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "公司名称", example = "赵六")
    private String companyName;

    @Schema(description = "咨询类型", example = "1")
    private String type;

    @Schema(description = "描述", example = "你猜")
    private String description;


}