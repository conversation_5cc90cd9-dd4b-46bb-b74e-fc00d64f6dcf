package cn.iocoder.yudao.module.member.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "用户 APP - 用户咨询 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppMemberInquiryRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21872")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "用户名", example = "芋艿")
    @ExcelProperty("用户名")
    private String name;

    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("邮箱")
    private String email;

    @Schema(description = "电话")
    @ExcelProperty("电话")
    private String phone;

    @Schema(description = "公司名称", example = "赵六")
    @ExcelProperty("公司名称")
    private String companyName;

    @Schema(description = "咨询类型", example = "1")
    @ExcelProperty(value = "咨询类型", converter = DictConvert.class)
    @DictFormat("member_inquiry_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String type;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}