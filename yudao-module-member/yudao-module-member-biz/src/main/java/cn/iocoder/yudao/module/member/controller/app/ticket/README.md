# 工单系统后端实现说明

## 概述

本工单系统基于RuoYi-Vue-Pro框架实现，为代购网站用户提供完整的客服支持功能。

## 技术架构

### 数据库设计

#### 主表：member_ticket
- 存储工单基本信息
- 包含工单号、标题、类型、状态、优先级等字段
- 支持附件URL列表的JSON存储

#### 子表：member_ticket_message  
- 存储工单的所有回复记录
- 区分用户回复和客服回复
- 支持附件上传

### 核心组件

#### 1. 实体类 (DO)
- `TicketDO`: 工单主表实体
- `TicketMessageDO`: 工单消息实体

#### 2. 枚举类
- `MemberTicketTypeEnum`: 工单类型
- `MemberTicketStatusEnum`: 工单状态  
- `MemberTicketPriorityEnum`: 优先级
- `MemberTicketMessageTypeEnum`: 消息类型

#### 3. 控制器
- `AppTicketController`: App端API接口
- `TicketController`: 管理端API接口（自动生成）

#### 4. 服务层
- `TicketService`: 服务接口
- `TicketServiceImpl`: 服务实现

#### 5. 数据访问层
- `TicketMapper`: 工单数据访问
- `TicketMessageMapper`: 消息数据访问

## 核心功能

### 1. 工单管理
- 创建工单：用户可创建不同类型的工单
- 工单列表：支持分页、筛选、搜索
- 工单详情：查看工单及所有回复记录
- 工单关闭：用户可主动关闭工单

### 2. 消息回复
- 用户回复：用户可对工单进行回复
- 客服回复：管理员可回复用户工单
- 附件支持：支持多附件上传
- 状态流转：根据回复自动更新工单状态

### 3. 评分系统
- 服务评分：1-5分评分系统
- 评价备注：支持文字评价

### 4. 权限控制
- 用户隔离：用户只能访问自己的工单
- 状态限制：根据工单状态限制操作

## 业务流程

### 工单生命周期
1. **创建** → 待处理
2. **分配** → 处理中  
3. **回复** → 待用户回复
4. **解决** → 已解决
5. **关闭** → 已关闭

### 状态流转规则
- 用户创建工单 → 待处理
- 客服回复 → 待用户回复
- 用户回复 → 处理中
- 客服标记解决 → 已解决
- 用户/客服关闭 → 已关闭

## 技术特性

### 1. 数据库优化
- 使用Redis生成唯一工单号
- JSON字段存储附件列表
- 合理的索引设计

### 2. 安全性
- 用户权限校验
- 数据访问隔离
- 参数验证

### 3. 扩展性
- 枚举驱动的类型系统
- 灵活的附件处理
- 国际化支持

### 4. 性能优化
- 分页查询优化
- 缓存工单号生成
- 批量数据处理

## 配置说明

### Redis配置
工单号生成需要Redis支持：
```yaml
spring:
  redis:
    host: localhost
    port: 6379
```

### 文件上传配置
附件上传依赖文件服务：
```yaml
yudao:
  file:
    # 文件存储配置
```

## 部署注意事项

1. **数据库表创建**：确保member_ticket和member_ticket_message表已创建
2. **Redis服务**：确保Redis服务正常运行
3. **文件服务**：确保文件上传服务可用
4. **权限配置**：配置相应的菜单和权限

## 扩展建议

### 1. 消息通知
- 集成站内信通知
- 邮件/短信提醒
- WebSocket实时推送

### 2. 工作流集成
- 自动分配规则
- 升级机制
- SLA管理

### 3. 统计分析
- 工单统计报表
- 客服绩效分析
- 用户满意度统计

### 4. 智能化
- 自动回复机器人
- 问题分类推荐
- 知识库集成

## 测试建议

### 1. 单元测试
- Service层业务逻辑测试
- 权限校验测试
- 状态流转测试

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 文件上传测试

### 3. 性能测试
- 并发创建工单测试
- 大量数据分页测试
- Redis性能测试

## 维护指南

### 1. 日志监控
- 关键操作日志记录
- 异常情况监控
- 性能指标监控

### 2. 数据备份
- 定期备份工单数据
- 附件文件备份
- Redis数据备份

### 3. 版本升级
- 数据库结构变更
- 接口兼容性处理
- 配置项更新

---

**开发团队**: 后端开发组  
**文档版本**: v1.0.0  
**最后更新**: 2024-01-01
