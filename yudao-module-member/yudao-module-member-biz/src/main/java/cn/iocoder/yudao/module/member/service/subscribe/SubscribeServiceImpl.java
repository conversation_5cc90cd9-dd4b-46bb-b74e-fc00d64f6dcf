package cn.iocoder.yudao.module.member.service.subscribe;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.member.api.subscribe.dto.SubscribeUserPageApiReqDTO;
import cn.iocoder.yudao.module.member.api.subscribe.dto.SubscribeUserRespDTO;
import cn.iocoder.yudao.module.member.controller.admin.subscribe.vo.SubscribePageReqVO;
import cn.iocoder.yudao.module.member.controller.admin.subscribe.vo.SubscribeSaveReqVO;
import cn.iocoder.yudao.module.member.controller.app.subscribe.vo.AppSubscribeReqVO;
import cn.iocoder.yudao.module.member.controller.app.subscribe.vo.AppUnsubscribeReqVO;
import cn.iocoder.yudao.module.member.dal.dataobject.subscribe.SubscribeDO;
import cn.iocoder.yudao.module.member.dal.mysql.subscribe.SubscribeMapper;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static cn.iocoder.yudao.module.member.dal.redis.RedisKeyConstants.UNSUBSCRIBE_TOKEN;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.SUBSCRIBE_LINK_EXPIRED;

/**
 * 用户订阅 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SubscribeServiceImpl implements SubscribeService {

    @Resource
    private SubscribeMapper subscribeMapper;

    @Resource
    private MemberUserService userService;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Override
    public Long createSubscribe(SubscribeSaveReqVO createReqVO) {
        // 插入
        SubscribeDO subscribe = BeanUtils.toBean(createReqVO, SubscribeDO.class);
        subscribeMapper.insert(subscribe);
        // 返回
        return subscribe.getId();
    }

    @Override
    public void updateSubscribe(SubscribeSaveReqVO updateReqVO) {
        // 校验存在
        validateSubscribeExists(updateReqVO.getId());
        // 更新
        SubscribeDO updateObj = BeanUtils.toBean(updateReqVO, SubscribeDO.class);
        subscribeMapper.updateById(updateObj);
    }

    @Override
    public void deleteSubscribe(Long id) {
        // 校验存在
        validateSubscribeExists(id);
        // 删除
        subscribeMapper.deleteById(id);
    }

    private void validateSubscribeExists(Long id) {
        //if (subscribeMapper.selectById(id) == null) {
        //    throw exception(SUBSCRIBE_NOT_EXISTS);
        //}
    }

    @Override
    public SubscribeDO getSubscribe(Long id) {
        return subscribeMapper.selectById(id);
    }

    @Override
    public PageResult<SubscribeDO> getSubscribePage(SubscribePageReqVO pageReqVO) {
        return subscribeMapper.selectPage(pageReqVO);
    }


    //-------------APP


    @Override
    public Long createSubscribe(AppSubscribeReqVO createReqVO) {
        // 校验存在
        SubscribeDO subscribeDO = subscribeMapper.selectOne(SubscribeDO::getEmail, createReqVO.getEmail());
        if (subscribeDO == null) {
            subscribeDO = new SubscribeDO();
            BeanUtil.copyProperties(createReqVO, subscribeDO);
            subscribeMapper.insert(subscribeDO);
        }
        return subscribeDO.getId();
    }

    @Override
    public void unsubscribe(AppUnsubscribeReqVO updateReqVO) {
        // 校验存在
        String key = UNSUBSCRIBE_TOKEN + getTenantId() + ":"+updateReqVO.getToken();
        String email = stringRedisTemplate.opsForValue().get(key);
        if(StrUtil.isEmpty(email) || !email.equalsIgnoreCase(updateReqVO.getEmail())){
            throw exception(SUBSCRIBE_LINK_EXPIRED);
        }

        // 删除订阅表
        subscribeMapper.deleteByEmail(updateReqVO.getEmail());

        // 如果用户存在，则设置用户表为不订阅
        userService.unsubscribe(updateReqVO.getEmail());
        //删除key
        stringRedisTemplate.delete(key);
    }

    @Override
    public PageResult<SubscribeDO> getSubscribeUserPage(SubscribeUserPageApiReqDTO pageReqVO) {
        return subscribeMapper.selectSubscribeUserPage(pageReqVO);
    }
}