package cn.iocoder.yudao.module.member.controller.app.inquiry;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.member.controller.app.inquiry.vo.*;
import cn.iocoder.yudao.module.member.dal.dataobject.inquiry.MemberInquiryDO;
import cn.iocoder.yudao.module.member.service.inquiry.MemberInquiryService;

@Tag(name = "用户 APP - 用户咨询")
@RestController
@RequestMapping("/member/inquiry")
@Validated
public class AppMemberInquiryController {

    @Resource
    private MemberInquiryService inquiryService;

    @PostMapping("/create")
    @Operation(summary = "创建用户咨询")
    public CommonResult<Long> createInquiry(@Valid @RequestBody AppMemberInquirySaveReqVO createReqVO) {
        return success(inquiryService.createInquiryApp(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户咨询")
    public CommonResult<Boolean> updateInquiry(@Valid @RequestBody AppMemberInquirySaveReqVO updateReqVO) {
        inquiryService.updateInquiryApp(updateReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得用户咨询")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppMemberInquiryRespVO> getInquiry(@RequestParam("id") Long id) {
        MemberInquiryDO inquiry = inquiryService.getInquiry(id);
        return success(BeanUtils.toBean(inquiry, AppMemberInquiryRespVO.class));
    }



}