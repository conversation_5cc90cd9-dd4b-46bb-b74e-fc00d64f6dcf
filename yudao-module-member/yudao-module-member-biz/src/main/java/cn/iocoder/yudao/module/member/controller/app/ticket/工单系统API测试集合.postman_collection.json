{"info": {"name": "工单系统API测试集合", "description": "用户工单系统的完整API测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:48080/app-api", "type": "string"}, {"key": "token", "value": "your-auth-token-here", "type": "string"}, {"key": "tenantId", "value": "1", "type": "string"}], "item": [{"name": "1. 获取工单类型列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/member/ticket/types", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "types"]}}}, {"name": "2. 创建工单", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"订单支付问题\",\n  \"type\": 1,\n  \"priority\": 2,\n  \"description\": \"我的订单已经支付成功，但是订单状态还是显示未支付，请帮忙处理一下。订单号：202401010001\",\n  \"attachmentUrls\": [\n    \"https://example.com/payment-screenshot.jpg\"\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/member/ticket/create", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "create"]}}}, {"name": "3. 获取工单分页列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/member/ticket/page?pageNo=1&pageSize=10&type=1&status=1", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "page"], "query": [{"key": "pageNo", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "type", "value": "1"}, {"key": "status", "value": "1"}]}}}, {"name": "4. 获取工单详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/member/ticket/get?id=1024", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "get"], "query": [{"key": "id", "value": "1024", "description": "工单ID，需要替换为实际的工单ID"}]}}}, {"name": "5. 提交工单回复", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticketId\": 1024,\n  \"content\": \"感谢您的回复，我已经按照您的建议重新检查了订单状态，现在显示正常了。\",\n  \"attachmentUrls\": [\n    \"https://example.com/order-status-screenshot.jpg\"\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/member/ticket/reply", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "reply"]}}}, {"name": "6. 关闭工单", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/member/ticket/close/1024", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "close", "1024"]}}}, {"name": "7. 评分工单", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticketId\": 1024,\n  \"rating\": 5,\n  \"ratingComment\": \"客服回复很及时，问题解决得很好，非常满意！\"\n}"}, "url": {"raw": "{{baseUrl}}/member/ticket/rate", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "rate"]}}}, {"name": "8. 搜索工单", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/member/ticket/page?pageNo=1&pageSize=10&keyword=支付", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "page"], "query": [{"key": "pageNo", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "keyword", "value": "支付", "description": "搜索关键词"}]}}}, {"name": "9. 按状态筛选工单", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/member/ticket/page?pageNo=1&pageSize=10&status=2", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "page"], "query": [{"key": "pageNo", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "status", "value": "2", "description": "状态：1-待处理，2-处理中，3-待用户回复，4-已解决，5-已关闭"}]}}}, {"name": "10. 按时间范围查询工单", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/member/ticket/page?pageNo=1&pageSize=10&createTime=2024-01-01 00:00:00,2024-01-31 23:59:59", "host": ["{{baseUrl}}"], "path": ["member", "ticket", "page"], "query": [{"key": "pageNo", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "createTime", "value": "2024-01-01 00:00:00,2024-01-31 23:59:59", "description": "时间范围查询，格式：开始时间,结束时间"}]}}}]}