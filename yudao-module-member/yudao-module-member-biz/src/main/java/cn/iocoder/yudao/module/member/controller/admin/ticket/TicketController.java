package cn.iocoder.yudao.module.member.controller.admin.ticket;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.member.controller.admin.ticket.vo.*;
import cn.iocoder.yudao.module.member.dal.dataobject.ticket.TicketDO;
import cn.iocoder.yudao.module.member.dal.dataobject.ticketmessage.TicketMessageDO;
import cn.iocoder.yudao.module.member.service.ticket.TicketService;

@Tag(name = "管理后台 - 用户工单")
@RestController
@RequestMapping("/member/ticket")
@Validated
public class TicketController {

    @Resource
    private TicketService ticketService;

    @PostMapping("/create")
    @Operation(summary = "创建用户工单")
    @PreAuthorize("@ss.hasPermission('member:ticket:create')")
    public CommonResult<Long> createTicket(@Valid @RequestBody TicketSaveReqVO createReqVO) {
        return success(ticketService.createTicket(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户工单")
    @PreAuthorize("@ss.hasPermission('member:ticket:update')")
    public CommonResult<Boolean> updateTicket(@Valid @RequestBody TicketSaveReqVO updateReqVO) {
        ticketService.updateTicket(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户工单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('member:ticket:delete')")
    public CommonResult<Boolean> deleteTicket(@RequestParam("id") Long id) {
        ticketService.deleteTicket(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户工单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:ticket:query')")
    public CommonResult<TicketRespVO> getTicket(@RequestParam("id") Long id) {
        TicketDO ticket = ticketService.getTicket(id);
        return success(BeanUtils.toBean(ticket, TicketRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户工单分页")
    @PreAuthorize("@ss.hasPermission('member:ticket:query')")
    public CommonResult<PageResult<TicketRespVO>> getTicketPage(@Valid TicketPageReqVO pageReqVO) {
        PageResult<TicketDO> pageResult = ticketService.getTicketPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TicketRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户工单 Excel")
    @PreAuthorize("@ss.hasPermission('member:ticket:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTicketExcel(@Valid TicketPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TicketDO> list = ticketService.getTicketPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户工单.xls", "数据", TicketRespVO.class,
                        BeanUtils.toBean(list, TicketRespVO.class));
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新用户工单状态")
    @PreAuthorize("@ss.hasPermission('member:ticket:update')")
    public CommonResult<Boolean> updateTicketStatus(@RequestBody TicketUpdateStatusReqVO updateReqVO) {
        ticketService.updateTicketStatus(updateReqVO.getId(),updateReqVO.getStatus());
        return success(true);
    }

    // ==================== 子表（用户工单信息） ====================

    @GetMapping("/ticket-message/list-by-ticket-id")
    @Operation(summary = "获得用户工单信息列表")
    @Parameter(name = "ticketId", description = "工单编号")
    @PreAuthorize("@ss.hasPermission('member:ticket:query')")
    public CommonResult<List<TicketMessageDO>> getTicketMessageListByTicketId(@RequestParam("ticketId") Long ticketId) {
        return success(ticketService.getTicketMessageListByTicketId(ticketId));
    }

    @PostMapping("/ticket-message/create-message")
    @Operation(summary = "创建用户工单信息")
    @PreAuthorize("@ss.hasPermission('member:ticket:create')")
    public CommonResult<Long> createTicket(@Valid @RequestBody TicketMessageSaveReqVO createReqVO) {
        return success(ticketService.createTicketMessage(createReqVO));
    }

}