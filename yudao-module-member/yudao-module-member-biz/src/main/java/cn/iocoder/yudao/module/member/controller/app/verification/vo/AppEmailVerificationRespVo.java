package cn.iocoder.yudao.module.member.controller.app.verification.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ruoyi-vue-pro
 * @description: 邮箱验证请求
 * @author: DingXiao
 * @create: 2024-12-12 12:18
 **/

@Schema(description = "用户 APP - 邮箱验证 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppEmailVerificationRespVo {

    private String email;

    private String userName;

    private Long userId;
}
