package cn.iocoder.yudao.module.member.controller.app.verification;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.member.controller.app.user.vo.AppMemberUserUpdateReqVO;
import cn.iocoder.yudao.module.member.controller.app.verification.vo.AppEmailVerificationReqVo;
import cn.iocoder.yudao.module.member.controller.app.verification.vo.AppEmailVerificationRespVo;
import cn.iocoder.yudao.module.member.service.verification.EmailVerificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * @program: ruoyi-vue-pro
 * @description: 用户邮箱验证
 * @author: DingXiao
 * @create: 2024-12-12 12:05
 **/

@Tag(name = "用户 APP - 用户邮箱验证")
@RestController
@RequestMapping("/member/email")
@Validated
@Slf4j
public class AppEmailVerificationController {

    @Autowired
    private EmailVerificationService emailVerificationService;

    @PostMapping("/verify")
    @Operation(summary = "验证邮箱")
    @PermitAll
    public CommonResult<AppEmailVerificationRespVo> verifyEmail(@RequestBody @Valid AppEmailVerificationReqVo reqVO) {
        //emailVerificationService.verify(reqVO);
        return success(emailVerificationService.verify(reqVO));
    }

    @PostMapping("/resend")
    @Operation(summary = "重发验证邮件")
    public CommonResult<Boolean> resend(@RequestBody @Valid AppEmailVerificationReqVo reqVO) {
        emailVerificationService.resend(reqVO.getUserId());
        return success(true);
    }

}
