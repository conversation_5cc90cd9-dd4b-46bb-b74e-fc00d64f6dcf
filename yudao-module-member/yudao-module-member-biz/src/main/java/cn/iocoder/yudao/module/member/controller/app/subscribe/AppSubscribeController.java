package cn.iocoder.yudao.module.member.controller.app.subscribe;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.member.controller.app.subscribe.vo.AppSubscribeReqVO;
import cn.iocoder.yudao.module.member.controller.app.subscribe.vo.AppUnsubscribeReqVO;
import cn.iocoder.yudao.module.member.service.subscribe.SubscribeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * @program: ruoyi-vue-pro
 * @description: App-订阅控制器
 * @author: DingXiao
 * @create: 2025-03-07 18:17
 **/
@Tag(name = "用户 APP - 订阅")
@RestController
@RequestMapping("/member/subscribe")
@Validated
public class AppSubscribeController {

    @Resource
    private SubscribeService subscribeService;

    @PostMapping("/create")
    @Operation(summary = "创建订阅")
    @PermitAll
    public CommonResult<Long> createAddress(@Valid AppSubscribeReqVO createReqVO) {
        return success(subscribeService.createSubscribe(createReqVO));
    }

    @DeleteMapping("/unsubscribe")
    @Operation(summary = "取消订阅")
    @PermitAll
    public CommonResult<Boolean> deleteAddress(AppUnsubscribeReqVO reqVO) {
        subscribeService.unsubscribe(reqVO);
        return success(true);
    }
}
