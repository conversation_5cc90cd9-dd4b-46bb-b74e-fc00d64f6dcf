package cn.iocoder.yudao.module.member.controller.admin.inquiry;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.member.controller.admin.inquiry.vo.*;
import cn.iocoder.yudao.module.member.dal.dataobject.inquiry.MemberInquiryDO;
import cn.iocoder.yudao.module.member.service.inquiry.MemberInquiryService;

@Tag(name = "管理后台 - 用户咨询")
@RestController
@RequestMapping("/member/inquiry")
@Validated
public class MemberInquiryController {

    @Resource
    private MemberInquiryService inquiryService;

    @PostMapping("/create")
    @Operation(summary = "创建用户咨询")
    @PreAuthorize("@ss.hasPermission('member:inquiry:create')")
    public CommonResult<Long> createInquiry(@Valid @RequestBody MemberInquirySaveReqVO createReqVO) {
        return success(inquiryService.createInquiry(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户咨询")
    @PreAuthorize("@ss.hasPermission('member:inquiry:update')")
    public CommonResult<Boolean> updateInquiry(@Valid @RequestBody MemberInquirySaveReqVO updateReqVO) {
        inquiryService.updateInquiry(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户咨询")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('member:inquiry:delete')")
    public CommonResult<Boolean> deleteInquiry(@RequestParam("id") Long id) {
        inquiryService.deleteInquiry(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户咨询")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:inquiry:query')")
    public CommonResult<MemberInquiryRespVO> getInquiry(@RequestParam("id") Long id) {
        MemberInquiryDO inquiry = inquiryService.getInquiry(id);
        return success(BeanUtils.toBean(inquiry, MemberInquiryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户咨询分页")
    @PreAuthorize("@ss.hasPermission('member:inquiry:query')")
    public CommonResult<PageResult<MemberInquiryRespVO>> getInquiryPage(@Valid MemberInquiryPageReqVO pageReqVO) {
        PageResult<MemberInquiryDO> pageResult = inquiryService.getInquiryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MemberInquiryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户咨询 Excel")
    @PreAuthorize("@ss.hasPermission('member:inquiry:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryExcel(@Valid MemberInquiryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberInquiryDO> list = inquiryService.getInquiryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户咨询.xls", "数据", MemberInquiryRespVO.class,
                        BeanUtils.toBean(list, MemberInquiryRespVO.class));
    }

}