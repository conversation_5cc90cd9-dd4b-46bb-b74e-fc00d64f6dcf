package cn.iocoder.yudao.module.product.framework.onebound;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.product.controller.app.property.vo.value.AppProductPropertyValueDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.framework.onebound.convert.OneBoundConvert;
import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundDetailRespDTO;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OneBound 真实数据测试
 *
 * <AUTHOR>
 */
public class OneBoundRealDataTest {

    private final OneBoundConvert convert = OneBoundConvert.INSTANCE;

    @Test
    public void testRealDataConvert() {
        // 使用真实的OneBound数据进行测试
        OneBoundDetailRespDTO.Item item = createRealDataItem();
        
        // 转换为商品详情
        AppProductSpuDetailRespVO productDetail = convert.convertProductDetail(
            createRealDataResponse(item), "taobao");
        
        // 打印转换结果
        System.out.println("=== 商品基本信息 ===");
        System.out.println("商品名称: " + productDetail.getName());
        System.out.println("商品价格: " + productDetail.getPrice() + "分");
        System.out.println("SKU数量: " + (productDetail.getSkus() != null ? productDetail.getSkus().size() : 0));
        
        System.out.println("\n=== SKU详细信息 ===");
        if (productDetail.getSkus() != null) {
            for (int i = 0; i < productDetail.getSkus().size(); i++) {
                AppProductSpuDetailRespVO.Sku sku = productDetail.getSkus().get(i);
                System.out.println("\n--- SKU " + (i + 1) + " ---");
                System.out.println("ID: " + sku.getId());
                System.out.println("价格: " + sku.getPrice() + "分");
                System.out.println("库存: " + sku.getStock());
                System.out.println("属性:");
                
                if (sku.getProperties() != null) {
                    for (AppProductPropertyValueDetailRespVO prop : sku.getProperties()) {
                        System.out.println("  - " + prop.getPropertyName() + " (ID:" + prop.getPropertyId() + "): " 
                                         + prop.getValueName() + " (ID:" + prop.getValueId() + ")");
                    }
                } else {
                    System.out.println("  无属性信息");
                }
            }
        }
        
        // 验证属性分组
        System.out.println("\n=== 属性分组验证 ===");
        verifyPropertyGrouping(productDetail.getSkus());
        
        // 输出JSON格式便于前端测试
        System.out.println("\n=== JSON格式输出 ===");
        System.out.println(JsonUtils.toJsonPrettyString(productDetail.getSkus()));
    }

    private OneBoundDetailRespDTO createRealDataResponse(OneBoundDetailRespDTO.Item item) {
        OneBoundDetailRespDTO response = new OneBoundDetailRespDTO();
        response.setItem(item);
        response.setErrorCode("0000");
        return response;
    }

    private OneBoundDetailRespDTO.Item createRealDataItem() {
        OneBoundDetailRespDTO.Item item = new OneBoundDetailRespDTO.Item();
        
        // 基本信息
        item.setNumIid("662092201146");
        item.setTitle("夏科电脑键盘鼠标套装有线台式笔记本办公专用静音无声打字外接usb家用商务电竞游戏真机械手感键鼠三件套");
        item.setPrice("17.9");
        item.setOrginalPrice("17.9");
        item.setPicUrl("https://img.alicdn.com/imgextra/i3/2438280281/O1CN01KVOEj21DwkG7ZiU6g_!!0-item_pic.jpg");
        item.setNick("夏科数码旗舰店");
        
        // 设置props_list
        Map<String, String> propsList = new HashMap<>();
        propsList.put("1627207:16990330706", "颜色分类:【雅黑-限时特惠】");
        propsList.put("1627207:10679502937", "颜色分类:【白键-悬浮炫光】");
        propsList.put("1627207:10679502939", "颜色分类:【黑键-悬浮炫光】");
        propsList.put("1627207:10679502941", "颜色分类:【合金-悬浮炫光】");
        propsList.put("1627207:16990333835", "颜色分类:【雅黑-限时特惠】+鼠标+送鼠标垫");
        propsList.put("1627207:16990393154", "颜色分类:【白键-悬浮炫光】+鼠标+送鼠标垫");
        propsList.put("1627207:16990410021", "颜色分类:【黑键-悬浮炫光】+鼠标+送鼠标垫");
        propsList.put("1627207:16990384205", "颜色分类:【合金-悬浮炫光】+鼠标+送鼠标垫");
        propsList.put("5919063:6536025", "套餐类型:官方标配");
        item.setPropsList(propsList);
        
        // 设置SKU数据
        OneBoundDetailRespDTO.Skus skus = new OneBoundDetailRespDTO.Skus();
        List<OneBoundDetailRespDTO.Sku> skuList = new ArrayList<>();
        
        // SKU 1: 雅黑-限时特惠 + 官方标配
        OneBoundDetailRespDTO.Sku sku1 = new OneBoundDetailRespDTO.Sku();
        sku1.setSkuId("4946331086473");
        sku1.setPrice("17.9");
        sku1.setOrginalPrice("17.9");
        sku1.setQuantity("200");
        sku1.setProperties("1627207:16990330706;5919063:6536025");
        skuList.add(sku1);
        
        // SKU 2: 白键-悬浮炫光 + 官方标配
        OneBoundDetailRespDTO.Sku sku2 = new OneBoundDetailRespDTO.Sku();
        sku2.setSkuId("4946331086474");
        sku2.setPrice("29.9");
        sku2.setOrginalPrice("29.9");
        sku2.setQuantity("0");
        sku2.setProperties("1627207:10679502937;5919063:6536025");
        skuList.add(sku2);
        
        // SKU 3: 黑键-悬浮炫光 + 官方标配
        OneBoundDetailRespDTO.Sku sku3 = new OneBoundDetailRespDTO.Sku();
        sku3.setSkuId("4946331086475");
        sku3.setPrice("29.9");
        sku3.setOrginalPrice("29.9");
        sku3.setQuantity("200");
        sku3.setProperties("1627207:10679502939;5919063:6536025");
        skuList.add(sku3);
        
        // SKU 4: 合金-悬浮炫光 + 官方标配
        OneBoundDetailRespDTO.Sku sku4 = new OneBoundDetailRespDTO.Sku();
        sku4.setSkuId("4946331086476");
        sku4.setPrice("39.9");
        sku4.setOrginalPrice("39.9");
        sku4.setQuantity("200");
        sku4.setProperties("1627207:10679502941;5919063:6536025");
        skuList.add(sku4);
        
        // SKU 5: 雅黑-限时特惠+鼠标+送鼠标垫 + 官方标配
        OneBoundDetailRespDTO.Sku sku5 = new OneBoundDetailRespDTO.Sku();
        sku5.setSkuId("4946331086477");
        sku5.setPrice("27.9");
        sku5.setOrginalPrice("27.9");
        sku5.setQuantity("200");
        sku5.setProperties("1627207:16990333835;5919063:6536025");
        skuList.add(sku5);
        
        skus.setSku(skuList);
        item.setSkus(skus);
        
        return item;
    }
    
    private void verifyPropertyGrouping(List<AppProductSpuDetailRespVO.Sku> skus) {
        Map<String, Map<String, Integer>> propertyStats = new HashMap<>();
        
        if (skus != null) {
            for (AppProductSpuDetailRespVO.Sku sku : skus) {
                if (sku.getProperties() != null) {
                    for (AppProductPropertyValueDetailRespVO prop : sku.getProperties()) {
                        String propertyName = prop.getPropertyName();
                        String valueName = prop.getValueName();
                        
                        propertyStats.computeIfAbsent(propertyName, k -> new HashMap<>())
                                   .merge(valueName, 1, Integer::sum);
                    }
                }
            }
        }
        
        // 打印统计结果
        for (Map.Entry<String, Map<String, Integer>> entry : propertyStats.entrySet()) {
            System.out.println(entry.getKey() + ":");
            for (Map.Entry<String, Integer> valueEntry : entry.getValue().entrySet()) {
                System.out.println("  - " + valueEntry.getKey() + " (出现" + valueEntry.getValue() + "次)");
            }
        }
        
        // 验证期望结果
        System.out.println("\n期望的前端展示效果:");
        System.out.println("颜色分类: [雅黑-限时特惠] [白键-悬浮炫光] [黑键-悬浮炫光] [合金-悬浮炫光] [雅黑-限时特惠+鼠标+送鼠标垫]");
        System.out.println("套餐类型: [官方标配]");
    }

}
