package cn.iocoder.yudao.module.product.framework.onebound;

import cn.hutool.core.util.URLUtil;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * OneBound URL构建测试
 *
 * <AUTHOR>
 */
public class OneBoundUrlTest {

    @Test
    public void testBuildSearchUrl() {
        String baseUrl = "https://api-gw.onebound.cn/taobao/item_search/";
        Map<String, Object> params = new HashMap<>();
        params.put("key", "test-key");
        params.put("secret", "test-secret");
        params.put("q", "女装");
        params.put("page", 1);
        params.put("start_price", "0");
        params.put("end_price", "0");
        params.put("cat", "0");
        params.put("discount_only", "");
        params.put("sort", "");
        params.put("seller_info", "");
        params.put("nick", "");
        params.put("ppath", "");
        params.put("imgid", "");
        params.put("filter", "");
        params.put("lang", "zh-CN");

        String fullUrl = buildUrlWithParams(baseUrl, params);
        System.out.println("搜索URL: " + fullUrl);
    }

    @Test
    public void testBuildDetailUrl() {
        String baseUrl = "https://api-gw.onebound.cn/taobao/item_get/";
        Map<String, Object> params = new HashMap<>();
        params.put("key", "test-key");
        params.put("secret", "test-secret");
        params.put("num_iid", "652874751412");
        params.put("is_promotion", "1");
        params.put("lang", "zh-CN");

        String fullUrl = buildUrlWithParams(baseUrl, params);
        System.out.println("详情URL: " + fullUrl);
    }

    private String buildUrlWithParams(String baseUrl, Map<String, Object> params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        if (!params.isEmpty()) {
            urlBuilder.append("?");
            boolean first = true;
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (!first) {
                    urlBuilder.append("&");
                }
                String key = entry.getKey();
                String value = entry.getValue() != null ? entry.getValue().toString() : "";
                urlBuilder.append(URLUtil.encode(key)).append("=").append(URLUtil.encode(value));
                first = false;
            }
        }
        return urlBuilder.toString();
    }

}
