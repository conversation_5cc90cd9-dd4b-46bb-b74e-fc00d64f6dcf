package cn.iocoder.yudao.module.product.framework.crawler.service;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchDetailReqVO;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchPageReqVO;
import cn.iocoder.yudao.module.product.framework.crawler.config.CrawlerProperties;
import cn.iocoder.yudao.module.product.service.search.CrawlerServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 爬虫系统服务测试类
 *
 * <AUTHOR>
 */
public class CrawlerServiceTest extends BaseDbUnitTest {

    @Mock
    private CrawlerProperties crawlerProperties;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private CrawlerServiceImpl crawlerService;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        crawlerProperties = new CrawlerProperties();
        crawlerProperties.setBaseUrl("http://localhost:8000/api/v1/upstream");
        crawlerProperties.setToken("test-token");
        crawlerProperties.setCacheEnabled(true);
    }

    @Test
    void testSearchProducts() {
        // 准备测试数据
        AppSearchPageReqVO reqVO = new AppSearchPageReqVO();
        reqVO.setKeyword("iPhone 15");
        reqVO.setPlatform("taobao");
        reqVO.setLanguage("zh");
        reqVO.setPageNo(1);
        reqVO.setPageSize(10);

        // 由于需要真实的爬虫系统响应，这里只测试参数构建逻辑
        assertNotNull(reqVO.getKeyword());
        assertEquals("taobao", reqVO.getPlatform());
        assertEquals("zh", reqVO.getLanguage());
    }

    @Test
    void testGetProductDetail() {
        // 准备测试数据
        AppSearchDetailReqVO reqVO = new AppSearchDetailReqVO();
        reqVO.setProductUrl("https://item.taobao.com/item.htm?id=123456789");
        reqVO.setLanguage("zh");

        // 验证请求参数
        assertNotNull(reqVO.getProductUrl());
        assertTrue(reqVO.getProductUrl().contains("taobao.com"));
        assertEquals("zh", reqVO.getLanguage());
    }

}
