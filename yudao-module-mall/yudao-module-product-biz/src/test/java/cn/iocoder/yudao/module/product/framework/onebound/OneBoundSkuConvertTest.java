package cn.iocoder.yudao.module.product.framework.onebound;


import cn.iocoder.yudao.module.product.controller.app.property.vo.value.AppProductPropertyValueDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.framework.onebound.convert.OneBoundConvert;
import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundDetailRespDTO;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OneBound SKU转换测试
 *
 * <AUTHOR>
 */
public class OneBoundSkuConvertTest {

    private final OneBoundConvert convert = OneBoundConvert.INSTANCE;

    @Test
    public void testSkuConvert() {
        // 模拟OneBound返回的SKU数据和props_list
        List<OneBoundDetailRespDTO.Sku> skuList = createMockSkuList();
        Map<String, String> propsList = createMockPropsList();

        // 转换SKU
        List<AppProductSpuDetailRespVO.Sku> convertedSkus = new ArrayList<>();
        for (OneBoundDetailRespDTO.Sku sku : skuList) {
            AppProductSpuDetailRespVO.Sku convertedSku = convert.convertSku(sku, skuList, propsList);
            convertedSkus.add(convertedSku);
        }
        
        // 打印结果
        System.out.println("转换后的SKU数量: " + convertedSkus.size());
        for (int i = 0; i < convertedSkus.size(); i++) {
            AppProductSpuDetailRespVO.Sku sku = convertedSkus.get(i);
            System.out.println("\n=== SKU " + (i + 1) + " ===");
            System.out.println("ID: " + sku.getId());
            System.out.println("价格: " + sku.getPrice() + "分");
            System.out.println("库存: " + sku.getStock());
            System.out.println("属性:");
            if (sku.getProperties() != null) {
                for (AppProductPropertyValueDetailRespVO prop : sku.getProperties()) {
                    System.out.println("  - " + prop.getPropertyName() + " (ID:" + prop.getPropertyId() + "): " 
                                     + prop.getValueName() + " (ID:" + prop.getValueId() + ")");
                }
            }
        }
        
        // 验证属性分组
        System.out.println("\n=== 属性分组验证 ===");
        verifyPropertyGrouping(convertedSkus);
    }

    private List<OneBoundDetailRespDTO.Sku> createMockSkuList() {
        List<OneBoundDetailRespDTO.Sku> skuList = new ArrayList<>();

        // 按照真实OneBound数据格式创建SKU
        // SKU 1: 雅黑-限时特惠 + 官方标配
        OneBoundDetailRespDTO.Sku sku1 = new OneBoundDetailRespDTO.Sku();
        sku1.setSkuId("4946331086473");
        sku1.setPrice("17.9");
        sku1.setOrginalPrice("17.9");
        sku1.setQuantity("200");
        sku1.setProperties("1627207:16990330706;5919063:6536025");
        sku1.setPropertiesName("1627207:16990330706:颜色分类:【雅黑-限时特惠】;5919063:6536025:套餐类型:官方标配");
        skuList.add(sku1);

        // SKU 2: 白键-悬浮炫光 + 官方标配
        OneBoundDetailRespDTO.Sku sku2 = new OneBoundDetailRespDTO.Sku();
        sku2.setSkuId("4946331086474");
        sku2.setPrice("29.9");
        sku2.setOrginalPrice("29.9");
        sku2.setQuantity("0");
        sku2.setProperties("1627207:10679502937;5919063:6536025");
        sku2.setPropertiesName("1627207:10679502937:颜色分类:【白键-悬浮炫光】;5919063:6536025:套餐类型:官方标配");
        skuList.add(sku2);

        // SKU 3: 合金-悬浮炫光 + 官方标配
        OneBoundDetailRespDTO.Sku sku3 = new OneBoundDetailRespDTO.Sku();
        sku3.setSkuId("4946331086476");
        sku3.setPrice("39.9");
        sku3.setOrginalPrice("39.9");
        sku3.setQuantity("200");
        sku3.setProperties("1627207:10679502941;5919063:6536025");
        sku3.setPropertiesName("1627207:10679502941:颜色分类:【合金-悬浮炫光】;5919063:6536025:套餐类型:官方标配");
        skuList.add(sku3);

        // SKU 4: 雅黑-限时特惠 + 鼠标+送鼠标垫
        OneBoundDetailRespDTO.Sku sku4 = new OneBoundDetailRespDTO.Sku();
        sku4.setSkuId("4946331086477");
        sku4.setPrice("27.9");
        sku4.setOrginalPrice("27.9");
        sku4.setQuantity("200");
        sku4.setProperties("1627207:16990333835;5919063:6536025");
        sku4.setPropertiesName("1627207:16990333835:颜色分类:【雅黑-限时特惠】+鼠标+送鼠标垫;5919063:6536025:套餐类型:官方标配");
        skuList.add(sku4);

        return skuList;
    }

    private Map<String, String> createMockPropsList() {
        Map<String, String> propsList = new HashMap<>();

        // 按照真实OneBound数据格式创建props_list映射
        propsList.put("1627207:16990330706", "颜色分类:【雅黑-限时特惠】");
        propsList.put("1627207:10679502937", "颜色分类:【白键-悬浮炫光】");
        propsList.put("1627207:10679502939", "颜色分类:【黑键-悬浮炫光】");
        propsList.put("1627207:10679502941", "颜色分类:【合金-悬浮炫光】");
        propsList.put("1627207:16990333835", "颜色分类:【雅黑-限时特惠】+鼠标+送鼠标垫");
        propsList.put("1627207:16990393154", "颜色分类:【白键-悬浮炫光】+鼠标+送鼠标垫");
        propsList.put("1627207:16990410021", "颜色分类:【黑键-悬浮炫光】+鼠标+送鼠标垫");
        propsList.put("1627207:16990384205", "颜色分类:【合金-悬浮炫光】+鼠标+送鼠标垫");
        propsList.put("5919063:6536025", "套餐类型:官方标配");

        return propsList;
    }
    
    private void verifyPropertyGrouping(List<AppProductSpuDetailRespVO.Sku> skus) {
        // 统计每个属性的不同值
        for (AppProductSpuDetailRespVO.Sku sku : skus) {
            if (sku.getProperties() != null) {
                for (AppProductPropertyValueDetailRespVO prop : sku.getProperties()) {
                    System.out.println(prop.getPropertyName() + " (ID:" + prop.getPropertyId() + "): " 
                                     + prop.getValueName() + " (ID:" + prop.getValueId() + ")");
                }
                System.out.println("---");
            }
        }
    }

}
