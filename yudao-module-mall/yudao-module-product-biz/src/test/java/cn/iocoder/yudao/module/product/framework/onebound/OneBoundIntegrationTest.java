package cn.iocoder.yudao.module.product.framework.onebound;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchDetailReqVO;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchPageReqVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuRespVO;
import cn.iocoder.yudao.module.product.service.search.OneBoundSearchServiceImpl;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OneBound集成测试
 * 
 * 注意：此测试需要有效的OneBound API密钥才能运行
 * 请在系统配置中设置正确的onebound.key和onebound.secret
 *
 * <AUTHOR>
 */
@SpringBootTest
@Disabled("需要有效的OneBound API密钥才能运行")
public class OneBoundIntegrationTest extends BaseDbUnitTest {

    @Resource
    private OneBoundSearchServiceImpl oneBoundSearchService;

    @Test
    public void testSearchTaobaoProducts() {
        // 准备测试数据
        AppSearchPageReqVO reqVO = new AppSearchPageReqVO();
        reqVO.setKeyword("iPhone 15");
        reqVO.setPlatform("taobao");
        reqVO.setPageNo(1);
        reqVO.setPageSize(10);

        // 执行搜索
        PageResult<AppProductSpuRespVO> result = oneBoundSearchService.searchProducts(reqVO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertTrue(result.getTotal() > 0);
        assertTrue(result.getList().size() > 0);

        // 验证第一个商品数据
        AppProductSpuRespVO firstProduct = result.getList().get(0);
        assertNotNull(firstProduct.getName());
//        assertNotNull(firstProduct.getSourceId());
        assertEquals("taobao", firstProduct.getSource());
        assertTrue(firstProduct.getPrice() > 0);

        System.out.println("淘宝搜索测试成功，返回商品数量: " + result.getList().size());
        System.out.println("第一个商品: " + firstProduct.getName());
    }

    @Test
    public void testSearchJdProducts() {
        // 准备测试数据
        AppSearchPageReqVO reqVO = new AppSearchPageReqVO();
        reqVO.setKeyword("华为手机");
        reqVO.setPlatform("jd");
        reqVO.setPageNo(1);
        reqVO.setPageSize(10);

        // 执行搜索
        PageResult<AppProductSpuRespVO> result = oneBoundSearchService.searchProducts(reqVO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertTrue(result.getTotal() > 0);
        assertTrue(result.getList().size() > 0);

        // 验证第一个商品数据
        AppProductSpuRespVO firstProduct = result.getList().get(0);
        assertNotNull(firstProduct.getName());
//        assertNotNull(firstProduct.getSourceId());
        assertEquals("jd", firstProduct.getSource());
        assertTrue(firstProduct.getPrice() > 0);

        System.out.println("京东搜索测试成功，返回商品数量: " + result.getList().size());
        System.out.println("第一个商品: " + firstProduct.getName());
    }

    @Test
    public void testGetTaobaoProductDetail() {
        // 准备测试数据（使用一个已知的淘宝商品ID）
        AppSearchDetailReqVO reqVO = new AppSearchDetailReqVO();
        reqVO.setId("672112332529"); // 示例商品ID
        reqVO.setPlatform("taobao");

        // 执行获取详情
        AppProductSpuDetailRespVO result = oneBoundSearchService.getProductDetailById(reqVO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getName());
//        assertNotNull(result.getSourceId());
        assertEquals("taobao", result.getSource());
        assertTrue(result.getPrice() > 0);
        assertNotNull(result.getSliderPicUrls());

        System.out.println("淘宝详情测试成功");
        System.out.println("商品名称: " + result.getName());
        System.out.println("商品价格: " + result.getPrice() + "分");
        System.out.println("图片数量: " + result.getSliderPicUrls().size());
    }

    @Test
    public void testGetJdProductDetail() {
        // 准备测试数据（使用一个已知的京东商品ID）
        AppSearchDetailReqVO reqVO = new AppSearchDetailReqVO();
        reqVO.setId("100012043978"); // 示例商品ID
        reqVO.setPlatform("jd");

        // 执行获取详情
        AppProductSpuDetailRespVO result = oneBoundSearchService.getProductDetailById(reqVO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getName());
//        assertNotNull(result.getSourceId());
        assertEquals("jd", result.getSource());
        assertTrue(result.getPrice() > 0);

        System.out.println("京东详情测试成功");
        System.out.println("商品名称: " + result.getName());
        System.out.println("商品价格: " + result.getPrice() + "分");
    }

    @Test
    public void testGetProductByUrl() {
        // 测试淘宝URL解析
        String taobaoUrl = "https://detail.tmall.com/item.htm?id=672112332529";
        AppProductSpuDetailRespVO result = oneBoundSearchService.getProductByUrl(taobaoUrl);
        
        assertNotNull(result);
        assertEquals("taobao", result.getSource());
        System.out.println("淘宝URL解析测试成功: " + result.getName());

        // 测试京东URL解析
        String jdUrl = "https://item.jd.com/100012043978.html";
        result = oneBoundSearchService.getProductByUrl(jdUrl);
        
        assertNotNull(result);
        assertEquals("jd", result.getSource());
        System.out.println("京东URL解析测试成功: " + result.getName());
    }

}
