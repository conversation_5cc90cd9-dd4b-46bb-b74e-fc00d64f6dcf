package cn.iocoder.yudao.module.product.framework.onebound.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;

import cn.iocoder.yudao.module.product.controller.app.property.vo.value.AppProductPropertyValueDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchPageReqVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuRespVO;
import cn.iocoder.yudao.module.product.framework.onebound.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.*;

/**
 * OneBound数据转换器
 *
 * <AUTHOR>
 */
@Mapper
public interface OneBoundConvert {

    OneBoundConvert INSTANCE = Mappers.getMapper(OneBoundConvert.class);

    /**
     * 转换搜索请求参数
     */
    default OneBoundSearchReqDTO convertSearchReq(AppSearchPageReqVO reqVO) {
        OneBoundSearchReqDTO dto = new OneBoundSearchReqDTO();
        dto.setQ(reqVO.getKeyword());
        dto.setPage(reqVO.getPageNo());
        dto.setPageSize(reqVO.getPageSize());
        
        // 价格范围转换
        if (reqVO.getPriceMin() != null) {
            dto.setStartPrice(String.valueOf(reqVO.getPriceMin()));
        }
        if (reqVO.getPriceMax() != null) {
            dto.setEndPrice(String.valueOf(reqVO.getPriceMax()));
        }
        
        // 排序转换
        if (StrUtil.isNotBlank(reqVO.getSort())) {
            dto.setSort(reqVO.getSort());
        }
        
        // 语言设置
        if (StrUtil.isNotBlank(reqVO.getLanguage())) {
            dto.setLang(reqVO.getLanguage());
        }
        
        return dto;
    }

    /**
     * 转换商品详情请求参数
     */
    default OneBoundDetailReqDTO convertDetailReq(String productId, String language) {
        OneBoundDetailReqDTO dto = new OneBoundDetailReqDTO();
        dto.setNumIid(productId);
        if (StrUtil.isNotBlank(language)) {
            dto.setLang(language);
        }
        return dto;
    }

    /**
     * 转换搜索响应为分页结果
     */
    default PageResult<AppProductSpuRespVO> convertSearchResp(OneBoundSearchRespDTO respDTO, String platform) {
        if (respDTO == null || respDTO.getItems() == null) {
            return PageResult.empty();
        }

        OneBoundSearchRespDTO.Items items = respDTO.getItems();
        List<AppProductSpuRespVO> list = convertProductList(items.getItem(), platform);
        
        // 计算总数和页数
        long total = NumberUtil.parseLong(items.getTotalResults(), 0L);
        
        return new PageResult<>(list, total);
    }

    /**
     * 转换商品列表
     */
    default List<AppProductSpuRespVO> convertProductList(List<OneBoundSearchRespDTO.Item> items, String platform) {
        if (CollUtil.isEmpty(items)) {
            return Collections.emptyList();
        }

        return CollectionUtils.convertList(items, item -> convertProduct(item, platform));
    }

    /**
     * 转换单个商品
     */
    default AppProductSpuRespVO convertProduct(OneBoundSearchRespDTO.Item item, String platform) {
        AppProductSpuRespVO vo = new AppProductSpuRespVO();
        
        // 基本信息
        vo.setName(item.getTitle());
        vo.setPicUrl(normalizeImageUrl(item.getPicUrl()));
        vo.setSourceLink(item.getDetailUrl());
        vo.setSource(platform);
        
        // 价格处理（转换为分）
        if (StrUtil.isNotBlank(item.getPrice())) {
            vo.setPrice(convertPriceToFen(item.getPrice()));
        }
        if (StrUtil.isNotBlank(item.getOrginalPrice())) {
            vo.setMarketPrice(convertPriceToFen(item.getOrginalPrice()));
        }
        
        // 商品ID和链接
        vo.setId(NumberUtil.parseLong(item.getNumIid()));
        
        // 店铺信息
        if ("jd".equals(platform)) {
            vo.setShopName(item.getSeller());
            if (StrUtil.isNotBlank(item.getReviews())) {
                vo.setScores(NumberUtil.parseInt(item.getReviews(), 5));
            }
        } else {
            // 淘宝的地区信息
            if (StrUtil.isNotBlank(item.getArea())) {
                vo.setShopName(item.getArea());
            }
        }
        
        // 默认值设置
        vo.setSpecType(false);
        vo.setStock(999); // 默认库存
        vo.setType(1); // 普通商品
        vo.setFreight(0); // 默认运费
        vo.setSalesCount(0); // 默认销量
        vo.setNewest(false);
        vo.setSale(true);
        vo.setHot(false);
        
        return vo;
    }

    /**
     * 转换商品详情
     */
    default AppProductSpuDetailRespVO convertProductDetail(OneBoundDetailRespDTO respDTO, String platform) {
        if (respDTO == null || respDTO.getItem() == null) {
            return null;
        }

        OneBoundDetailRespDTO.Item item = respDTO.getItem();
        AppProductSpuDetailRespVO vo = new AppProductSpuDetailRespVO();
        
        // 基本信息
        vo.setName(item.getTitle());
        vo.setIntroduction(item.getDescShort());
        vo.setPicUrl(normalizeImageUrl(item.getPicUrl()));
        vo.setSourceLink(item.getDetailUrl());
        vo.setSource(platform);
        vo.setId(NumberUtil.parseLong(item.getNumIid()));
        
        // 价格处理
        if (StrUtil.isNotBlank(item.getPrice())) {
            vo.setPrice(convertPriceToFen(item.getPrice()));
        }
        if (StrUtil.isNotBlank(item.getOrginalPrice())) {
            vo.setMarketPrice(convertPriceToFen(item.getOrginalPrice()));
        }
        
        // 库存
        if (item.getNum() != null) {
            vo.setStock(item.getNum());
        } else {
            vo.setStock(999); // 默认库存
        }
        
        // 图片处理
        List<String> sliderPicUrls = new ArrayList<>();
        if (CollUtil.isNotEmpty(item.getItemImgs())) {
            for (OneBoundDetailRespDTO.ItemImg img : item.getItemImgs()) {
                sliderPicUrls.add(normalizeImageUrl(img.getUrl()));
            }
        }
        vo.setSliderPicUrls(sliderPicUrls);
        
        // 描述图片
        if (CollUtil.isNotEmpty(item.getDescImg())) {
            List<String> descImages = CollectionUtils.convertList(item.getDescImg(), this::normalizeImageUrl);
            // 可以将描述图片拼接成HTML格式的描述
            StringBuilder descBuilder = new StringBuilder();
            for (String imgUrl : descImages) {
                descBuilder.append("<img src=\"").append(imgUrl).append("\" style=\"width:100%;\" />");
            }
            vo.setDescription(descBuilder.toString());
        }
        
        // 店铺信息
        if (item.getSellerInfo() != null) {
            vo.setShopName(item.getSellerInfo().getShopName());
        } else if (StrUtil.isNotBlank(item.getNick())) {
            vo.setShopName(item.getNick());
        }
        
        // SKU处理
        List<AppProductSpuDetailRespVO.Sku> skus = convertSkus(item.getSkus(), item.getPropsList());
        vo.setSkus(skus);

        // 如果有SKU，设置商品的最低价格
        if (!CollUtil.isEmpty(skus)) {
            Integer minPrice = skus.stream()
                    .filter(sku -> sku.getPrice() != null && sku.getPrice() > 0)
                    .mapToInt(AppProductSpuDetailRespVO.Sku::getPrice)
                    .min()
                    .orElse(vo.getPrice() != null ? vo.getPrice() : 0);
            if (minPrice > 0) {
                vo.setPrice(minPrice);
            }
        }
        
        // 默认值设置
        vo.setSpecType(!CollUtil.isEmpty(skus));
        vo.setType(1);
        vo.setFreight(0);
        vo.setSalesCount(NumberUtil.parseInt(item.getTotalSold(), 0));
        vo.setScores(5); // 默认评分
        vo.setNewest(false);
        vo.setSale(true);
        vo.setHot(false);
        
        return vo;
    }

    /**
     * 转换SKU列表
     */
    default List<AppProductSpuDetailRespVO.Sku> convertSkus(OneBoundDetailRespDTO.Skus skus, Map<String, String> propsList) {
        if (skus == null || CollUtil.isEmpty(skus.getSku())) {
            return Collections.emptyList();
        }

        return CollectionUtils.convertList(skus.getSku(), sku -> convertSku(sku, skus.getSku(), propsList));
    }

    /**
     * 转换单个SKU
     */
    default AppProductSpuDetailRespVO.Sku convertSku(OneBoundDetailRespDTO.Sku sku, List<OneBoundDetailRespDTO.Sku> allSkus, Map<String, String> propsList) {
        AppProductSpuDetailRespVO.Sku vo = new AppProductSpuDetailRespVO.Sku();

        // SKU ID处理
        if (StrUtil.isNotBlank(sku.getSkuId())) {
            try {
                vo.setId(Long.parseLong(sku.getSkuId()));
            } catch (NumberFormatException e) {
                // 如果SKU ID不是数字，使用哈希值作为ID
                vo.setId((long) Math.abs(sku.getSkuId().hashCode()));
            }
        }

        // 价格处理
        if (StrUtil.isNotBlank(sku.getPrice())) {
            vo.setPrice(convertPriceToFen(sku.getPrice()));
            vo.setVipPrice(convertPriceToFen(sku.getPrice())); // VIP价格暂时设置为相同
        }
        if (StrUtil.isNotBlank(sku.getOrginalPrice())) {
            vo.setMarketPrice(convertPriceToFen(sku.getOrginalPrice()));
        }

        // 库存
        if (StrUtil.isNotBlank(sku.getQuantity())) {
            vo.setStock(NumberUtil.parseInt(sku.getQuantity(), 0));
        }

        // 属性处理 - 使用props_list进行正确解析
        List<AppProductPropertyValueDetailRespVO> properties = parseSkuPropertiesV3(sku, allSkus, propsList);
        vo.setProperties(properties);

        // 默认值设置
        vo.setWeight(0.0);
        vo.setVolume(0.0);

        return vo;
    }

    /**
     * 解析SKU属性 V2版本 - 支持统一的属性映射
     */
    default List<AppProductPropertyValueDetailRespVO> parseSkuPropertiesV2(OneBoundDetailRespDTO.Sku currentSku, List<OneBoundDetailRespDTO.Sku> allSkus) {
        List<AppProductPropertyValueDetailRespVO> result = new ArrayList<>();

        if (StrUtil.isBlank(currentSku.getProperties()) && StrUtil.isBlank(currentSku.getPropertiesName())) {
            return result;
        }

        try {
            // 构建全局属性映射
            Map<String, Long> propertyNameToIdMap = buildPropertyNameToIdMap(allSkus);
            Map<String, Map<String, Long>> propertyValueToIdMap = buildPropertyValueToIdMap(allSkus);

            // 解析当前SKU的属性
            if (StrUtil.isNotBlank(currentSku.getPropertiesName())) {
                // 属性名称格式：颜色分类:【雅黑-限时特惠】;是否无线:否;键数:104键;套餐类型:官方标配
                String[] propertyPairs = currentSku.getPropertiesName().split(";");

                for (String pair : propertyPairs) {
                    if (StrUtil.isNotBlank(pair) && pair.contains(":")) {
                        String[] parts = pair.split(":", 2);
                        if (parts.length == 2) {
                            String propertyName = parts[0].trim();
                            String valueName = parts[1].trim();

                            // 清理属性值中的特殊字符
                            valueName = cleanPropertyValue(valueName);

                            AppProductPropertyValueDetailRespVO property = new AppProductPropertyValueDetailRespVO();

                            // 使用统一的属性ID
                            Long propertyId = propertyNameToIdMap.get(propertyName);
                            if (propertyId == null) {
                                propertyId = (long) (propertyNameToIdMap.size() + 1);
                                propertyNameToIdMap.put(propertyName, propertyId);
                            }
                            property.setPropertyId(propertyId);
                            property.setPropertyName(propertyName);

                            // 使用统一的属性值ID
                            Map<String, Long> valueMap = propertyValueToIdMap.computeIfAbsent(propertyName, k -> new HashMap<>());
                            Long valueId = valueMap.get(valueName);
                            if (valueId == null) {
                                valueId = (long) (valueMap.size() + 1);
                                valueMap.put(valueName, valueId);
                            }
                            property.setValueId(valueId);
                            property.setValueName(valueName);

                            result.add(property);
                        }
                    }
                }
            }

            // 如果没有解析到属性，尝试从properties字段解析
            if (result.isEmpty() && StrUtil.isNotBlank(currentSku.getProperties())) {
                String[] propertyPairs = currentSku.getProperties().split(";");
                long propertyId = 1L;

                for (String pair : propertyPairs) {
                    if (StrUtil.isNotBlank(pair) && pair.contains(":")) {
                        String[] parts = pair.split(":", 2);
                        if (parts.length == 2) {
                            AppProductPropertyValueDetailRespVO property = new AppProductPropertyValueDetailRespVO();
                            property.setPropertyId(propertyId);
                            property.setPropertyName("属性" + propertyId);

                            try {
                                property.setValueId(Long.parseLong(parts[1]));
                            } catch (NumberFormatException e) {
                                property.setValueId((long) Math.abs(parts[1].hashCode()));
                            }
                            property.setValueName(parts[1]);
                            result.add(property);
                            propertyId++;
                        }
                    }
                }
            }

        } catch (Exception e) {
            // 解析失败时，创建一个默认属性
            AppProductPropertyValueDetailRespVO defaultProperty = new AppProductPropertyValueDetailRespVO();
            defaultProperty.setPropertyId(1L);
            defaultProperty.setPropertyName("规格");
            defaultProperty.setValueId(1L);
            defaultProperty.setValueName(StrUtil.isNotBlank(currentSku.getPropertiesName()) ? currentSku.getPropertiesName() : "默认");
            result.add(defaultProperty);
        }

        return result;
    }

    /**
     * 构建属性名称到ID的映射
     */
    default Map<String, Long> buildPropertyNameToIdMap(List<OneBoundDetailRespDTO.Sku> allSkus) {
        Map<String, Long> propertyNameToIdMap = new HashMap<>();
        long propertyIdCounter = 1L;

        for (OneBoundDetailRespDTO.Sku sku : allSkus) {
            if (StrUtil.isNotBlank(sku.getPropertiesName())) {
                String[] propertyPairs = sku.getPropertiesName().split(";");
                for (String pair : propertyPairs) {
                    if (StrUtil.isNotBlank(pair) && pair.contains(":")) {
                        String propertyName = pair.split(":", 2)[0].trim();
                        if (!propertyNameToIdMap.containsKey(propertyName)) {
                            propertyNameToIdMap.put(propertyName, propertyIdCounter++);
                        }
                    }
                }
            }
        }

        return propertyNameToIdMap;
    }

    /**
     * 构建属性值到ID的映射
     */
    default Map<String, Map<String, Long>> buildPropertyValueToIdMap(List<OneBoundDetailRespDTO.Sku> allSkus) {
        Map<String, Map<String, Long>> propertyValueToIdMap = new HashMap<>();

        for (OneBoundDetailRespDTO.Sku sku : allSkus) {
            if (StrUtil.isNotBlank(sku.getPropertiesName())) {
                String[] propertyPairs = sku.getPropertiesName().split(";");
                for (String pair : propertyPairs) {
                    if (StrUtil.isNotBlank(pair) && pair.contains(":")) {
                        String[] parts = pair.split(":", 2);
                        if (parts.length == 2) {
                            String propertyName = parts[0].trim();
                            String valueName = cleanPropertyValue(parts[1].trim());

                            Map<String, Long> valueMap = propertyValueToIdMap.computeIfAbsent(propertyName, k -> new HashMap<>());
                            if (!valueMap.containsKey(valueName)) {
                                valueMap.put(valueName, (long) (valueMap.size() + 1));
                            }
                        }
                    }
                }
            }
        }

        return propertyValueToIdMap;
    }

    /**
     * 解析SKU属性 V3版本 - 使用props_list进行正确解析
     */
    default List<AppProductPropertyValueDetailRespVO> parseSkuPropertiesV3(
            OneBoundDetailRespDTO.Sku currentSku,
            List<OneBoundDetailRespDTO.Sku> allSkus,
            Map<String, String> propsList) {

        List<AppProductPropertyValueDetailRespVO> result = new ArrayList<>();

        if (StrUtil.isBlank(currentSku.getProperties()) || propsList == null || propsList.isEmpty()) {
            return result;
        }

        try {
            // 构建全局属性映射
            Map<String, Long> propertyNameToIdMap = buildPropertyNameToIdMapV3(allSkus, propsList);
            Map<String, Map<String, Long>> propertyValueToIdMap = buildPropertyValueToIdMapV3(allSkus, propsList);

            // 解析当前SKU的properties: "1627207:16990330706;5919063:6536025"
            String[] propertyIds = currentSku.getProperties().split(";");

            for (String propertyId : propertyIds) {
                if (StrUtil.isNotBlank(propertyId)) {
                    // 从props_list中获取完整的属性信息
                    String fullPropertyInfo = propsList.get(propertyId.trim());
                    if (StrUtil.isNotBlank(fullPropertyInfo)) {
                        // fullPropertyInfo格式: "颜色分类:【雅黑-限时特惠】"
                        if (fullPropertyInfo.contains(":")) {
                            String[] parts = fullPropertyInfo.split(":", 2);
                            if (parts.length == 2) {
                                String propertyName = parts[0].trim();
                                String valueName = cleanPropertyValue(parts[1].trim());

                                AppProductPropertyValueDetailRespVO property = new AppProductPropertyValueDetailRespVO();

                                // 使用统一的属性ID
                                Long propId = propertyNameToIdMap.get(propertyName);
                                if (propId == null) {
                                    propId = (long) (propertyNameToIdMap.size() + 1);
                                    propertyNameToIdMap.put(propertyName, propId);
                                }
                                property.setPropertyId(propId);
                                property.setPropertyName(propertyName);

                                // 使用统一的属性值ID
                                Map<String, Long> valueMap = propertyValueToIdMap.computeIfAbsent(propertyName, k -> new HashMap<>());
                                Long valueId = valueMap.get(valueName);
                                if (valueId == null) {
                                    valueId = (long) (valueMap.size() + 1);
                                    valueMap.put(valueName, valueId);
                                }
                                property.setValueId(valueId);
                                property.setValueName(valueName);

                                result.add(property);
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            // 解析失败时，创建一个默认属性
            AppProductPropertyValueDetailRespVO defaultProperty = new AppProductPropertyValueDetailRespVO();
            defaultProperty.setPropertyId(1L);
            defaultProperty.setPropertyName("规格");
            defaultProperty.setValueId(1L);
            defaultProperty.setValueName("默认");
            result.add(defaultProperty);
        }

        return result;
    }

    /**
     * 构建属性名称到ID的映射 V3版本
     */
    default Map<String, Long> buildPropertyNameToIdMapV3(List<OneBoundDetailRespDTO.Sku> allSkus, Map<String, String> propsList) {
        Map<String, Long> propertyNameToIdMap = new HashMap<>();
        long propertyIdCounter = 1L;

        // 遍历所有props_list条目，提取属性名称
        for (String fullPropertyInfo : propsList.values()) {
            if (StrUtil.isNotBlank(fullPropertyInfo) && fullPropertyInfo.contains(":")) {
                String propertyName = fullPropertyInfo.split(":", 2)[0].trim();
                if (!propertyNameToIdMap.containsKey(propertyName)) {
                    propertyNameToIdMap.put(propertyName, propertyIdCounter++);
                }
            }
        }

        return propertyNameToIdMap;
    }

    /**
     * 构建属性值到ID的映射 V3版本
     */
    default Map<String, Map<String, Long>> buildPropertyValueToIdMapV3(List<OneBoundDetailRespDTO.Sku> allSkus, Map<String, String> propsList) {
        Map<String, Map<String, Long>> propertyValueToIdMap = new HashMap<>();

        // 遍历所有props_list条目，提取属性值
        for (String fullPropertyInfo : propsList.values()) {
            if (StrUtil.isNotBlank(fullPropertyInfo) && fullPropertyInfo.contains(":")) {
                String[] parts = fullPropertyInfo.split(":", 2);
                if (parts.length == 2) {
                    String propertyName = parts[0].trim();
                    String valueName = cleanPropertyValue(parts[1].trim());

                    Map<String, Long> valueMap = propertyValueToIdMap.computeIfAbsent(propertyName, k -> new HashMap<>());
                    if (!valueMap.containsKey(valueName)) {
                        valueMap.put(valueName, (long) (valueMap.size() + 1));
                    }
                }
            }
        }

        return propertyValueToIdMap;
    }

    /**
     * 清理属性值中的特殊字符
     */
    default String cleanPropertyValue(String value) {
        if (StrUtil.isBlank(value)) {
            return value;
        }

        // 移除【】符号
        value = value.replaceAll("[【】\\[\\]]", "");

        // 移除多余的空格
        value = value.trim();

        return value;
    }

    /**
     * 标准化图片URL
     */
    default String normalizeImageUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return "";
        }
        
        // 如果URL以//开头，添加https:
        if (url.startsWith("//")) {
            return "https:" + url;
        }
        
        return url;
    }

    /**
     * 价格转换为分
     */
    default Integer convertPriceToFen(String priceStr) {
        if (StrUtil.isBlank(priceStr)) {
            return 0;
        }
        
        try {
            BigDecimal price = new BigDecimal(priceStr);
            return price.multiply(new BigDecimal("100")).intValue();
        } catch (Exception e) {
            return 0;
        }
    }

}
