# 商品搜索API快速开始指南

## 🚀 快速开始

### 1. 导入Postman集合

1. 下载 `商品搜索API.postman_collection.json` 文件
2. 打开Postman，点击 `Import` 按钮
3. 选择下载的JSON文件导入
4. 配置环境变量：
   - `baseUrl`: `http://localhost:48080/app-api`
   - `token`: 你的认证token
   - `tenantId`: 租户ID

### 2. 基础配置

确保后端服务已启动，并在 `application-dev.yaml` 中配置了爬虫系统：

```yaml
yudao:
  crawler:
    base-url: http://localhost:8000/api/v1/upstream
    token: your-token-here
    connect-timeout: 5000
    read-timeout: 30000
    cache-enabled: true
```

### 3. 第一次API调用

#### 搜索商品

```bash
curl -X GET "http://localhost:48080/app-api/product/search/keyword?keyword=iPhone 15&platform=taobao&language=zh&pageNo=1&pageSize=10" \
  -H "Authorization: Bearer your-token" \
  -H "tenant-id: 1"
```

#### 获取商品详情

```bash
curl -X POST "http://localhost:48080/app-api/product/search/detail" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -H "tenant-id: 1" \
  -d '{
    "productUrl": "https://item.taobao.com/item.htm?id=123456789",
    "language": "zh"
  }'
```

## 📋 前端集成清单

### React项目集成

1. **安装依赖**
```bash
npm install axios
npm install @types/axios  # TypeScript项目
```

2. **复制类型定义**
   - 从 `前端使用示例.md` 复制TypeScript类型定义

3. **创建API服务**
   - 复制 `ProductSearchService` 类

4. **使用React Hooks**
   - 复制 `useProductSearch` 和 `useProductDetail` hooks

### Vue 3项目集成

1. **安装依赖**
```bash
npm install axios
```

2. **复制类型定义**
   - 从 `前端使用示例.md` 复制TypeScript类型定义

3. **创建API服务**
   - 复制 `ProductSearchService` 类

4. **使用Composition API**
   - 复制Vue 3组合式函数

## 🔧 常见问题解决

### Q1: 请求返回401错误
**A**: 检查Authorization header是否正确设置Bearer token

### Q2: 请求返回403错误
**A**: 检查tenant-id header是否设置，确保有访问权限

### Q3: 搜索返回空结果
**A**: 
- 检查爬虫系统是否正常运行
- 确认平台代码是否正确（taobao, tmall, 1688）
- 检查关键词是否有效

### Q4: 价格显示异常
**A**: 注意API返回的价格单位是分，需要除以100转换为元

### Q5: 图片无法显示
**A**: 检查图片URL是否有效，可能需要处理跨域或防盗链问题

## 📊 性能优化建议

### 1. 缓存策略
- 搜索结果缓存30分钟
- 商品详情缓存1小时
- 前端可以实现额外的内存缓存

### 2. 分页优化
- 建议每页显示10-20个商品
- 实现无限滚动或分页加载
- 避免一次加载过多数据

### 3. 图片优化
- 使用图片懒加载
- 实现图片压缩和CDN加速
- 提供默认占位图

### 4. 错误处理
- 实现重试机制
- 提供友好的错误提示
- 记录错误日志用于分析

## 🎨 UI/UX建议

### 搜索页面
- 提供搜索历史记录
- 实现搜索建议/自动完成
- 添加筛选和排序选项
- 支持多选平台搜索

### 商品列表
- 网格或列表视图切换
- 商品对比功能
- 收藏/心愿单功能
- 价格趋势图表

### 商品详情
- 图片放大镜功能
- SKU选择器
- 价格计算器（含运费）
- 相似商品推荐

## 📱 移动端适配

### 响应式设计
```css
/* 移动端优化 */
@media (max-width: 768px) {
  .product-item {
    flex-direction: column;
    padding: 10px;
  }
  
  .search-form {
    flex-wrap: wrap;
  }
}
```

### 触摸优化
- 增大点击区域
- 支持手势操作
- 优化滚动性能
- 减少网络请求

## 🔍 测试建议

### 单元测试
```typescript
// Jest测试示例
describe('ProductSearchService', () => {
  test('should format price correctly', () => {
    expect(formatPrice(899900)).toBe('¥8999.00');
  });
  
  test('should convert yuan to fen', () => {
    expect(convertToFen(89.99)).toBe(8999);
  });
});
```

### 集成测试
- 测试API调用
- 测试错误处理
- 测试缓存机制
- 测试分页功能

### E2E测试
- 搜索流程测试
- 商品详情查看
- 多平台切换
- 语言切换功能

## 📈 监控和分析

### 关键指标
- API响应时间
- 搜索成功率
- 缓存命中率
- 用户搜索行为

### 错误监控
- API错误率统计
- 网络超时监控
- 爬虫系统状态
- 用户体验指标

## 🔄 版本更新

### v1.0.0 (当前版本)
- ✅ 关键词搜索功能
- ✅ 商品详情获取
- ✅ 多平台支持
- ✅ 多语言支持
- ✅ 缓存机制

### 计划中的功能
- 🚧 商品对比功能
- 🚧 价格监控提醒
- 🚧 批量搜索
- 🚧 搜索结果导出
- 🚧 AI推荐算法

## 📞 技术支持

如果在集成过程中遇到问题，请：

1. 查看API文档和示例代码
2. 检查Postman集合中的测试用例
3. 查看后端日志排查问题
4. 联系后端开发团队获取支持

---

**祝您开发顺利！** 🎉
