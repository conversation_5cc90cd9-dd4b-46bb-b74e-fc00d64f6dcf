{"info": {"name": "商品搜索API", "description": "代购网站商品搜索相关API接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:48080/app-api", "type": "string"}, {"key": "token", "value": "test247", "type": "string"}, {"key": "tenantId", "value": "1", "type": "string"}], "item": [{"name": "关键词搜索商品", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/product/search/keyword?keyword=iPhone 15&platform=taobao&language=zh&pageNo=1&pageSize=10&sort=default&priceMin=100000&priceMax=1000000", "host": ["{{baseUrl}}"], "path": ["product", "search", "keyword"], "query": [{"key": "keyword", "value": "iPhone 15"}, {"key": "platform", "value": "<PERSON><PERSON><PERSON>"}, {"key": "language", "value": "zh"}, {"key": "pageNo", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "sort", "value": "default"}, {"key": "priceMin", "value": "100000"}, {"key": "priceMax", "value": "1000000"}]}, "description": "通过关键词搜索商品，支持多平台、多语言、分页、排序和价格过滤"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"keyword\": \"iPhone 15\",\n  \"platform\": \"taobao\",\n  \"language\": \"zh\",\n  \"pageNo\": 1,\n  \"pageSize\": 10\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product/search/keyword", "host": ["{{baseUrl}}"], "path": ["product", "search", "keyword"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": 0,\n  \"msg\": \"\",\n  \"data\": {\n    \"list\": [\n      {\n        \"id\": 123456789,\n        \"name\": \"iPhone 15 Pro Max 256GB\",\n        \"introduction\": \"Apple iPhone 15 Pro Max with advanced features\",\n        \"categoryId\": 1,\n        \"picUrl\": \"https://example.com/image.jpg\",\n        \"sliderPicUrls\": [\"https://example.com/image1.jpg\"],\n        \"specType\": true,\n        \"price\": 899900,\n        \"marketPrice\": 999900,\n        \"stock\": 100,\n        \"type\": 1,\n        \"freight\": 0,\n        \"shopName\": \"Apple Official Store\",\n        \"source\": \"taobao\",\n        \"sourceLink\": \"https://item.taobao.com/item.htm?id=123456789\",\n        \"salesCount\": 1000,\n        \"scores\": 48,\n        \"newest\": false,\n        \"sale\": true,\n        \"hot\": true\n      }\n    ],\n    \"total\": 50\n  }\n}"}]}, {"name": "商品详情获取", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"productUrl\": \"https://item.taobao.com/item.htm?id=123456789\",\n  \"language\": \"zh\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product/search/detail", "host": ["{{baseUrl}}"], "path": ["product", "search", "detail"]}, "description": "根据商品链接获取详细信息，包括SKU、属性、描述等"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"productUrl\": \"https://item.taobao.com/item.htm?id=123456789\",\n  \"language\": \"zh\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product/search/detail", "host": ["{{baseUrl}}"], "path": ["product", "search", "detail"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": 0,\n  \"msg\": \"\",\n  \"data\": {\n    \"id\": 123456789,\n    \"name\": \"iPhone 15 Pro Max 256GB\",\n    \"introduction\": \"Apple iPhone 15 Pro Max with advanced features\",\n    \"description\": \"<div>Detailed product description...</div>\",\n    \"keyword\": \"iPhone 15 Pro Max\",\n    \"categoryId\": 1,\n    \"picUrl\": \"https://example.com/image.jpg\",\n    \"sliderPicUrls\": [\"https://example.com/image1.jpg\"],\n    \"specType\": true,\n    \"price\": 899900,\n    \"marketPrice\": 999900,\n    \"stock\": 100,\n    \"type\": 1,\n    \"freight\": 0,\n    \"shopName\": \"Apple Official Store\",\n    \"source\": 1,\n    \"sourceLink\": \"https://item.taobao.com/item.htm?id=123456789\",\n    \"scores\": 48,\n    \"newest\": false,\n    \"sale\": true,\n    \"hot\": true,\n    \"salesCount\": 1000,\n    \"skus\": [\n      {\n        \"id\": 1001,\n        \"price\": 899900,\n        \"marketPrice\": 999900,\n        \"vipPrice\": 859900,\n        \"picUrl\": \"https://example.com/sku-image.jpg\",\n        \"stock\": 50,\n        \"weight\": 0.2,\n        \"volume\": 0.001,\n        \"properties\": [\n          {\n            \"propertyId\": 1,\n            \"propertyName\": \"颜色\",\n            \"valueId\": 101,\n            \"valueName\": \"深空黑色\"\n          },\n          {\n            \"propertyId\": 2,\n            \"propertyName\": \"存储容量\",\n            \"valueId\": 201,\n            \"valueName\": \"256GB\"\n          }\n        ]\n      }\n    ]\n  }\n}"}]}, {"name": "搜索商品 - 英文", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"keyword\": \"iPhone 15\",\n  \"platform\": \"taobao\",\n  \"language\": \"en\",\n  \"pageNo\": 1,\n  \"pageSize\": 20,\n  \"sort\": \"price_asc\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product/search/keyword", "host": ["{{baseUrl}}"], "path": ["product", "search", "keyword"]}, "description": "英文搜索示例，按价格升序排列"}}, {"name": "搜索商品 - 天猫平台", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"keyword\": \"小米手机\",\n  \"platform\": \"tmall\",\n  \"language\": \"zh\",\n  \"pageNo\": 1,\n  \"pageSize\": 10,\n  \"sort\": \"sales\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product/search/keyword", "host": ["{{baseUrl}}"], "path": ["product", "search", "keyword"]}, "description": "天猫平台搜索示例，按销量排序"}}, {"name": "搜索商品 - 价格过滤", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "tenant-id", "value": "{{tenantId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"keyword\": \"笔记本电脑\",\n  \"platform\": \"1688\",\n  \"language\": \"zh\",\n  \"pageNo\": 1,\n  \"pageSize\": 15,\n  \"sort\": \"price_desc\",\n  \"priceMin\": 300000,\n  \"priceMax\": 800000\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product/search/keyword", "host": ["{{baseUrl}}"], "path": ["product", "search", "keyword"]}, "description": "1688平台搜索示例，价格区间3000-8000元，按价格降序"}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 可以在这里添加预请求脚本", "// 例如：动态设置token、时间戳等"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 通用测试脚本", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('msg');", "    pm.expect(jsonData).to.have.property('data');", "});", "", "pm.test(\"API call successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(0);", "});"]}}]}