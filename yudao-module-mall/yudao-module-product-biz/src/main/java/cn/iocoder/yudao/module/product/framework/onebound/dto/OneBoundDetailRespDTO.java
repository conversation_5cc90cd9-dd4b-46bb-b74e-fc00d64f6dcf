package cn.iocoder.yudao.module.product.framework.onebound.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * OneBound商品详情响应DTO
 *
 * <AUTHOR>
 */
@Data
public class OneBoundDetailRespDTO {

    /**
     * 商品详情
     */
    private Item item;

    /**
     * 错误码
     */
    @JsonProperty("error_code")
    private String errorCode;

    /**
     * 原因
     */
    private String reason;

    /**
     * 错误信息
     */
    private String error;

    @Data
    public static class Item {
        /**
         * 商品ID
         */
        @JsonProperty("num_iid")
        private String numIid;

        /**
         * 商品标题
         */
        private String title;

        /**
         * 商品描述
         */
        @JsonProperty("desc_short")
        private String descShort;

        /**
         * 价格
         */
        private String price;

        /**
         * 原价
         */
        @JsonProperty("orginal_price")
        private String orginalPrice;

        /**
         * 卖家昵称
         */
        private String nick;

        /**
         * 库存
         */
        private Integer num;

        /**
         * 详情URL
         */
        @JsonProperty("detail_url")
        private String detailUrl;

        /**
         * 主图URL
         */
        @JsonProperty("pic_url")
        private String picUrl;

        /**
         * 品牌
         */
        private String brand;

        /**
         * 分类ID
         */
        private String cid;

        /**
         * 商品描述HTML
         */
        private String desc;

        /**
         * 描述图片列表
         */
        @JsonProperty("desc_img")
        private List<String> descImg;

        /**
         * 商品图片列表
         */
        @JsonProperty("item_imgs")
        private List<ItemImg> itemImgs;

        /**
         * 地区
         */
        private String location;

        /**
         * 视频信息
         */
        private Video video;

        /**
         * 属性名称
         */
        @JsonProperty("props_name")
        private String propsName;

        /**
         * 属性列表
         */
        private List<Prop> props;

        /**
         * 属性映射列表 - 属性ID到属性名称的映射
         */
        @JsonProperty("props_list")
        private Map<String, String> propsList;

        /**
         * 总销量
         */
        @JsonProperty("total_sold")
        private String totalSold;

        /**
         * SKU列表
         */
        private Skus skus;

        /**
         * 卖家ID
         */
        @JsonProperty("seller_id")
        private String sellerId;

        /**
         * 销量
         */
        private String sales;

        /**
         * 店铺ID
         */
        @JsonProperty("shop_id")
        private String shopId;

        /**
         * 卖家信息
         */
        @JsonProperty("seller_info")
        private SellerInfo sellerInfo;
    }

    @Data
    public static class ItemImg {
        private String url;
    }

    @Data
    public static class Video {
        private String url;
    }

    @Data
    public static class Prop {
        private String name;
        private String value;
    }

    @Data
    public static class Skus {
        private List<Sku> sku;
    }

    @Data
    public static class Sku {
        private String price;
        @JsonProperty("orginal_price")
        private String orginalPrice;
        private String properties;
        @JsonProperty("properties_name")
        private String propertiesName;
        private String quantity;
        @JsonProperty("sku_id")
        private String skuId;
        @JsonProperty("sku_url")
        private String skuUrl;
    }

    @Data
    public static class SellerInfo {
        private String nick;
        private String zhuy;
        @JsonProperty("shop_name")
        private String shopName;
        @JsonProperty("shop_id")
        private String shopId;
    }

}
