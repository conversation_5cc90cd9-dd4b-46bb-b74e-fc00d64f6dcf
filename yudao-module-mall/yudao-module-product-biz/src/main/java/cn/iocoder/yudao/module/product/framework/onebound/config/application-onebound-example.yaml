# OneBound API配置示例
# 将此配置添加到你的 application-dev.yaml 或 application-prod.yaml 中

yudao:
  onebound:
    # OneBound API基础URL
    base-url: https://api-gw.onebound.cn
    
    # API密钥（建议通过系统配置管理，这里作为默认值）
    key: your-key-here
    secret: your-secret-here
    
    # 连接超时时间（毫秒）
    connect-timeout: 10000
    
    # 读取超时时间（毫秒）
    read-timeout: 30000
    
    # 是否启用缓存
    cache-enabled: true
    
    # 搜索结果缓存时间（秒）
    search-cache-time: 1800  # 30分钟
    
    # 商品详情缓存时间（秒）
    detail-cache-time: 3600  # 1小时
    
    # 默认语言
    default-language: zh-CN

# 系统配置SQL（需要在数据库中执行）
# INSERT INTO system_config (category, name, config_key, value, type, visible, remark, creator, create_time, updater, update_time, deleted) 
# VALUES ('商品搜索', '搜索策略', 'product.search.strategy', 'onebound', 1, 1, '商品搜索策略：crawler=爬虫，onebound=万邦API', 'admin', NOW(), 'admin', NOW(), 0);
# 
# INSERT INTO system_config (category, name, config_key, value, type, visible, remark, creator, create_time, updater, update_time, deleted) 
# VALUES ('OneBound', 'API密钥', 'onebound.key', 't8551216351', 1, 0, 'OneBound API密钥', 'admin', NOW(), 'admin', NOW(), 0);
# 
# INSERT INTO system_config (category, name, config_key, value, type, visible, remark, creator, create_time, updater, update_time, deleted) 
# VALUES ('OneBound', 'API密钥', 'onebound.secret', '63516f02', 1, 0, 'OneBound API密钥', 'admin', NOW(), 'admin', NOW(), 0);
