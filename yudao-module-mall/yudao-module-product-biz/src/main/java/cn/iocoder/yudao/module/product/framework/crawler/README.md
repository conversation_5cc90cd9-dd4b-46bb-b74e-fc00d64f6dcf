# 爬虫系统集成模块

## 概述

本模块实现了代购网站后端与爬虫子系统的集成，支持通过HTTPS请求调用Python爬虫系统进行商品搜索和详情获取。

## 架构设计

```
海外用户 → 代购站前端 → 代购站后端 → [HTTPS请求] → 国内爬虫系统
```

## 功能特性

- ✅ 关键词搜索商品
- ✅ 商品链接直接搜索获取详情
- ✅ 多平台支持（淘宝、天猫、1688等）
- ✅ 多语言支持（中文、英文、法语等）
- ✅ 缓存机制优化性能
- ✅ 异常处理和错误重试
- ✅ 统一的返回格式

## 支持的平台

| 平台代码 | 平台名称 | 状态 |
|---------|---------|------|
| `taobao` | 淘宝 | ✅ 已支持 |
| `tmall` | 天猫 | ✅ 已支持 |
| `1688` | 阿里巴巴1688 | ✅ 已支持 |
| `jd` | 京东 | 🚧 开发中 |
| `pdd` | 拼多多 | 🚧 开发中 |

## 支持的语言

| 语言代码 | 语言名称 | 状态 |
|---------|---------|------|
| `zh` | 中文 | ✅ 原生支持 |
| `en` | 英语 | ✅ 翻译支持 |
| `fr` | 法语 | ✅ 翻译支持 |
| `de` | 德语 | ✅ 翻译支持 |
| `es` | 西班牙语 | ✅ 翻译支持 |

## API接口

### 1. 关键词搜索商品

**接口地址**: `POST /app-api/product/search/keyword`

**请求参数**:
```json
{
  "keyword": "iPhone 15",
  "platform": "taobao",
  "language": "zh",
  "pageNo": 1,
  "pageSize": 10,
  "sort": "default",
  "priceMin": 100000,
  "priceMax": 1000000
}
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 123456789,
        "name": "iPhone 15 Pro Max 256GB",
        "introduction": "Apple iPhone 15 Pro Max with advanced features",
        "price": 899900,
        "marketPrice": 999900,
        "picUrl": "https://example.com/image.jpg",
        "sliderPicUrls": ["https://example.com/image1.jpg"],
        "shopName": "Apple Official Store",
        "source": "taobao",
        "sourceLink": "https://item.taobao.com/item.htm?id=123456789",
        "stock": 100,
        "salesCount": 1000,
        "freight": 0,
        "scores": 48
      }
    ],
    "total": 50
  },
  "msg": ""
}
```

### 2. 商品详情获取

**接口地址**: `POST /app-api/product/search/detail`

**请求参数**:
```json
{
  "productUrl": "https://item.taobao.com/item.htm?id=123456789",
  "language": "zh"
}
```

## 配置说明

在 `application-dev.yaml` 中添加爬虫系统配置：

```yaml
yudao:
  crawler:
    base-url: http://localhost:8000/api/v1/upstream # 爬虫系统基础URL
    token: your-token-here # 认证Token
    connect-timeout: 5000 # 连接超时时间（毫秒）
    read-timeout: 30000 # 读取超时时间（毫秒）
    cache-enabled: true # 是否启用缓存
    search-cache-time: 1800 # 搜索结果缓存时间（秒）
    detail-cache-time: 3600 # 商品详情缓存时间（秒）
```

## 缓存策略

- **搜索结果缓存**: 30分钟
- **商品详情缓存**: 1小时
- **缓存键格式**: 
  - 搜索: `product_search_keyword:{keyword}:{platform}:{pageNo}:{pageSize}:{sort}:{priceMin}:{priceMax}`
  - 详情: `product_search_detail:{productUrl}`

## 错误处理

系统定义了专门的错误码：

- `1_008_008_000`: 爬虫系统搜索失败
- `1_008_008_001`: 爬虫系统获取详情失败
- `1_008_008_002`: 爬虫系统异常

## 使用示例

### Java代码示例

```java
@Autowired
private CrawlerService crawlerService;

// 搜索商品
AppSearchPageReqVO searchReq = new AppSearchPageReqVO();
searchReq.setKeyword("iPhone 15");
searchReq.setPlatform("taobao");
searchReq.setPageNo(1);
searchReq.setPageSize(10);

PageResult<AppProductSpuRespVO> searchResult = crawlerService.searchProducts(searchReq);

// 获取商品详情
AppSearchDetailReqVO detailReq = new AppSearchDetailReqVO();
detailReq.setProductUrl("https://item.taobao.com/item.htm?id=123456789");

AppProductSpuDetailRespVO detail = crawlerService.getProductDetail(detailReq);
```

## 注意事项

1. 确保爬虫系统正常运行并可访问
2. 配置正确的认证Token
3. 注意价格单位转换（分 ↔ 元）
4. 合理设置超时时间
5. 监控缓存命中率和系统性能

## 测试

运行测试类：
```bash
mvn test -Dtest=CrawlerServiceTest
```

使用HTTP文件测试：
```
AppSearchController.http
```
