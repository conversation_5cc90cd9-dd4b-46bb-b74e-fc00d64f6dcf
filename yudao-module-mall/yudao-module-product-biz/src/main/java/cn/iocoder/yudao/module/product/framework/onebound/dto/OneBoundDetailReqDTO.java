package cn.iocoder.yudao.module.product.framework.onebound.dto;

import lombok.Data;

/**
 * OneBound商品详情请求DTO
 *
 * <AUTHOR>
 */
@Data
public class OneBoundDetailReqDTO {

    /**
     * 商品ID
     */
    private String numIid;

    /**
     * 是否获取促销信息
     */
    private String isPromotion = "1";

    /**
     * 语言
     */
    private String lang = "cn";

    /**
     * 域名类型（京东专用）
     */
    private String domainType;

}
