package cn.iocoder.yudao.module.product.controller.app.search.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 用户 App - 商品搜索分页 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 App - 商品搜索分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSearchPageReqVO extends PageParam {

    @Schema(description = "搜索关键词", requiredMode = Schema.RequiredMode.REQUIRED, example = "iPhone 15")
    @NotBlank(message = "搜索关键词不能为空")
    @Size(min = 1, max = 200, message = "搜索关键词长度必须在1-200字符之间")
    private String keyword;

    @Schema(description = "平台代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "taobao")
    @NotBlank(message = "平台代码不能为空")
    private String platform;

    @Schema(description = "语言代码", example = "zh")
    private String language = "zh";

    @Schema(description = "排序方式", example = "default")
    private String sort = "default";

    @Schema(description = "最低价格(分)", example = "10000")
    private Integer priceMin;

    @Schema(description = "最高价格(分)", example = "200000")
    private Integer priceMax;

}
