package cn.iocoder.yudao.module.product.framework.onebound.dto;

import lombok.Data;

/**
 * OneBound搜索请求DTO
 *
 * <AUTHOR>
 */
@Data
public class OneBoundSearchReqDTO {

    /**
     * 搜索关键词
     */
    private String q;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 48;

    /**
     * 最低价格
     */
    private String startPrice = "0";

    /**
     * 最高价格
     */
    private String endPrice = "0";

    /**
     * 分类ID
     */
    private String cat = "0";

    /**
     * 排序方式
     */
    private String sort = "";

    /**
     * 语言
     */
    private String lang = "cn";

    /**
     * 是否只显示折扣商品
     */
    private String discountOnly = "";

    /**
     * 卖家信息
     */
    private String sellerInfo = "no";

    /**
     * 卖家昵称
     */
    private String nick = "";

    /**
     * 路径
     */
    private String ppath = "";

    /**
     * 图片ID
     */
    private String imgid = "";

    /**
     * 过滤条件
     */
    private String filter = "";

}
