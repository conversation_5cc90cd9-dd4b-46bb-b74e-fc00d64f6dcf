package cn.iocoder.yudao.module.product.framework.onebound.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * OneBound搜索响应DTO
 *
 * <AUTHOR>
 */
@Data
public class OneBoundSearchRespDTO {

    /**
     * 商品列表信息
     */
    private Items items;

    /**
     * 错误码
     */
    @JsonProperty("error_code")
    private String errorCode;

    /**
     * 原因
     */
    private String reason;

    /**
     * 错误信息
     */
    private String error;

    @Data
    public static class Items {
        /**
         * 当前页码
         */
        private String page;

        /**
         * 实际总结果数
         */
        @JsonProperty("real_total_results")
        private String realTotalResults;

        /**
         * 总结果数
         */
        @JsonProperty("total_results")
        private String totalResults;

        /**
         * 每页大小
         */
        @JsonProperty("page_size")
        private Integer pageSize;

        /**
         * 总页数
         */
        private String pagecount;

        /**
         * 商品列表
         */
        private List<Item> item;
    }

    @Data
    public static class Item {
        /**
         * 商品标题
         */
        private String title;

        /**
         * 商品图片URL
         */
        @JsonProperty("pic_url")
        private String picUrl;

        /**
         * 促销价格
         */
        @JsonProperty("promotion_price")
        private String promotionPrice;

        /**
         * 原价
         */
        @JsonProperty("orginal_price")
        private String orginalPrice;

        /**
         * 价格
         */
        private String price;

        /**
         * 商品ID
         */
        @JsonProperty("num_iid")
        private String numIid;

        /**
         * 地区
         */
        private String area;

        /**
         * 详情URL
         */
        @JsonProperty("detail_url")
        private String detailUrl;

        /**
         * 销量（京东）
         */
        private Integer sales;

        /**
         * 卖家（京东）
         */
        private String seller;

        /**
         * 店铺ID（京东）
         */
        @JsonProperty("shop_id")
        private String shopId;

        /**
         * 评论数（京东）
         */
        private String reviews;
    }

}
