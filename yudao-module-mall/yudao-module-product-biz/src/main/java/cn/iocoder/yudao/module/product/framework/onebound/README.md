# OneBound API集成说明

## 概述

OneBound是一个第三方商品数据API服务，支持淘宝、天猫、京东等主流电商平台的商品搜索和详情获取。本模块实现了OneBound API的集成，作为商品搜索策略模式的一种实现。

## 最新优化 (v2.0)

### 🔧 HTTP请求方式优化
- **改进前**: 使用Hutool的HttpRequest.form()方式发送请求
- **改进后**: 按照官方示例，使用原生URLConnection + GET方式拼接URL参数
- **优势**: 提高请求稳定性，减少请求失败率

### 🎯 SKU数据转换优化
- **修复问题**: SKU的id和properties字段缺失
- **新增功能**:
  - 正确解析OneBound返回的sku_id字段
  - 智能解析properties和properties_name字段
  - 支持多种属性格式：`颜色:红色;尺寸:L` 或 `1627207:28341;1627208:28342`
  - 自动生成属性ID和值ID

### 📝 请求日志优化
- **新增**: 完整的带参数请求URL日志输出
- **便于**: 问题排查和API调用跟踪

## 配置说明

### 1. 系统配置

在系统配置表中添加以下配置项：

```sql
-- 商品搜索策略配置
INSERT INTO system_config (category, name, config_key, value, type, visible, remark, creator, create_time, updater, update_time, deleted) 
VALUES ('商品搜索', '搜索策略', 'product.search.strategy', 'onebound', 1, 1, '商品搜索策略：crawler=爬虫，onebound=万邦API', 'admin', NOW(), 'admin', NOW(), 0);

-- OneBound API密钥配置
INSERT INTO system_config (category, name, config_key, value, type, visible, remark, creator, create_time, updater, update_time, deleted) 
VALUES ('OneBound', 'API密钥', 'onebound.key', 't8551216351', 1, 0, 'OneBound API密钥', 'admin', NOW(), 'admin', NOW(), 0);

-- OneBound API密钥配置
INSERT INTO system_config (category, name, config_key, value, type, visible, remark, creator, create_time, updater, update_time, deleted) 
VALUES ('OneBound', 'API密钥', 'onebound.secret', '63516f02', 1, 0, 'OneBound API密钥', 'admin', NOW(), 'admin', NOW(), 0);
```

### 2. 应用配置

在 `application-dev.yaml` 中添加OneBound配置：

```yaml
yudao:
  onebound:
    base-url: https://api-gw.onebound.cn
    key: your-key-here  # 默认密钥，优先使用系统配置
    secret: your-secret-here  # 默认密钥，优先使用系统配置
    connect-timeout: 10000
    read-timeout: 30000
    cache-enabled: true
    search-cache-time: 1800  # 30分钟
    detail-cache-time: 3600  # 1小时
    default-language: zh-CN
```

## 支持的平台

- **淘宝/天猫**: `taobao`
- **京东**: `jd`

## API接口

### 1. 商品搜索

**淘宝搜索**:
```
GET https://api-gw.onebound.cn/taobao/item_search/?key={key}&secret={secret}&q={keyword}&page={page}&lang={lang}
```

**京东搜索**:
```
GET https://api-gw.onebound.cn/jd/item_search/?key={key}&secret={secret}&q={keyword}&page={page}&lang={lang}
```

### 2. 商品详情

**淘宝详情**:
```
GET https://api-gw.onebound.cn/taobao/item_get/?key={key}&secret={secret}&num_iid={productId}&is_promotion=1&lang={lang}
```

**京东详情**:
```
GET https://api-gw.onebound.cn/jd/item_get/?key={key}&secret={secret}&num_iid={productId}&domain_type=jd&lang={lang}
```

## 数据转换

### 搜索结果转换

OneBound API返回的商品数据会被转换为系统标准的 `AppProductSpuRespVO` 格式：

- `title` → `name`
- `pic_url` → `picUrl`
- `price` → `price` (元转分)
- `num_iid` → `sourceId`
- `detail_url` → `sourceLink`

### 商品详情转换

详情数据转换为 `AppProductSpuDetailRespVO` 格式：

- 基本信息转换
- 图片URL标准化（添加https协议）
- 价格单位转换（元转分）
- SKU信息转换
- 描述图片转换为HTML格式

## 缓存策略

- **搜索结果**: 缓存30分钟
- **商品详情**: 缓存1小时
- **缓存键格式**:
  - 搜索: `product_search_keyword:{参数组合}`
  - 详情: `product_search_detail:{sourceId}:{source}`

## 错误处理

系统定义了专门的错误码：

- `1_008_010_000`: OneBound搜索失败
- `1_008_010_001`: OneBound获取详情失败
- `1_008_010_002`: OneBound系统异常
- `1_008_010_003`: OneBound配置错误

## 使用示例

### Java代码示例

```java
@Autowired
private ProductSearchContext productSearchContext;

// 搜索商品
AppSearchPageReqVO searchReq = new AppSearchPageReqVO();
searchReq.setKeyword("iPhone 15");
searchReq.setPlatform("taobao");
searchReq.setPageNo(1);
searchReq.setPageSize(10);

PageResult<AppProductSpuRespVO> searchResult = productSearchContext.searchProducts(searchReq);

// 获取商品详情
AppSearchDetailReqVO detailReq = new AppSearchDetailReqVO();
detailReq.setId("672112332529");
detailReq.setPlatform("taobao");

AppProductSpuDetailRespVO detail = productSearchContext.getProductDetailById(detailReq);
```

### 切换搜索策略

```java
// 动态切换到OneBound策略
productSearchContext.setProductSearchService("onebound");

// 切换回爬虫策略
productSearchContext.setProductSearchService("crawler");
```

## 注意事项

1. **API限制**: OneBound API有调用次数限制，请合理使用缓存
2. **价格单位**: 所有价格以分为单位存储和传输
3. **图片URL**: 自动处理相对URL，添加https协议
4. **语言支持**: 支持中文(zh-CN)和英文(en)
5. **错误重试**: 建议实现适当的重试机制
6. **配置优先级**: 系统配置优先于应用配置文件

## 开发调试

### 测试接口

可以通过以下接口测试OneBound集成：

```bash
# 测试搜索
curl -X GET "http://localhost:48080/app-api/product/search/keyword?keyword=键盘&platform=taobao&pageNo=1&pageSize=10" \
  -H "Authorization: Bearer your-token" \
  -H "tenant-id: 1"

# 测试详情
curl -X POST "http://localhost:48080/app-api/product/search/detail" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -H "tenant-id: 1" \
  -d '{"id":"672112332529","platform":"taobao"}'
```

### 日志监控

关键日志会输出到以下Logger：

- `cn.iocoder.yudao.module.product.framework.onebound.core.OneBoundClient`
- `cn.iocoder.yudao.module.product.service.search.OneBoundSearchServiceImpl`

建议在生产环境中适当调整日志级别。

