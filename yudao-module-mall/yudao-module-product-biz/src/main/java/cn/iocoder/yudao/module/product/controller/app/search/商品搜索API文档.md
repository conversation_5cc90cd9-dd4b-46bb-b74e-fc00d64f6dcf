# 商品搜索API文档

## 概述

本文档描述了代购网站商品搜索相关的API接口，支持通过关键词搜索商品和根据商品链接获取详情。

## 基础信息

- **Base URL**: `/app-api/product/search`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token

## 通用请求头

```http
Authorization: Bearer {token}
Content-Type: application/json
tenant-id: {tenantId}
```

## API接口列表

### 1. 关键词搜索商品

根据关键词搜索商品，支持多平台、多语言、分页、排序和价格过滤。

#### 接口信息
- **URL**: `GET /app-api/product/search/keyword`
- **描述**: 通过关键词搜索商品
- **缓存**: 30分钟

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| keyword | string | ✅ | 搜索关键词 (1-200字符) | - |
| platform | string | ✅ | 平台代码 | - |
| language | string | ❌ | 语言代码 | zh |
| pageNo | number | ❌ | 页码 | 1 |
| pageSize | number | ❌ | 每页数量 | 10 |
| sort | string | ❌ | 排序方式 | default |
| priceMin | number | ❌ | 最低价格-分 | - |
| priceMax | number | ❌ | 最高价格-分 | - |

#### 请求示例

```bash
GET /app-api/product/search/keyword?keyword=iPhone 15&platform=taobao&language=zh&pageNo=1&pageSize=10&sort=default&priceMin=100000&priceMax=1000000
```

#### 响应格式

```typescript
interface SearchResponse {
  code: number;
  msg: string;
  data: {
    list: ProductItem[];
    total: number;
  };
}

interface ProductItem {
  id: number;                    // 商品ID
  name: string;                  // 商品名称
  introduction: string;          // 商品简介
  categoryId: number;            // 分类ID
  picUrl: string;                // 主图URL
  sliderPicUrls: string[];       // 轮播图URLs
  specType: boolean;             // 规格类型 (true: 多规格, false: 单规格)
  price: number;                 // 价格(分)
  marketPrice: number;           // 市场价(分)
  stock: number;                 // 库存
  type: number;                  // 类型 (0: 自营, 1: 代购)
  freight: number;               // 运费(分)
  shopName: string;              // 店铺名称
  source: string;                // 来源平台
  sourceLink: string;            // 原始链接
  salesCount: number;            // 销量
  scores: number;                // 评分
  newest: boolean;               // 是否新品
  sale: boolean;                 // 是否特价
  hot: boolean;                  // 是否热销
}
```

#### 响应示例

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "list": [
      {
        "id": 123456789,
        "name": "iPhone 15 Pro Max 256GB",
        "introduction": "Apple iPhone 15 Pro Max with advanced features",
        "categoryId": 1,
        "picUrl": "https://example.com/image.jpg",
        "sliderPicUrls": ["https://example.com/image1.jpg"],
        "specType": true,
        "price": 899900,
        "marketPrice": 999900,
        "stock": 100,
        "type": 1,
        "freight": 0,
        "shopName": "Apple Official Store",
        "source": "taobao",
        "sourceLink": "https://item.taobao.com/item.htm?id=123456789",
        "salesCount": 1000,
        "scores": 48,
        "newest": false,
        "sale": true,
        "hot": true
      }
    ],
    "total": 50
  }
}
```

### 2. 商品详情获取

根据商品链接获取详细信息，包括SKU、属性、描述等。

#### 接口信息
- **URL**: `POST /app-api/product/search/detail`
- **描述**: 根据商品链接获取详情
- **缓存**: 1小时

#### 请求参数

```typescript
interface DetailRequest {
  productUrl: string;     // 商品链接 (必填, 10-500字符)
  language?: string;      // 语言代码 (可选, 默认: "zh")
}
```

#### 请求示例

```json
{
  "productUrl": "https://item.taobao.com/item.htm?id=123456789",
  "language": "zh"
}
```

#### 响应格式

```typescript
interface DetailResponse {
  code: number;
  msg: string;
  data: ProductDetail;
}

interface ProductDetail {
  id: number;                    // 商品ID
  name: string;                  // 商品名称
  introduction: string;          // 商品简介
  description: string;           // 商品详情(HTML)
  keyword: string;               // 关键词
  categoryId: number;            // 分类ID
  picUrl: string;                // 主图URL
  sliderPicUrls: string[];       // 轮播图URLs
  specType: boolean;             // 规格类型
  price: number;                 // 价格(分)
  marketPrice: number;           // 市场价(分)
  stock: number;                 // 库存
  type: number;                  // 类型 (0: 自营, 1: 代购)
  freight: number;               // 运费(分)
  shopName: string;              // 店铺名称
  source: number;                // 来源平台编码
  sourceLink: string;            // 原始链接
  scores: number;                // 评分
  newest: boolean;               // 是否新品
  sale: boolean;                 // 是否特价
  hot: boolean;                  // 是否热销
  salesCount: number;            // 销量
  skus: ProductSku[];            // SKU列表
}

interface ProductSku {
  id: number;                    // SKU ID
  price: number;                 // 价格(分)
  marketPrice: number;           // 市场价(分)
  vipPrice: number;              // VIP价格(分)
  picUrl: string;                // SKU图片
  stock: number;                 // 库存
  weight: number;                // 重量(kg)
  volume: number;                // 体积(m³)
  properties: SkuProperty[];     // 属性列表
}

interface SkuProperty {
  propertyId: number;            // 属性ID
  propertyName: string;          // 属性名称
  valueId: number;               // 属性值ID
  valueName: string;             // 属性值名称
}
```

#### 响应示例

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": 123456789,
    "name": "iPhone 15 Pro Max 256GB",
    "introduction": "Apple iPhone 15 Pro Max with advanced features",
    "description": "<div>Detailed product description...</div>",
    "keyword": "iPhone 15 Pro Max",
    "categoryId": 1,
    "picUrl": "https://example.com/image.jpg",
    "sliderPicUrls": ["https://example.com/image1.jpg"],
    "specType": true,
    "price": 899900,
    "marketPrice": 999900,
    "stock": 100,
    "type": 1,
    "freight": 0,
    "shopName": "Apple Official Store",
    "source": 1,
    "sourceLink": "https://item.taobao.com/item.htm?id=123456789",
    "scores": 48,
    "newest": false,
    "sale": true,
    "hot": true,
    "salesCount": 1000,
    "skus": [
      {
        "id": 1001,
        "price": 899900,
        "marketPrice": 999900,
        "vipPrice": 859900,
        "picUrl": "https://example.com/sku-image.jpg",
        "stock": 50,
        "weight": 0.2,
        "volume": 0.001,
        "properties": [
          {
            "propertyId": 1,
            "propertyName": "颜色",
            "valueId": 101,
            "valueName": "深空黑色"
          },
          {
            "propertyId": 2,
            "propertyName": "存储容量",
            "valueId": 201,
            "valueName": "256GB"
          }
        ]
      }
    ]
  }
}
```

## 参数说明

### 支持的平台 (platform)

| 平台代码 | 平台名称 | 状态 |
|---------|---------|------|
| `taobao` | 淘宝 | ✅ 已支持 |
| `tmall` | 天猫 | ✅ 已支持 |
| `1688` | 阿里巴巴1688 | ✅ 已支持 |
| `jd` | 京东 | 🚧 开发中 |
| `pdd` | 拼多多 | 🚧 开发中 |

### 支持的语言 (language)

| 语言代码 | 语言名称 | 状态 |
|---------|---------|------|
| `zh` | 中文 | ✅ 原生支持 |
| `en` | 英语 | ✅ 翻译支持 |
| `fr` | 法语 | ✅ 翻译支持 |
| `de` | 德语 | ✅ 翻译支持 |
| `es` | 西班牙语 | ✅ 翻译支持 |
| `it` | 意大利语 | ✅ 翻译支持 |
| `ja` | 日语 | ✅ 翻译支持 |
| `ko` | 韩语 | ✅ 翻译支持 |

### 排序方式 (sort)

| 值 | 说明 |
|----|------|
| `default` | 默认排序 |
| `price_asc` | 价格升序 |
| `price_desc` | 价格降序 |
| `sales` | 销量排序 |
| `newest` | 最新商品 |

### 来源平台编码 (source)

| 编码 | 平台名称 |
|------|---------|
| `1` | 淘宝 |
| `2` | 天猫 |
| `3` | 阿里巴巴1688 |
| `4` | 京东 |
| `5` | 拼多多 |
| `0` | 未知平台 |

## 错误处理

### 错误响应格式

```json
{
  "code": 400,
  "msg": "错误描述",
  "data": null
}
```

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| `0` | 成功 |
| `400` | 请求参数错误 |
| `401` | 认证失败 |
| `403` | 权限不足 |
| `500` | 服务器内部错误 |
| `1008008000` | 爬虫系统搜索失败 |
| `1008008001` | 爬虫系统获取详情失败 |

## 注意事项

### 价格单位
- 所有价格字段以**分**为单位
- 例如：`899900` 表示 ¥8999.00

### 缓存机制
- 搜索结果缓存30分钟
- 商品详情缓存1小时
- 相同参数的请求会直接返回缓存结果

### 请求限制
- 关键词长度：1-200字符
- 商品链接长度：10-500字符
- 分页大小：建议不超过50

### 最佳实践
1. 合理设置分页大小，避免一次请求过多数据
2. 利用缓存机制，避免重复请求相同数据
3. 处理网络异常和超时情况
4. 对价格进行正确的单位转换显示
