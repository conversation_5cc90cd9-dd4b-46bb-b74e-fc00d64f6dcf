package cn.iocoder.yudao.module.product.controller.app.spu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 App - 分类商品 SPU列表  Request VO")
@Data
public class AppProductCategorySpuReqVO {


    @Schema(description = "分类编号", example = "1")
    private Long categoryId;

    @Schema(description = "当前spuId", example = "1001")
    private Long spuId;

    @Schema(description = "数量", example = "1,3,5")
    private Integer limit ;

}
