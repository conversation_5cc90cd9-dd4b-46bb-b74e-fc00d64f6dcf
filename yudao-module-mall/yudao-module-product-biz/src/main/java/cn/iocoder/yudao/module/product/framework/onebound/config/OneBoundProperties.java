package cn.iocoder.yudao.module.product.framework.onebound.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * OneBound API配置属性
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "yudao.onebound")
@Component
@Validated
@Data
public class OneBoundProperties {

    /**
     * OneBound API基础URL
     */
    @NotEmpty(message = "OneBound API基础URL不能为空")
    private String baseUrl = "https://api-gw.onebound.cn";

    /**
     * API密钥
     */
    @NotEmpty(message = "OneBound API密钥不能为空")
    private String key = "your-key-here";

    /**
     * API密钥
     */
    @NotEmpty(message = "OneBound API密钥不能为空")
    private String secret = "your-secret-here";

    /**
     * 连接超时时间（毫秒）
     */
    @NotNull(message = "连接超时时间不能为空")
    private Integer connectTimeout = 10000;

    /**
     * 读取超时时间（毫秒）
     */
    @NotNull(message = "读取超时时间不能为空")
    private Integer readTimeout = 30000;

    /**
     * 是否启用缓存
     */
    private Boolean cacheEnabled = true;

    /**
     * 搜索结果缓存时间（秒）
     */
    private Integer searchCacheTime = 1800; // 30分钟

    /**
     * 商品详情缓存时间（秒）
     */
    private Integer detailCacheTime = 3600; // 1小时

    /**
     * 默认语言
     */
    private String defaultLanguage = "cn";

}
