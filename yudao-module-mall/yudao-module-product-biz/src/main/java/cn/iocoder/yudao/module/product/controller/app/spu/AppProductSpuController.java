package cn.iocoder.yudao.module.product.controller.app.spu;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.*;
import cn.iocoder.yudao.module.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.product.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.product.enums.spu.ProductSpuStatusEnum;
import cn.iocoder.yudao.module.product.service.history.ProductBrowseHistoryService;
import cn.iocoder.yudao.module.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.product.service.spu.ProductSpuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.string.StrUtils.generateSlug;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.product.enums.ErrorCodeConstants.SPU_NOT_ENABLE;
import static cn.iocoder.yudao.module.product.enums.ErrorCodeConstants.SPU_NOT_EXISTS;

@Tag(name = "用户 APP - 商品 SPU")
@RestController
@RequestMapping("/product/spu")
@Validated
public class AppProductSpuController {

    @Resource
    private ProductSpuService productSpuService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ProductBrowseHistoryService productBrowseHistoryService;

    @GetMapping("/list-by-ids")
    @Operation(summary = "获得商品 SPU 列表")
    @Parameter(name = "ids", description = "编号列表", required = true)
    @PermitAll
    public CommonResult<List<AppProductSpuRespVO>> getSpuList(@RequestParam("ids") Set<Long> ids) {
        List<ProductSpuDO> list = productSpuService.getSpuList(ids);
        if (CollUtil.isEmpty(list)) {
            return success(Collections.emptyList());
        }

        // 拼接返回
        list.forEach(spu -> spu.setSalesCount(spu.getSalesCount() + spu.getVirtualSalesCount()));
        List<AppProductSpuRespVO> voList = BeanUtils.toBean(list, AppProductSpuRespVO.class);
        return success(voList);
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品 SPU 分页")
    @PermitAll
    @Cacheable(cacheNames = RedisKeyConstants.PRODUCT_SPU_PAGE+"#3600",
            key = "{#pageVO.categoryId, #pageVO.categoryIds, #pageVO.keyword, #pageVO.sortField, #pageVO.sortAsc, #pageVO.ids,#pageVO.pageSize,#pageVO.pageNo}") //1小时缓存
    public CommonResult<PageResult<AppProductSpuRespVO>> getSpuPage(@Valid AppProductSpuPageReqVO pageVO) {
        PageResult<ProductSpuDO> pageResult = productSpuService.getSpuPage(pageVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty(pageResult.getTotal()));
        }

        // 拼接返回
        pageResult.getList().forEach(spu -> spu.setSalesCount(spu.getSalesCount() + spu.getVirtualSalesCount()));
        PageResult<AppProductSpuRespVO> voPageResult = BeanUtils.toBean(pageResult, AppProductSpuRespVO.class);
        return success(voPageResult);
    }

    @GetMapping("/get-detail")
    @Operation(summary = "获得商品 SPU 明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    @Cacheable(cacheNames = RedisKeyConstants.PRODUCT_SPU_DETAIL+"#86400",key = "#id") //24小时缓存
    public CommonResult<AppProductSpuDetailRespVO> getSpuDetail(@RequestParam("id") Long id) {
        // 获得商品 SPU
        ProductSpuDO spu = productSpuService.getSpu(id);
        if (spu == null) {
            throw exception(SPU_NOT_EXISTS);
        }
        if (!ProductSpuStatusEnum.isEnable(spu.getStatus())) {
            throw exception(SPU_NOT_ENABLE, spu.getName());
        }
        // 获得商品 SKU
        List<ProductSkuDO> skus = productSkuService.getSkuListBySpuId(spu.getId());

        // 增加浏览量
        productSpuService.updateBrowseCount(id, 1);
        // 保存浏览记录 ding 加入缓存功能后 浏览记录需要以谷歌统计为准
        productBrowseHistoryService.createBrowseHistory(getLoginUserId(), id);

        // 拼接返回
        spu.setSalesCount(spu.getSalesCount() + spu.getVirtualSalesCount());
        AppProductSpuDetailRespVO spuVO = BeanUtils.toBean(spu, AppProductSpuDetailRespVO.class)
                .setSkus(BeanUtils.toBean(skus, AppProductSpuDetailRespVO.Sku.class));
        return success(spuVO);
    }

    @GetMapping("/list-by-category")
    @Operation(summary = "根据分类获得商品 SPU 列表")
    @Parameter(name = "ids", description = "编号列表", required = true)
    @PermitAll
    @Cacheable(cacheNames = RedisKeyConstants.PRODUCT_SPU_RELATED+"#86400",key = "{#reqVO.spuId,#reqVO.categoryId,#reqVO.limit}") //24小时缓存
    public CommonResult<List<AppProductSpuRespVO>> getSpuListByCategory(AppProductCategorySpuReqVO reqVO) {
        List<ProductSpuDO> list = productSpuService.getSpuListByCategory(reqVO);
        if (CollUtil.isEmpty(list)) {
            return success(Collections.emptyList());
        }
        // 拼接返回
        list.forEach(spu -> spu.setSalesCount(spu.getSalesCount() + spu.getVirtualSalesCount()));
        List<AppProductSpuRespVO> voList = BeanUtils.toBean(list, AppProductSpuRespVO.class);
        return success(voList);
    }

    @GetMapping("/urls/{domain}")
    @Operation(summary = "获取所有商品Url列表")
    @PermitAll
    @Cacheable(cacheNames = RedisKeyConstants.PRODUCT_URLS+"#43200",key = "#domain") //12小时缓存
    public List<String> getProductUrls(@PathVariable("domain") String domain){
        List<ProductSpuDO> list = productSpuService.getAllSpuList();
        List<String> productUrls = new ArrayList<>();

        // 拼接每个商品的完整 URL
        for (ProductSpuDO product : list) {
            String productUrl = formatProductUrl(product,domain);
            productUrls.add(productUrl);
        }

        return productUrls;
    }

    @PostMapping("/agent-transition")
    @Operation(summary = "代购商品转换为本地商品")
    public  CommonResult<AppProductSpuDetailRespVO> productTransitionBySource(AppProductTransitionReqVO reqVO){

        return success(productSpuService.agentProductTransition(getLoginUserId(),reqVO));

    }

    @PostMapping("/custom-transition")
    @Operation(summary = "自定义商品转换为本地商品")
    public CommonResult<AppProductSpuDetailRespVO> productTransitionBySourceId(AppCustomProductTransitionReqVO reqVO){

        return success(productSpuService.customProductTransition(getLoginUserId(),reqVO));

    }

    private String formatProductUrl(ProductSpuDO product,String domain) {
        return "https://www."+domain + ".com/product/" + product.getId()+"/"+generateSlug(product.getName());
    }


}
