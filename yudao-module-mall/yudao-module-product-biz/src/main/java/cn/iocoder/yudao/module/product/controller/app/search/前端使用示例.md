# 前端使用示例

## TypeScript 类型定义

```typescript
// 搜索请求参数
export interface SearchRequest {
  keyword: string;
  platform: string;
  language?: string;
  pageNo?: number;
  pageSize?: number;
  sort?: string;
  priceMin?: number;
  priceMax?: number;
}

// 商品详情请求参数
export interface DetailRequest {
  productUrl: string;
  language?: string;
}

// 商品项
export interface ProductItem {
  id: number;
  name: string;
  introduction: string;
  categoryId: number;
  picUrl: string;
  sliderPicUrls: string[];
  specType: boolean;
  price: number;
  marketPrice: number;
  stock: number;
  type: number;
  freight: number;
  shopName: string;
  source: string;
  sourceLink: string;
  salesCount: number;
  scores: number;
  newest: boolean;
  sale: boolean;
  hot: boolean;
}

// 商品详情
export interface ProductDetail {
  id: number;
  name: string;
  introduction: string;
  description: string;
  keyword: string;
  categoryId: number;
  picUrl: string;
  sliderPicUrls: string[];
  specType: boolean;
  price: number;
  marketPrice: number;
  stock: number;
  type: number;
  freight: number;
  shopName: string;
  source: number;
  sourceLink: string;
  scores: number;
  newest: boolean;
  sale: boolean;
  hot: boolean;
  salesCount: number;
  skus: ProductSku[];
}

// SKU信息
export interface ProductSku {
  id: number;
  price: number;
  marketPrice: number;
  vipPrice: number;
  picUrl: string;
  stock: number;
  weight: number;
  volume: number;
  properties: SkuProperty[];
}

// SKU属性
export interface SkuProperty {
  propertyId: number;
  propertyName: string;
  valueId: number;
  valueName: string;
}

// API响应格式
export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// 分页响应
export interface PageResponse<T> {
  list: T[];
  total: number;
}
```

## API 服务类

```typescript
import axios, { AxiosResponse } from 'axios';

class ProductSearchService {
  private baseURL = '/app-api/product/search';
  
  /**
   * 关键词搜索商品
   */
  async searchByKeyword(params: SearchRequest): Promise<PageResponse<ProductItem>> {
    const response: AxiosResponse<ApiResponse<PageResponse<ProductItem>>> =
      await axios.get(`${this.baseURL}/keyword`, { params });

    if (response.data.code === 0) {
      return response.data.data;
    } else {
      throw new Error(response.data.msg || '搜索失败');
    }
  }

  /**
   * 获取商品详情
   */
  async getProductDetail(params: DetailRequest): Promise<ProductDetail> {
    const response: AxiosResponse<ApiResponse<ProductDetail>> = 
      await axios.post(`${this.baseURL}/detail`, params);
    
    if (response.data.code === 0) {
      return response.data.data;
    } else {
      throw new Error(response.data.msg || '获取详情失败');
    }
  }

  /**
   * 价格格式化（分转元）
   */
  formatPrice(price: number): string {
    return (price / 100).toFixed(2);
  }

  /**
   * 价格转换（元转分）
   */
  convertToFen(yuan: number): number {
    return Math.round(yuan * 100);
  }
}

export const productSearchService = new ProductSearchService();
```

## React Hooks 示例

```typescript
import { useState, useEffect } from 'react';
import { productSearchService } from './ProductSearchService';

// 搜索Hook
export const useProductSearch = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<PageResponse<ProductItem> | null>(null);
  const [error, setError] = useState<string | null>(null);

  const search = async (params: SearchRequest) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await productSearchService.searchByKeyword(params);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索失败');
    } finally {
      setLoading(false);
    }
  };

  return { loading, data, error, search };
};

// 商品详情Hook
export const useProductDetail = (productUrl?: string) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ProductDetail | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchDetail = async (url: string, language = 'zh') => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await productSearchService.getProductDetail({
        productUrl: url,
        language
      });
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (productUrl) {
      fetchDetail(productUrl);
    }
  }, [productUrl]);

  return { loading, data, error, fetchDetail };
};
```

## Vue 3 Composition API 示例

```typescript
import { ref, reactive } from 'vue';
import { productSearchService } from './ProductSearchService';

// 搜索组合式函数
export const useProductSearch = () => {
  const loading = ref(false);
  const data = ref<PageResponse<ProductItem> | null>(null);
  const error = ref<string | null>(null);

  const search = async (params: SearchRequest) => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await productSearchService.searchByKeyword(params);
      data.value = result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '搜索失败';
    } finally {
      loading.value = false;
    }
  };

  return { loading, data, error, search };
};

// 商品详情组合式函数
export const useProductDetail = () => {
  const loading = ref(false);
  const data = ref<ProductDetail | null>(null);
  const error = ref<string | null>(null);

  const fetchDetail = async (productUrl: string, language = 'zh') => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await productSearchService.getProductDetail({
        productUrl,
        language
      });
      data.value = result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取详情失败';
    } finally {
      loading.value = false;
    }
  };

  return { loading, data, error, fetchDetail };
};
```

## 使用示例

### React 组件示例

```tsx
import React, { useState } from 'react';
import { useProductSearch } from './hooks/useProductSearch';

const ProductSearchPage: React.FC = () => {
  const { loading, data, error, search } = useProductSearch();
  const [searchParams, setSearchParams] = useState<SearchRequest>({
    keyword: '',
    platform: 'taobao',
    language: 'zh',
    pageNo: 1,
    pageSize: 10
  });

  const handleSearch = () => {
    if (searchParams.keyword.trim()) {
      search(searchParams);
    }
  };

  const formatPrice = (price: number) => {
    return `¥${(price / 100).toFixed(2)}`;
  };

  return (
    <div className="product-search">
      <div className="search-form">
        <input
          type="text"
          placeholder="请输入搜索关键词"
          value={searchParams.keyword}
          onChange={(e) => setSearchParams({
            ...searchParams,
            keyword: e.target.value
          })}
        />
        <select
          value={searchParams.platform}
          onChange={(e) => setSearchParams({
            ...searchParams,
            platform: e.target.value
          })}
        >
          <option value="taobao">淘宝</option>
          <option value="tmall">天猫</option>
          <option value="1688">1688</option>
        </select>
        <button onClick={handleSearch} disabled={loading}>
          {loading ? '搜索中...' : '搜索'}
        </button>
      </div>

      {error && <div className="error">{error}</div>}

      {data && (
        <div className="search-results">
          <div className="total">共找到 {data.total} 个商品</div>
          <div className="product-list">
            {data.list.map((product) => (
              <div key={product.id} className="product-item">
                <img src={product.picUrl} alt={product.name} />
                <div className="product-info">
                  <h3>{product.name}</h3>
                  <p>{product.introduction}</p>
                  <div className="price">
                    <span className="current-price">
                      {formatPrice(product.price)}
                    </span>
                    {product.marketPrice > product.price && (
                      <span className="market-price">
                        {formatPrice(product.marketPrice)}
                      </span>
                    )}
                  </div>
                  <div className="shop-info">
                    <span>店铺: {product.shopName}</span>
                    <span>销量: {product.salesCount}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductSearchPage;
```

### Vue 3 组件示例

```vue
<template>
  <div class="product-search">
    <div class="search-form">
      <input
        v-model="searchParams.keyword"
        type="text"
        placeholder="请输入搜索关键词"
      />
      <select v-model="searchParams.platform">
        <option value="taobao">淘宝</option>
        <option value="tmall">天猫</option>
        <option value="1688">1688</option>
      </select>
      <button @click="handleSearch" :disabled="loading">
        {{ loading ? '搜索中...' : '搜索' }}
      </button>
    </div>

    <div v-if="error" class="error">{{ error }}</div>

    <div v-if="data" class="search-results">
      <div class="total">共找到 {{ data.total }} 个商品</div>
      <div class="product-list">
        <div
          v-for="product in data.list"
          :key="product.id"
          class="product-item"
        >
          <img :src="product.picUrl" :alt="product.name" />
          <div class="product-info">
            <h3>{{ product.name }}</h3>
            <p>{{ product.introduction }}</p>
            <div class="price">
              <span class="current-price">
                {{ formatPrice(product.price) }}
              </span>
              <span
                v-if="product.marketPrice > product.price"
                class="market-price"
              >
                {{ formatPrice(product.marketPrice) }}
              </span>
            </div>
            <div class="shop-info">
              <span>店铺: {{ product.shopName }}</span>
              <span>销量: {{ product.salesCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { useProductSearch } from './composables/useProductSearch';

const { loading, data, error, search } = useProductSearch();

const searchParams = reactive<SearchRequest>({
  keyword: '',
  platform: 'taobao',
  language: 'zh',
  pageNo: 1,
  pageSize: 10
});

const handleSearch = () => {
  if (searchParams.keyword.trim()) {
    search(searchParams);
  }
};

const formatPrice = (price: number) => {
  return `¥${(price / 100).toFixed(2)}`;
};
</script>
```

## 常用工具函数

```typescript
// 价格格式化
export const formatPrice = (price: number): string => {
  return `¥${(price / 100).toFixed(2)}`;
};

// 价格转换（元转分）
export const convertToFen = (yuan: number): number => {
  return Math.round(yuan * 100);
};

// 平台名称映射
export const platformNames: Record<string, string> = {
  taobao: '淘宝',
  tmall: '天猫',
  '1688': '阿里巴巴',
  jd: '京东',
  pdd: '拼多多'
};

// 来源平台编码映射
export const sourceCodeNames: Record<number, string> = {
  1: '淘宝',
  2: '天猫',
  3: '阿里巴巴',
  4: '京东',
  5: '拼多多',
  0: '未知平台'
};

// 排序选项
export const sortOptions = [
  { value: 'default', label: '默认排序' },
  { value: 'price_asc', label: '价格升序' },
  { value: 'price_desc', label: '价格降序' },
  { value: 'sales', label: '销量排序' },
  { value: 'newest', label: '最新商品' }
];

// 语言选项
export const languageOptions = [
  { value: 'zh', label: '中文' },
  { value: 'en', label: 'English' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'es', label: 'Español' }
];
```

## 错误处理最佳实践

```typescript
// 统一错误处理
export const handleApiError = (error: any): string => {
  if (error.response) {
    // 服务器响应错误
    const { code, msg } = error.response.data;
    switch (code) {
      case 1008008000:
        return '商品搜索失败，请稍后重试';
      case 1008008001:
        return '获取商品详情失败，请稍后重试';
      case 400:
        return '请求参数错误，请检查输入';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '没有权限访问该资源';
      case 500:
        return '服务器内部错误，请稍后重试';
      default:
        return msg || '操作失败，请稍后重试';
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络设置';
  } else {
    // 其他错误
    return error.message || '未知错误';
  }
};
```
