package cn.iocoder.yudao.module.product.framework.crawler.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 爬虫系统配置属性
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "yudao.crawler")
@Validated
@Data
public class CrawlerProperties {

    /**
     * 爬虫系统基础URL
     */
    @NotEmpty(message = "爬虫系统基础URL不能为空")
    private String baseUrl = "http://localhost:8000/api/v1/upstream";

    /**
     * 认证Token
     */
    @NotEmpty(message = "认证Token不能为空")
    private String token = "your-token-here";

    /**
     * 连接超时时间（毫秒）
     */
    @NotNull(message = "连接超时时间不能为空")
    private Integer connectTimeout = 5000;

    /**
     * 读取超时时间（毫秒）
     */
    @NotNull(message = "读取超时时间不能为空")
    private Integer readTimeout = 30000;

    /**
     * 是否启用缓存
     */
    private Boolean cacheEnabled = true;

    /**
     * 搜索结果缓存时间（秒）
     */
    private Integer searchCacheTime = 1800; // 30分钟

    /**
     * 商品详情缓存时间（秒）
     */
    private Integer detailCacheTime = 3600; // 1小时

}
