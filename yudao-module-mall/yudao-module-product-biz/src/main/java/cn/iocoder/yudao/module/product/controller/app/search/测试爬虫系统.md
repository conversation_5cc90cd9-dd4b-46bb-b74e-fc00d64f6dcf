# 测试爬虫系统连接

## 直接测试爬虫系统

### 1. 测试健康检查
```bash
curl -X GET "http://localhost:8000/api/v1/upstream/health"
```

### 2. 测试搜索接口
```bash
curl -X POST "http://localhost:8000/api/v1/upstream/search" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -d '{
    "keyword": "小叶紫檀",
    "platform": "taobao",
    "language": "zh",
    "page": 1,
    "page_size": 10,
    "sort": "default"
  }'
```

### 3. 测试统计接口
```bash
curl -X GET "http://localhost:8000/api/v1/upstream/stats" \
  -H "Authorization: Bearer your-token-here"
```

## 常见问题排查

### 1. 检查爬虫系统是否运行
```bash
# 检查端口是否监听
netstat -an | grep 8000
# 或者
lsof -i :8000
```

### 2. 检查网络连接
```bash
# 测试连通性
telnet localhost 8000
# 或者
nc -zv localhost 8000
```

### 3. 检查防火墙设置
```bash
# Windows
netsh advfirewall firewall show rule name="Port 8000"

# Linux
iptables -L | grep 8000
```

## 配置检查清单

- [ ] 爬虫系统是否在 localhost:8000 运行
- [ ] 认证Token是否正确
- [ ] 网络连接是否正常
- [ ] 防火墙是否阻止连接
- [ ] 爬虫系统日志是否有错误

## 预期响应格式

### 健康检查响应
```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "healthy",
    "timestamp": "2025-06-18T13:49:22.123Z",
    "version": "1.0.0"
  }
}
```

### 搜索响应
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "products": [...],
    "total": 50,
    "page": 1,
    "page_size": 10,
    "platform": "taobao",
    "keyword": "小叶紫檀",
    "timestamp": "2025-06-18T13:49:22.123Z"
  }
}
```
