package cn.iocoder.yudao.module.product.controller.app.spu.vo;

import lombok.Data;

import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 用户App 自定义商品转换Request VO
 * @author: Ding<PERSON>ia<PERSON>
 * @create: 2025-07-08 14:34
 **/
@Data
public class AppCustomProductTransitionReqVO {
   //* @param {string} orderData.productName - 商品名称
   //* @param {string} orderData.productLink - 商品链接
   //* @param {string} orderData.specifications - 规格说明
   //* @param {string} orderData.productNotes - 商品备注
   //* @param {Array} orderData.images - 商品图片数组
   //* @param {number} orderData.unitPrice - 单价（分）
   //* @param {number} orderData.quantity - 数量
   //* @param {number} orderData.domesticShipping - 国内运费（分）
   //* @param {string} orderData.currency - 货币类型，默认CNY

    private String name;
    private String sourceLink;
    private String specifications;
    private String memo;
    private List<String> images;
    private Integer price;
    private Integer count;
    private Integer freight;

}
