package cn.iocoder.yudao.module.product.controller.app.category;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.product.controller.app.category.vo.AppCategoryRespVO;
import cn.iocoder.yudao.module.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.product.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.product.service.category.ProductCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.string.StrUtils.generateSlug;

@Tag(name = "用户 APP - 商品分类")
@RestController
@RequestMapping("/product/category")
@Validated
public class AppCategoryController {

    @Resource
    private ProductCategoryService categoryService;

    @GetMapping("/list")
    @Operation(summary = "获得商品分类列表")
    @PermitAll
    @Cacheable(cacheNames = RedisKeyConstants.CATEGORY_LIST+"#86400",key = "'default'") //24小时缓存
    public CommonResult<List<AppCategoryRespVO>> getProductCategoryList() {
        List<ProductCategoryDO> list = categoryService.getEnableCategoryList();
        list.sort(Comparator.comparing(ProductCategoryDO::getSort));
        return success(BeanUtils.toBean(list, AppCategoryRespVO.class));
    }

    @GetMapping("/list-by-ids")
    @Operation(summary = "获得商品分类列表，指定编号")
    @Parameter(name = "ids", description = "商品分类编号数组", required = true)
    @PermitAll
    public CommonResult<List<AppCategoryRespVO>> getProductCategoryList(@RequestParam("ids") List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return success(Collections.emptyList());
        }
        List<ProductCategoryDO> list = categoryService.getEnableCategoryList(ids);
        list.sort(Comparator.comparing(ProductCategoryDO::getSort));
        return success(BeanUtils.toBean(list, AppCategoryRespVO.class));
    }

    @GetMapping("/urls/{domain}")
    @Operation(summary = "获取所有分类Url列表")
    @PermitAll
    @Cacheable(cacheNames = RedisKeyConstants.CATEGORY_URLS+"#432000",key = "#domain") //120小时缓存
    public List<String> getCategoryUrls(@PathVariable("domain") String domain){

        List<ProductCategoryDO> list = categoryService.getEnableCategoryList();
        List<String> categoryUrls = new ArrayList<>();

        // 拼接每个商品的完整 URL
        for (ProductCategoryDO category : list) {
            String categoryUrl = formatCategoryUrl(category,domain);
            categoryUrls.add(categoryUrl);
        }

        return categoryUrls;
    }

    private String formatCategoryUrl(ProductCategoryDO category,String domain) {
        return "https://www."+domain + ".com/category/" + category.getId()+"/"+generateSlug(category.getName());
    }


}
