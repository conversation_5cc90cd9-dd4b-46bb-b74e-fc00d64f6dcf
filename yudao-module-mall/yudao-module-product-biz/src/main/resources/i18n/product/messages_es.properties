# 商品分类相关
product.category.not_exists=La categoría no existe
product.category.parent_not_exists=La categoría padre no existe
product.category.parent_not_first_level=La categoría padre no puede ser de segundo nivel
product.category.exists_children=No se puede eliminar porque existen subcategorías
product.category.disabled=La categoría ({}) está deshabilitada y no se puede usar
product.category.have_bind_spu=No se puede eliminar porque hay productos en esta categoría

# 商品品牌相关
product.brand.not_exists=La marca no existe
product.brand.disabled=La marca está deshabilitada
product.brand.name_exists=El nombre de la marca ya existe

# 商品属性项
product.property.not_exists=El atributo no existe
product.property.exists=El nombre del atributo ya existe
product.property.delete_fail_value_exists=No se puede eliminar porque existen valores de atributo

# 商品属性值
product.property_value.not_exists=El valor del atributo no existe
product.property_value.exists=El nombre del valor del atributo ya existe

# 商品 SPU
product.spu.not_exists=El SPU del producto no existe
product.spu.save_fail_category_level_error=Categoría del producto incorrecta: debe ser de segundo nivel o inferior
product.spu.save_fail_coupon_template_not_exists=Error al guardar el SPU del producto: la plantilla del cupón no existe
product.spu.not_enable=El SPU del producto [{}] no está activo
product.spu.not_recycle=El SPU del producto no está en la papelera

# 商品 SKU
product.sku.not_exists=El SKU del producto no existe
product.sku.properties_duplicated=La combinación de atributos del SKU está duplicada
product.spu.attr_numbers_must_be_equals=Los atributos de cada SKU dentro de un SPU deben ser consistentes
product.spu.sku_not_duplicate=Cada SKU dentro de un SPU debe ser único
product.sku.stock_not_enough=Stock insuficiente para el SKU del producto

# 商品评价
product.comment.not_exists=La valoración del producto no existe
product.comment.order_exists=Ya existe una valoración para este pedido

# 商品收藏
product.favorite.exists=El producto ya ha sido agregado a favoritos
product.favorite.not_exists=El producto en favoritos no existe
