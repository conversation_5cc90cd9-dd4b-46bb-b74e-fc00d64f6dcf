# 商品分类相关
product.category.not_exists=الفئة غير موجودة
product.category.parent_not_exists=الفئة الرئيسية غير موجودة
product.category.parent_not_first_level=لا يمكن أن تكون الفئة الرئيسية من المستوى الثاني
product.category.exists_children=لا يمكن الحذف لوجود فئات فرعية
product.category.disabled=الفئة ({}) معطلة ولا يمكن استخدامها
product.category.have_bind_spu=لا يمكن الحذف لوجود منتجات ضمن هذه الفئة

# 商品品牌相关
product.brand.not_exists=العلامة التجارية غير موجودة
product.brand.disabled=العلامة التجارية معطلة
product.brand.name_exists=اسم العلامة التجارية موجود بالفعل

# 商品属性项
product.property.not_exists=العنصر الخاصية غير موجود
product.property.exists=اسم الخاصية موجود بالفعل
product.property.delete_fail_value_exists=لا يمكن الحذف لوجود قيم ضمن الخاصية

# 商品属性值
product.property_value.not_exists=قيمة الخاصية غير موجودة
product.property_value.exists=اسم قيمة الخاصية موجود بالفعل

# 商品 SPU
product.spu.not_exists=المنتج SPU غير موجود
product.spu.save_fail_category_level_error=فئة المنتج غير صحيحة: يجب استخدام الفئة من المستوى الثاني أو أدنى
product.spu.save_fail_coupon_template_not_exists=فشل حفظ SPU: القسيمة غير موجودة
product.spu.not_enable=المنتج SPU [{}] غير مفعل
product.spu.not_recycle=المنتج SPU ليس في حالة سلة المهملات

# 商品 SKU
product.sku.not_exists=المنتج SKU غير موجود
product.sku.properties_duplicated=مجموعة خصائص SKU مكررة
product.spu.attr_numbers_must_be_equals=يجب أن تكون خصائص كل SKU ضمن نفس SPU متطابقة
product.spu.sku_not_duplicate=يجب أن يكون كل SKU ضمن SPU فريدًا
product.sku.stock_not_enough=المخزون غير كافٍ لـ SKU الخاص بالمنتج

# 商品评价
product.comment.not_exists=تقييم المنتج غير موجود
product.comment.order_exists=التقييم لهذا الطلب موجود بالفعل

# 商品收藏
product.favorite.exists=تمت إضافة المنتج بالفعل إلى المفضلة
product.favorite.not_exists=المنتج في المفضلة غير موجود
