# 商品分类相关
product.category.not_exists=商品分类不存在
product.category.parent_not_exists=父分类不存在
product.category.parent_not_first_level=父分类不能是二级分类
product.category.exists_children=存在子分类，无法删除
product.category.disabled=商品分类({})已禁用，无法使用
product.category.have_bind_spu=类别下存在商品，无法删除

# 商品品牌相关
product.brand.not_exists=品牌不存在
product.brand.disabled=品牌已禁用
product.brand.name_exists=品牌名称已存在

# 商品属性项
product.property.not_exists=属性项不存在
product.property.exists=属性项的名称已存在
product.property.delete_fail_value_exists=属性项下存在属性值，无法删除

# 商品属性值
product.property_value.not_exists=属性值不存在
product.property_value.exists=属性值的名称已存在

# 商品 SPU
product.spu.not_exists=商品 SPU 不存在
product.spu.save_fail_category_level_error=商品分类不正确，原因：必须使用第二级的商品分类及以下
product.spu.save_fail_coupon_template_not_exists=商品 SPU 保存失败，原因：优惠劵不存在
product.spu.not_enable=商品 SPU【{}】不处于上架状态
product.spu.not_recycle=商品 SPU 不处于回收站状态

# 商品 SKU
product.sku.not_exists=商品 SKU 不存在
product.sku.properties_duplicated=商品 SKU 的属性组合存在重复
product.spu.attr_numbers_must_be_equals=一个 SPU 下的每个 SKU，其属性项必须一致
product.spu.sku_not_duplicate=一个 SPU 下的每个 SKU，必须不重复
product.sku.stock_not_enough=商品 SKU 库存不足

# 商品评价
product.comment.not_exists=商品评价不存在
product.comment.order_exists=订单的商品评价已存在

# 商品收藏
product.favorite.exists=该商品已经被收藏
product.favorite.not_exists=商品收藏不存在
