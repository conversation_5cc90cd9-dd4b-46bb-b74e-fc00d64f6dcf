# ========== Agent Module 1-300-001-000 ==========
agent.stock.not.exists=Stock does not exist

# ========== Agent parcel 1-300-002-000 ==========
agent.parcel.not.exists=Agent parcel does not exist

# ========== Agent Transfer 1-300-003-000 ==========
agent.transfer.not.exists=Agent transfer does not exist

# ========== ?????? 1-300-004-000 ==========
agent.warehouse.not.exists=Warehouse does not exist

# ========== ?????? 1-300-005-000 ==========
agent.delivery.express.not.exists=Delivery express does not exist

# ========== ?????? 1-*********** ==========
agent.category.not.exists=Category does not exist
agent.category.exists.children=Category has children, cannot be deleted
agent.category.parent.error=Cannot set itself as the parent category
agent.category.parent.not.exists=Parent category does not exist
agent.category.parent.is.child= Cannot set its own child category as parent
agent.category.name.zh.duplicate=Category name already exists

# ========== ???????? 1-*********** ==========
agent.transport.company.not.exists=Transport company does not exist

# ========== ???????? 1-*********** ==========
agent.transport.plan.not.exists=Transport plan does not exist

# ========== 代购服务项目 1-*********** ==========
agent.serve.not.exists= Service does not exist

# ========== 代购服务项目 1-*********** ==========
agent.config.not.exists= Config does not exist


# ========== 代购国家 1-*********** ==========
agent.country.not.exists= Country does not exist

# ========== 代购物流公司 1-*********** ==========
agent.logistics.company.not.exists= Logistics company does not exist

# ========== 代购物流公司产品 1-*********** ==========
agent.logistics.product.not.exists= Logistics product does not exist
agent.logistics.product.price.not.exists= Logistics product price does not exist

# ========== 代购物流分区 1-*********** ==========
agent.logistics.zone.not.exists= Logistics zone does not exist