# ========== 代购模块Module 1-300-001-000 ==========
agent.stock.not.exists=库存不存在

# ========== 代购包裹订单 1-300-002-000 ==========
agent.parcel.not.exists=代购包裹不存在
agent.parcel.cancel_fail_status_not_unpaid= 包裹取消失败，订单不是【待支付】状态
agent.parcel.delete_fail_status_not_cancel= 包裹订单删除失败，订单不是【已取消】状态
agent.parcel.deliver.fail.refund.status.not.none = 包裹发货失败 ，订单已退款或部分退款
agent.parcel.deliver.fail.status.not_undelivered = 包裹发货失败，订单不是【待发货】状态
agent.parcel.update_price_fail_paid =  订单调价失败，原因：支付订单已付款，不能调价
agent.parcel.update_price_fail_already =  订单调价失败，原因：已经修改过价格
agent.parcel.update_price_fail_price_error =  订单调价失败，原因：调整后支付价格不能小于 0
agent.parcel.update_address_fail_status_not_delivered = 交易订单修改收货地址失败，原因：订单不是【待发货】状态
agent.parcel.submit.failed.amount_incorrect = 包裹订单提交失败，原因：订单金额不正确
agent.parcel.update_paid_fail_pay_order_id_error = 包裹订单更新支付状态失败，支付单编号不匹配
agent.parcel.update_paid_status_not_unpaid = 订单更新支付状态失败，订单不是【未支付】状态
agent.parcel_item.update_after_sale_status_fail =订单项更新售后状态失败，请重试
agent.parcel.update_paid_fail_pay_order_status_not_success = 订单更新支付状态失败，支付单状态不是【支付成功】状态
agent.parcel.update_paid_fail_pay_price_not_match = 订单更新支付状态失败，支付单金额不匹配

# ========== 代购转运订单 1-300-003-000 ==========
agent.transfer.not.exists=代购转运单不存在

# ========== 代购平台仓库 1-300-004-000 ==========
agent.warehouse.not.exists=仓库不存在

# ========== 代购快递公司 1-300-005-000 ==========
agent.delivery.express.not.exists=快递公司不存在

# ========== 代购商品分类 1-*********** ==========
agent.category.not.exists=分类不存在
agent.category.exists.children=存在子分类，无法删除
agent.category.parent.error=不能设置自己为父分类
agent.category.parent.not.exists= 父分类不存在
agent.category.parent.is.child= 不能设置自己的子分类为父分类
agent.category.name.zh.duplicate= 已经存在该中文名称的分类

# ========== 代购国际货运公司 1-*********** ==========
agent.transport.company.not.exists=国际货运公司不存在

# ========== 代购国际货运方案 1-*********** ==========
agent.transport.plan.not.exists=国际货运方案不存在
agent.transport.plan.fee.not.exists = 国际货运方案价格不存在

# ========== 代购服务项目 1-*********** ==========
agent.serve.not.exists= 代购服务项目不存在

# ========== 代购服务项目 1-*********** ==========
agent.config.not.exists= 代购配置不存在

// ========== 代购售后订单 1-*********** ==========
agent.after.sale.not.exists=代购售后订单不存在

# ========== 代购国家 1-*********** ==========
agent.country.not.exists= 国家不存在

# ========== 代购物流公司 1-*********** ==========
agent.logistics.company.not.exists= 物流公司不存在

# ========== 代购物流公司产品 1-*********** ==========
agent.logistics.product.not.exists= 代购物流公司产品不存在
agent.logistics.product.price.not.exists= 代购物流公司产品价格不存在
agent.logistics.product.price.import.list.is.empty= 导入价格规则列表不能为空

# ========== 代购物流分区 1-*********** ==========
agent.logistics.zone.not.exists= 代购物流分区不存在