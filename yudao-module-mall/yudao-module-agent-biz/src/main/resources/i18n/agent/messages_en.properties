# ========== Agent Module 1-300-001-000 ==========
agent.stock.not.exists=Stock does not exist

# ========== Agent parcel 1-300-002-000 ==========
agent.parcel.not.exists=Agent parcel does not exist
agent.parcel.cancel_fail_status_not_unpaid= Payment order cancel failed, order status is not unpaid
agent.parcel.delete_fail_status_not_cancel= Failed to delete the order, the order is not in "cancelled" status
agent.parcel.deliver.fail.refund.status.not.none = Parcel order delivery failed, refund status is not none
agent.parcel.deliver.fail.status.not_undelivered = Parcel order delivery failed, order status is not undelivered
agent.parcel.update_price_fail_paid = Parcel order adjustment failed, reason: Parcel order has been paid, cannot adjust
agent.parcel.update_price_fail_already = Parcel order adjustment failed, reason: Already adjusted
agent.parcel.update_price_fail_price_error = Parcel order adjustment failed, reason: Adjusted price cannot be less than 0
agent.parcel.update_address_fail_status_not_delivered = Failed to update delivery address, the order is not in "to be delivered" status
agent.parcel.submit.failed.amount_incorrect = Parcel order submission failed, reason: Amount is incorrect
agent.parcel.update_paid_fail_pay_order_id_error =Failed to update order payment status, payment order ID does not match
agent.parcel.update_paid_status_not_unpaid = Failed to update order payment status, the order is not in "unpaid" status
agent.parcel_item.update_after_sale_status_fail =Failed to update after-sale status of the order item, please try again
agent.parcel.update_paid_fail_pay_order_status_not_success = Failed to update order payment status, payment order status is not "successful"
agent.parcel.update_paid_fail_pay_price_not_match = Failed to update order payment status, payment amount does not match

# ========== Agent Transfer 1-300-003-000 ==========
agent.transfer.not.exists=Agent transfer does not exist

# ========== ?????? 1-300-004-000 ==========
agent.warehouse.not.exists=Warehouse does not exist

# ========== ?????? 1-300-005-000 ==========
agent.delivery.express.not.exists=Delivery express does not exist

# ========== ?????? 1-*********** ==========
agent.category.not.exists=Category does not exist
agent.category.exists.children=Category has children, cannot be deleted
agent.category.parent.error=Cannot set itself as the parent category
agent.category.parent.not.exists=Parent category does not exist
agent.category.parent.is.child= Cannot set its own child category as parent
agent.category.name.zh.duplicate=Category name already exists

# ========== ???????? 1-*********** ==========
agent.transport.company.not.exists=Transport company does not exist

# ========== ???????? 1-*********** ==========
agent.transport.plan.not.exists=Transport plan does not exist
agent.transport.plan.fee.not.exists=Transport plan fee does not exist

# ========== 代购服务项目 1-*********** ==========
agent.serve.not.exists=Service does not exist

# ========== 代购服务项目 1-*********** ==========
agent.config.not.exists=Config does not exist

// ========== 代购售后订单 1-*********** ==========
agent.after.sale.not.exists=After sale order does not exist

# ========== 代购国际货运公司 1-*********** ==========
agent.country.not.exists= Country does not exist

# ========== 代购国际货运公司 1-*********** ==========
agent.logistics.company.not.exists= Logistics company does not exist

# ========== 代购物流公司产品 1-*********** ==========
agent.logistics.product.not.exists= Logistics product does not exist
agent.logistics.product.price.not.exists= Logistics product price does not exist
agent.logistics.product.price.import.list.is.empty= Import price rule list cannot be empty

# ========== 代购物流分区 1-*********** ==========
agent.logistics.zone.not.exists= Logistics zone does not exist