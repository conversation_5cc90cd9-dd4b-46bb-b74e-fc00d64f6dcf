# 代购物流产品价格规则导入模板说明

## Excel模板字段说明

| 字段名 | 字段说明 | 是否必填 | 示例值 | 备注 |
|--------|----------|----------|--------|------|
| 国家编码 | 目的地国家编码 | 是 | US | 使用ISO 3166-1 alpha-2标准 |
| 分区编码 | 邮编分区编码 | 否 | Zone1 | 为空表示全国统一价格 |
| 时效 | 运输时效 | 否 | 12-20天 | 描述性文字 |
| 计费方式 | 计费类型 | 是 | WEIGHT | WEIGHT(重量)/VOLUME(体积)/PIECE(件数) |
| 价格类型 | 价格计算类型 | 是 | INCREMENTAL | TIERED(阶梯)/INCREMENTAL(递增) |
| 首重(g)/首件数量 | 首重或首件数量 | 是 | 500 | 单位：克或件 |
| 首重/首件价格(分) | 首重或首件价格 | 是 | 10500 | 单位：分 |
| 续重(g)/续件单位 | 续重或续件单位 | 否 | 200 | 递增模式必填，单位：克或件 |
| 续重/续件价格(分) | 续重或续件价格 | 否 | 2940 | 递增模式必填，单位：分 |
| 最小重量(g) | 最小计费重量 | 否 | 100 | 单位：克 |
| 最大重量(g) | 最大计费重量 | 否 | 30000 | 单位：克 |
| 阶梯价格配置JSON | 阶梯价格配置 | 否 | 见下方示例 | 阶梯模式必填 |
| 燃油费率 | 燃油附加费率 | 否 | 0.15 | 小数格式，如15%填0.15 |
| 挂号费(分) | 挂号费 | 否 | 0 | 单位：分 |
| 操作费(分) | 操作费 | 否 | 0 | 单位：分 |
| 服务费(分) | 服务费 | 否 | 0 | 单位：分 |
| 清关费(分) | 清关费 | 否 | 0 | 单位：分 |
| 是否预收关税 | 是否预收关税 | 否 | 0 | 0-否，1-是 |
| 关税税率 | 关税税率 | 否 | 0.00 | 小数格式，如10%填0.10 |
| 生效日期 | 价格规则生效日期 | 否 | 2024-01-01 00:00:00 | 日期时间格式 |
| 失效日期 | 价格规则失效日期 | 否 | 2024-12-31 23:59:59 | 日期时间格式 |

## 阶梯价格配置JSON格式示例

```json
[
  {
    "tierStart": 0,
    "tierEnd": 500,
    "unitPrice": 12000,
    "description": "0-500g"
  },
  {
    "tierStart": 501,
    "tierEnd": 1000,
    "unitPrice": 8000,
    "description": "501-1000g"
  },
  {
    "tierStart": 1001,
    "tierEnd": null,
    "unitPrice": 6000,
    "description": "1001g以上"
  }
]
```

## 导入注意事项

1. **产品编号自动设置**：导入时会自动设置为当前选中的产品编号，无需在Excel中填写
2. **计费方式和价格类型**：必须选择正确的组合
3. **递增模式**：选择INCREMENTAL时，续重单位和续重价格必填
4. **阶梯模式**：选择TIERED时，阶梯价格配置JSON必填
5. **唯一性约束**：同一产品的同一国家同一分区只能有一条价格规则
6. **更新模式**：如果选择支持更新，会覆盖已存在的价格规则
7. **默认值**：排序默认为0，状态默认为启用(1)

## 示例数据

| 国家编码 | 分区编码 | 计费方式 | 价格类型 | 首重(g) | 首重价格(分) | 续重(g) | 续重价格(分) |
|---------|---------|---------|---------|---------|-------------|---------|-------------|
| US | | WEIGHT | INCREMENTAL | 500 | 10500 | 200 | 2940 |
| AU | Zone1 | WEIGHT | TIERED | 500 | 12000 | | |

