package cn.iocoder.yudao.module.agent.framework.parcel.core.annotations;

import cn.iocoder.yudao.module.agent.enums.parcel.AgentParcelOperateTypeEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.METHOD;

/**
 * @program: ruoyi-vue-pro
 * @description: 代购包裹订单操作日志 AOP 注解
 * @author: Ding<PERSON>iao
 * @create: 2025-05-20 19:37
 **/
@Target({METHOD, ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AgentParcelLog {

    /**
     * 操作类型
     */
    AgentParcelOperateTypeEnum operateType();
}
