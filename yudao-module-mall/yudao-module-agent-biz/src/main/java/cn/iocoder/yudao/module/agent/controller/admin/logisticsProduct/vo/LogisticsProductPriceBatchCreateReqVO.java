package cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 代购物流产品价格规则批量创建 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 代购物流产品价格规则批量创建 Request VO")
@Data
public class LogisticsProductPriceBatchCreateReqVO {

    @Schema(description = "产品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "产品编号不能为空")
    private Long productId;

    @Schema(description = "国家编码列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\"KH\",\"GB\",\"DE\",\"MV\"]")
    @NotEmpty(message = "国家编码列表不能为空")
    private List<String> countryCodes;

    @Schema(description = "分区编码", example = "Zone1")
    private String zoneCode;

    @Schema(description = "时效", example = "12-20天")
    private String transitTime;

    @Schema(description = "时效说明", example = "{\"deliveryRate\":0.4,\"timelinessInfos\":[]}")
    private String timelinessInfo;

    @Schema(description = "计费方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "WEIGHT")
    @NotEmpty(message = "计费方式不能为空")
    private String chargeType;

    @Schema(description = "价格类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "INCREMENTAL", allowableValues = {"INCREMENTAL", "TIERED", "TIERED_INCREMENTAL"})
    @NotEmpty(message = "价格类型不能为空")
    private String priceType;

    @Schema(description = "首重(g)/首件数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "500")
    @NotNull(message = "首重/首件数量不能为空")
    private Integer firstUnit;

    @Schema(description = "首重/首件价格(分)", requiredMode = Schema.RequiredMode.REQUIRED, example = "35200")
    @NotNull(message = "首重/首件价格不能为空")
    private Integer firstPrice;

    @Schema(description = "续重(g)/续件单位", example = "200")
    private Integer additionalUnit;

    @Schema(description = "续重/续件价格(分)", example = "2940")
    private Integer additionalPrice;

    @Schema(description = "最小重量(g)", example = "500")
    private Integer minWeight;

    @Schema(description = "最大重量(g)", example = "20000")
    private Integer maxWeight;

    @Schema(description = "阶梯价格配置JSON", example = "[{\"tierStart\":0,\"tierEnd\":500,\"unitPrice\":12000}]")
    private String tieredPrices;

    @Schema(description = "阶梯递增价格配置JSON", example = "[{\"tierStart\":1,\"tierEnd\":15000,\"firstWeight\":100,\"firstPrice\":14210,\"additionalWeight\":100,\"additionalPrice\":2610,\"registrationFee\":0}]")
    private String tieredIncrementalPrices;

    @Schema(description = "燃油费率", example = "0.15")
    private BigDecimal fuelFeeRate;

    @Schema(description = "挂号费(分)", example = "0")
    private Integer registrationFee;

    @Schema(description = "操作费(分)", example = "0")
    private Integer operationFee;

    @Schema(description = "服务费(分)", example = "0")
    private Integer serviceFee;

    @Schema(description = "清关费(分)", example = "0")
    private Integer customsFee;

    @Schema(description = "是否预收关税", example = "0")
    private Boolean prepayTariff;

    @Schema(description = "关税税率", example = "0.00")
    private BigDecimal tariffRate;

    @Schema(description = "尺寸限制配置", example = "{\"maxLength\":120,\"maxWidth\":60,\"maxHeight\":60,\"maxGirth\":300}")
    private String sizeRestrictions;

    @Schema(description = "生效时间", example = "2024-01-01 00:00:00")
    private LocalDateTime effectiveTime;

    @Schema(description = "失效时间", example = "2024-12-31 23:59:59")
    private LocalDateTime expireTime;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "开启状态", example = "0")
    private Integer status;

}
