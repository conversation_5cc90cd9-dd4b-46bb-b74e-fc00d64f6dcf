package cn.iocoder.yudao.module.agent.controller.admin.warehouse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 代购平台仓库新增/修改 Request VO")
@Data
public class AgentWarehouseSaveReqVO {

    @Schema(description = "仓库编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12926")
    private Long id;

    @Schema(description = "仓库编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "仓库编码不能为空")
    private String code;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "仓库名称不能为空")
    private String name;

    @Schema(description = "仓库地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "仓库地址不能为空")
    private String address;

    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "联系电话不能为空")
    private String phone;

    @Schema(description = "收件人", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "收件人不能为空")
    private String recipient;

    @Schema(description = "免费存放天数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "免费存放天数不能为空")
    private Integer freeDays;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Long sort;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "开启状态不能为空")
    private Integer status;

    @Schema(description = "是否默认", example = "2")
    private Boolean defaultStatus;

}