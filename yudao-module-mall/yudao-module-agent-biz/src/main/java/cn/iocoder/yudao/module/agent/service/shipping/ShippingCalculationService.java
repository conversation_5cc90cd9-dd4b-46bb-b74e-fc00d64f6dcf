package cn.iocoder.yudao.module.agent.service.shipping;

import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsZone.LogisticsZoneDO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingCalculationReqBO;
import cn.iocoder.yudao.module.agent.util.SizeRestrictionUtil;

import java.math.BigDecimal;

/**
 * 运费计算服务接口
 *
 * <AUTHOR>
 */
public interface ShippingCalculationService {

    /**
     * 计算运费
     *
     * @param countryCode 国家编码
     * @param postalCode 邮编
     * @param productId 物流产品ID
     * @param weight 重量(kg)
     * @param length 长度(cm)
     * @param width 宽度(cm)
     * @param height 高度(cm)
     * @return 运费计算结果
     */
    ShippingCalculationResult calculateShipping(String countryCode, String postalCode, 
                                               Long productId, BigDecimal weight,
                                               BigDecimal length, BigDecimal width, BigDecimal height);


    /**
     * 检查配送限制
     *
     * @param countryCode 国家编码
     * @param postalCode 邮编
     * @param productId 物流产品ID
     * @return 配送限制结果
     */
    ShippingRestrictionResult checkShippingRestriction(String countryCode, String postalCode, Long productId);

    /**
     * 检查配送限制（支持完整地址信息）
     *
     * @param countryCode 国家编码
     * @param postalCode 邮编
     * @param stateProvince 州/省
     * @param city 城市
     * @param district 区/县
     * @param productId 物流产品ID
     * @return 配送限制结果
     */
    ShippingRestrictionResult checkShippingRestrictionWithAddress(String countryCode, String postalCode,
                                                                String stateProvince, String city, String district,
                                                                Long productId);




    /**
     * 运费计算结果
     */
    class ShippingCalculationResult {
        private boolean canShip;
        private BigDecimal baseFee;
        private BigDecimal remoteFee;
        private BigDecimal totalFee;
        private String restrictionType;
        private String restrictionMessage;
        private LogisticsZoneDO matchedZone;
        private SizeRestrictionUtil.SizeCheckResult sizeCheckResult;

        // 构造函数
        public ShippingCalculationResult(boolean canShip, BigDecimal baseFee, BigDecimal remoteFee,
                                       BigDecimal totalFee, String restrictionType, String restrictionMessage,
                                       LogisticsZoneDO matchedZone, SizeRestrictionUtil.SizeCheckResult sizeCheckResult) {
            this.canShip = canShip;
            this.baseFee = baseFee;
            this.remoteFee = remoteFee;
            this.totalFee = totalFee;
            this.restrictionType = restrictionType;
            this.restrictionMessage = restrictionMessage;
            this.matchedZone = matchedZone;
            this.sizeCheckResult = sizeCheckResult;
        }

        // Getters
        public boolean isCanShip() { return canShip; }
        public BigDecimal getBaseFee() { return baseFee; }
        public BigDecimal getRemoteFee() { return remoteFee; }
        public BigDecimal getTotalFee() { return totalFee; }
        public String getRestrictionType() { return restrictionType; }
        public String getRestrictionMessage() { return restrictionMessage; }
        public LogisticsZoneDO getMatchedZone() { return matchedZone; }
        public SizeRestrictionUtil.SizeCheckResult getSizeCheckResult() { return sizeCheckResult; }
    }

    /**
     * 配送限制结果
     */
    class ShippingRestrictionResult {
        private boolean canShip;
        private String restrictionType;
        private String restrictionMessage;
        private LogisticsZoneDO matchedZone;

        public ShippingRestrictionResult(boolean canShip, String restrictionType, 
                                       String restrictionMessage, LogisticsZoneDO matchedZone) {
            this.canShip = canShip;
            this.restrictionType = restrictionType;
            this.restrictionMessage = restrictionMessage;
            this.matchedZone = matchedZone;
        }

        // Getters
        public boolean isCanShip() { return canShip; }
        public String getRestrictionType() { return restrictionType; }
        public String getRestrictionMessage() { return restrictionMessage; }
        public LogisticsZoneDO getMatchedZone() { return matchedZone; }
    }
}
