package cn.iocoder.yudao.module.agent.controller.admin.deliveryexpress.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 代购快递公司分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AgentDeliveryExpressPageReqVO extends PageParam {

    @Schema(description = "快递公司编码")
    private String code;

    @Schema(description = "快递公司名称", example = "王五")
    private String name;

    @Schema(description = "状态", example = "2")
    private Integer status;

}