package cn.iocoder.yudao.module.agent.controller.app.country;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.agent.controller.app.country.vo.AppCountryRespVO;
import cn.iocoder.yudao.module.agent.service.country.AgentCountryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLang;

@Tag(name = "用户 APP - 代购服务国家")
@RestController
@RequestMapping("/agent/country")
@Validated
public class AppAgentCountryController {

    @Resource
    private AgentCountryService agentCountryService;


    @GetMapping("/list")
    @Operation(summary = "获得代购服务国家")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<List<AppCountryRespVO>> getCountries() {
        List<AppCountryRespVO> countries = agentCountryService.getCountries(getCurrentLang());
        return success(countries);
    }



}