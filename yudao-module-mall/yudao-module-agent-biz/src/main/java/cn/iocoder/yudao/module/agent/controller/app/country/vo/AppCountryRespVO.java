package cn.iocoder.yudao.module.agent.controller.app.country.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 APP - 代购服务国家 Response VO")
@Data
public class AppCountryRespVO {

    @Schema(description = "国家编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "图标地址", example = "https://www.iocoder.cn")
    private String flagUrl;

    @Schema(description = "当前语言名称")
    private String name;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String nameEn;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "热门", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean hot;


}