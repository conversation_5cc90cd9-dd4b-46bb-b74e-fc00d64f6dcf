package cn.iocoder.yudao.module.agent.controller.app.transfer.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "用户 APP - 代购转运单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppAgentTransferRespVO {

    @Schema(description = "转运单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16496")
    @ExcelProperty("转运单编号")
    private Long id;

    @Schema(description = "转运单流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("转运单流水号")
    private String no;

    @Schema(description = "仓库编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("仓库编号")
    private Long warehouseId;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19625")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "发货物流公司编号", example = "32487")
    @ExcelProperty("发货物流公司编号")
    private Long logisticsId;

    @Schema(description = "物流公司单号")
    @ExcelProperty("物流公司单号")
    private String logisticsNo;

    @Schema(description = "物流备注", example = "你说的对")
    @ExcelProperty("物流备注")
    private String logisticsRemark;

    @Schema(description = "转运的商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "17170")
    @ExcelProperty("转运的商品数量")
    private Integer itemCount;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    //@DictFormat("agent_transfer_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "商品项数组", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品不能为空")
    private List<Item> items;

    @Data
    @Schema(description = "用户 App - 转运单商品项")
    @Valid
    public static class Item {
        /**
         * 转运商品分类编号
         */
        private Long categoryId;
        /**
         * 商品编号
         */
        private Long productId;
        /**
         * 转运商品名称
         */
        private String productName;
        /**
         * 转运商品链接
         */
        private String productUrl;
        /**
         * 转运商品图片
         */
        private String picUrl;
        /**
         * 转运数量
         */
        private Integer count;
        /**
         * 商品价格（单），单位：分
         */
        private Integer price;
        /**
         * 商品重量，单位：kg 千克
         */
        private BigDecimal weight;
        /**
         * 商品体积，单位：cm^3
         */
        private BigDecimal volume;
        /**
         * 长，单位：cm
         */
        private BigDecimal length;
        /**
         * 宽，单位：cm
         */
        private BigDecimal width;
        /**
         * 高，单位：cm
         */
        private BigDecimal height;
        /**
         * 备注
         */
        private String remark;
    }

}