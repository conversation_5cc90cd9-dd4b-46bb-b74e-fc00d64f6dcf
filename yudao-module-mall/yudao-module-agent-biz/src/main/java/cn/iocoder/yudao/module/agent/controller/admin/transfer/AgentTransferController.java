package cn.iocoder.yudao.module.agent.controller.admin.transfer;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.admin.transfer.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.transfer.AgentTransferDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.transferitem.AgentTransferItemDO;
import cn.iocoder.yudao.module.agent.service.transfer.AgentTransferService;

@Tag(name = "管理后台 - 代购转运单")
@RestController
@RequestMapping("/agent/transfer")
@Validated
public class AgentTransferController {

    @Resource
    private AgentTransferService transferService;

    @PostMapping("/create")
    @Operation(summary = "创建代购转运单")
    @PreAuthorize("@ss.hasPermission('agent:transfer:create')")
    public CommonResult<Long> createTransfer(@Valid @RequestBody AgentTransferSaveReqVO createReqVO) {
        return success(transferService.createTransfer(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购转运单")
    @PreAuthorize("@ss.hasPermission('agent:transfer:update')")
    public CommonResult<Boolean> updateTransfer(@Valid @RequestBody AgentTransferSaveReqVO updateReqVO) {
        transferService.updateTransfer(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购转运单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:transfer:delete')")
    public CommonResult<Boolean> deleteTransfer(@RequestParam("id") Long id) {
        transferService.deleteTransfer(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购转运单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:transfer:query')")
    public CommonResult<AgentTransferRespVO> getTransfer(@RequestParam("id") Long id) {
        AgentTransferDO transfer = transferService.getTransfer(id);
        return success(BeanUtils.toBean(transfer, AgentTransferRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购转运单分页")
    @PreAuthorize("@ss.hasPermission('agent:transfer:query')")
    public CommonResult<PageResult<AgentTransferRespVO>> getTransferPage(@Valid AgentTransferPageReqVO pageReqVO) {
        PageResult<AgentTransferDO> pageResult = transferService.getTransferPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgentTransferRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购转运单 Excel")
    @PreAuthorize("@ss.hasPermission('agent:transfer:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransferExcel(@Valid AgentTransferPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentTransferDO> list = transferService.getTransferPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购转运单.xls", "数据", AgentTransferRespVO.class,
                        BeanUtils.toBean(list, AgentTransferRespVO.class));
    }

    // ==================== 子表（代购转运单明细） ====================

    @GetMapping("/transfer-item/list-by-transfer-id")
    @Operation(summary = "获得代购转运单明细列表")
    @Parameter(name = "transferId", description = "转运单编号")
    @PreAuthorize("@ss.hasPermission('agent:transfer:query')")
    public CommonResult<List<AgentTransferItemDO>> getTransferItemListByTransferId(@RequestParam("transferId") Long transferId) {
        return success(transferService.getTransferItemListByTransferId(transferId));
    }

}