package cn.iocoder.yudao.module.agent.controller.app.parcel.vo;

import cn.iocoder.yudao.module.agent.controller.app.base.property.AppProductPropertyValueDetailRespVO;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 用户App - 包裹订单结算信息 Response VO
 * @author: DingXiao
 * @create: 2025-04-30 15:14
 **/
@Schema(description = "用户App - 包裹订单结算信息 Response VO")
@Data
public class AppAgentParcelSettlementRespVO {

    @Schema(description = "交易类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1") //
    private Integer type;

    @Schema(description = "优惠劵数组", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Coupon> coupons; // 可用 + 不可用

    @Schema(description = "费用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Price price;

    @Schema(description = "收件地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private Address address;

    @Schema(description = "已使用的积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer usePoint;

    @Schema(description = "总积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer totalPoint;

    @Schema(description = "包裹项数组", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Item> items;

    @Schema(description = "物流方案数组", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<LogisticsPlan> logisticsPlans;

    @Schema(description = "包裹信息")
    private Info info;


    @Schema(description = "包裹项")
    @Data
    public static class Item {

        // ========== SPU 信息 ==========

        @Schema(description = "SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
        private Long spuId;
        @Schema(description = "SPU 名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "Apple iPhone 12")
        private String spuName;

        // ========== SKU 信息 ==========

        @Schema(description = "SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
        private Integer skuId;
        @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/1.png")
        private String picUrl;
        @Schema(description = "属性数组", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
        private List<AppProductPropertyValueDetailRespVO> properties;

        // ========== 库存信息 ==========

        @Schema(description = "库存编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
        private Long stockId;

        @Schema(description = "购买数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer count;

        @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer stockCount;

        @Schema(description = "重量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer weight;

        @Schema(description = "体积", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private BigDecimal volume;

    }

    @Schema(description = "费用（合计）")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Price {

        @Schema(description = "商品原价（总），单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "500")
        private Integer totalPrice;

        @Schema(description = "订单优惠（总），单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "66")
        private Integer discountPrice;

        @Schema(description = "运费金额，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "50")
        private Integer deliveryPrice;

        @Schema(description = "优惠劵减免金额，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
        private Integer couponPrice;

        @Schema(description = "积分抵扣的金额，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "50")
        private Integer pointPrice;

        @Schema(description = "VIP 减免金额，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "30")
        private Integer vipPrice;

        @Schema(description = "保险金额，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "30")
        private Integer insurancePrice;

        @Schema(description = "服务金额，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "30")
        private Integer servicePrice;

        @Schema(description = "平台佣金，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "30")
        private Integer platformPrice;

        @Schema(description = "实际支付金额（总），单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "450")
        private Integer payPrice;

    }

    @Schema(description = "地址信息")
    @Data
    public static class Address {

        @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Long id;

        @Schema(description = "收件人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "小王")
        private String name;

        @Schema(description = "手机区号", requiredMode = Schema.RequiredMode.REQUIRED, example = "86")
        private String phoneCode;

        @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
        private String mobile;

        @Schema(description = "地区编号", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long areaId;
        @Schema(description = "地区名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "上海上海市普陀区")
        private String areaName;

        @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "望京悠乐汇 A 座")
        private String detailAddress;

        @Schema(description = "是否默认收件地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
        private Boolean defaultStatus;

        @Schema(description = "邮编", requiredMode = Schema.RequiredMode.REQUIRED, example = "221700")
        private String postCode;

        @Schema(description = "国家编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "CN")
        private String countryCode;

    }

    @Schema(description = "优惠劵信息")
    @Data
    public static class Coupon {

        @Schema(description = "优惠劵编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Long id;

        @Schema(description = "优惠劵名", requiredMode = Schema.RequiredMode.REQUIRED, example = "春节送送送")
        private String name;

        @Schema(description = "是否设置满多少金额可用", requiredMode = Schema.RequiredMode.REQUIRED, example = "100") // 单位：分；0 - 不限制
        private Integer usePrice;

        @Schema(description = "固定日期 - 生效开始时间")
        private LocalDateTime validStartTime;

        @Schema(description = "固定日期 - 生效结束时间")
        private LocalDateTime validEndTime;

        @Schema(description = "优惠类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer discountType;

        @Schema(description = "折扣百分比", example = "80") //  例如说，80% 为 80
        private Integer discountPercent;

        @Schema(description = "优惠金额", example = "10")
        private Integer discountPrice;

        @Schema(description = "折扣上限", example = "100") // 单位：分，仅在 discountType 为 PERCENT 使用
        private Integer discountLimitPrice;

        @Schema(description = "是否可用", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
        private Boolean match;

        @Schema(description = "不可用原因", example = "优惠劵已过期")
        private String mismatchReason;

    }

    @Schema(description = "信息")
    @Data
    public static class Info {

        @Schema(description = "数量")
        private Integer count;

        @Schema(description = "重量")
        private Integer weight;

        @Schema(description = "包装重量")
        private Integer packageWeight;

        @Schema(description = "体积")
        private BigDecimal volume;

    }

    @Data
    public static class LogisticsPlan {

        @Schema(description = "物流产品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20623")
        private Long productId;

        @Schema(description = "物流产品价格编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20623")
        private Long priceId;

        @Schema(description = "方案名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
        private String name;

        @Schema(description = "方案编码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String code;

        @Schema(description = "渠道编码")
        private String channelCode;

        @Schema(description = "方案类型(极速，标准，经济)", example = "2")
        private String planType;

        @Schema(description = "运输公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
        private String companyName;

        @Schema(description = "运输方式（空运，陆运，海运等）")
        private String transportMethod;

        @Schema(description = "是否可以带电")
        private Boolean battery;

        @Schema(description = "备注", example = "你说的对")
        private String remark;

        //基础运费
        private Integer basePrice;
        //时效
        private String transitTime;
        //总运费金额
        private Integer totalFee;
        //是否含税
        private Boolean taxInclude;
        //免费保险
        private Boolean freeInsure;

    }

}
