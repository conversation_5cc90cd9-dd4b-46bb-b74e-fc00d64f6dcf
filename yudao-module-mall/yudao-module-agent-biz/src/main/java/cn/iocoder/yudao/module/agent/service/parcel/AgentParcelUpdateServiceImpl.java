package cn.iocoder.yudao.module.agent.service.parcel;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.number.MoneyUtils;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelRemarkReqVO;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelTransportReqVO;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelUpdateAddressReqVO;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelUpdatePriceReqVO;
import cn.iocoder.yudao.module.agent.controller.app.parcel.vo.AppAgentParcelCreateReqVO;
import cn.iocoder.yudao.module.agent.controller.app.parcel.vo.AppAgentParcelSettlementReqVO;
import cn.iocoder.yudao.module.agent.controller.app.parcel.vo.AppAgentParcelSettlementRespVO;
import cn.iocoder.yudao.module.agent.convert.parcel.AgentParcelConvert;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductPriceDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelItemDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.stock.AgentStockDO;
import cn.iocoder.yudao.module.agent.dal.mysql.parcel.AgentParcelItemMapper;
import cn.iocoder.yudao.module.agent.dal.mysql.parcel.AgentParcelMapper;
import cn.iocoder.yudao.module.agent.dal.redis.no.AgentNoRedisDAO;
import cn.iocoder.yudao.module.agent.enums.parcel.*;
import cn.iocoder.yudao.module.agent.framework.parcel.config.AgentParcelProperties;
import cn.iocoder.yudao.module.agent.framework.parcel.core.annotations.AgentParcelLog;
import cn.iocoder.yudao.module.agent.framework.parcel.core.utils.AgentParcelLogUtils;
import cn.iocoder.yudao.module.agent.mq.producer.AgentParcelProducer;
import cn.iocoder.yudao.module.agent.service.logisticsProduct.LogisticsProductService;
import cn.iocoder.yudao.module.agent.service.message.AgentMessageService;
import cn.iocoder.yudao.module.agent.service.message.bo.AgentParcelMessageWhenDeliveryOrderReqBO;
import cn.iocoder.yudao.module.agent.service.parcel.handler.AgentParcelHandler;
import cn.iocoder.yudao.module.agent.service.price.AgentPriceService;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateReqBO;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateRespBO;
import cn.iocoder.yudao.module.agent.service.price.calculator.AgentPriceCalculatorHelper;
import cn.iocoder.yudao.module.agent.service.stock.AgentStockService;
import cn.iocoder.yudao.module.member.api.address.MemberAddressApi;
import cn.iocoder.yudao.module.member.api.address.dto.MemberAddressRespDTO;
import cn.iocoder.yudao.module.pay.api.order.PayOrderApi;
import cn.iocoder.yudao.module.pay.api.order.dto.PayOrderCreateReqDTO;
import cn.iocoder.yudao.module.pay.api.order.dto.PayOrderRespDTO;
import cn.iocoder.yudao.module.pay.enums.order.PayOrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.getSumValue;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLanguage;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getTerminal;
import static cn.iocoder.yudao.module.agent.enums.ErrorCodeConstants.*;

/**
 * @program: ruoyi-vue-pro
 * @description: 代购包裹订单写服务 实现类
 * @author: DingXiao
 * @create: 2025-04-30 15:12
 **/
@Slf4j
@Service
public class AgentParcelUpdateServiceImpl implements AgentParcelUpdateService{


    @Resource
    private MemberAddressApi addressApi;
    @Resource
    private AgentStockService stockService;
    @Resource
    private AgentPriceService agentPriceService;
    @Resource
    private List<AgentParcelHandler> parcelHandlers;
    @Resource
    private AgentParcelMapper  agentParcelMapper;
    @Resource
    private AgentParcelItemMapper agentParcelItemMapper;
    @Resource
    private AgentNoRedisDAO agentNoRedisDAO;
    @Resource
    private AgentParcelProperties agentParcelProperties;
    @Resource
    private PayOrderApi payOrderApi;
    @Resource
    private AgentParcelProducer  agentParcelProducer;
    @Resource
    private LogisticsProductService logisticsProductService;
    @Resource
    private AgentMessageService  agentMessageService;


    @Override
    public AppAgentParcelSettlementRespVO settlementOrder(Long userId, AppAgentParcelSettlementReqVO settlementReqVO) {
        // 1. 获得收货地址
        MemberAddressRespDTO address = getAddress(userId, settlementReqVO.getAddressId());
        if (address != null) {
            settlementReqVO.setAddressId(address.getId());
        }

        // 2. 计算价格
        AgentPriceCalculateRespBO calculateRespBO = calculatePrice(userId, settlementReqVO);

        // 3. 拼接返回
        return AgentParcelConvert.INSTANCE.convert(calculateRespBO, address);
    }


    /**
     * 计算价格
     *
     * @param userId      用户编号
     * @param settlementReqVO 结算请求
     * @return 价格
     */
    private AgentPriceCalculateRespBO calculatePrice(Long userId, AppAgentParcelSettlementReqVO settlementReqVO) {

        // 1. 获得所选的库存物品
        List<AgentStockDO> stockDOList = stockService.getStockList(userId,
                convertSet(settlementReqVO.getItems(), AppAgentParcelSettlementReqVO.Item::getStockId));

        // 2. 计算价格
        AgentPriceCalculateReqBO calculateReqBO = AgentParcelConvert.INSTANCE.convert(userId,settlementReqVO, stockDOList);


        return agentPriceService.calculateParcelPrice(calculateReqBO);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @AgentParcelLog(operateType = AgentParcelOperateTypeEnum.MEMBER_CREATE)
    public AgentParcelDO createParcel(Long userId, AppAgentParcelCreateReqVO createReqVO) {
        // 1.1 价格计算
        AgentPriceCalculateRespBO calculateRespBO = calculatePrice(userId, createReqVO);
        // 1.2 构建订单
        AgentParcelDO order = buildAgentParcel(userId, createReqVO, calculateRespBO);
        List<AgentParcelItemDO> orderItems = buildAgentParcelItems(order, calculateRespBO);

        // 2. 订单创建前的逻辑
        parcelHandlers.forEach(handler -> handler.beforeOrderCreate(order, orderItems));

        // 3. 保存订单
        // 3.1 金额判断
        if(order.getPayPrice()<=0){
            throw  exception(AGENT_PARCEL_SUBMIT_FAIL);
        }
        agentParcelMapper.insert(order);
        orderItems.forEach(orderItem -> orderItem.setParcelId(order.getId()));
        agentParcelItemMapper.insertBatch(orderItems);

        // 4. 订单创建后的逻辑
        afterCreateAgentParcel(order, orderItems, createReqVO);

        return order;
    }

    @Override
    public void updateOrderGiveCouponIds(Long userId, Long orderId, List<Long> giveCouponIds) {
        // 1. 检验订单存在
        AgentParcelDO order = agentParcelMapper.selectParcelByIdAndUserId(orderId, userId);
        if (order == null) {
            throw exception(AGENT_PARCEL_NOT_EXISTS);
        }

        // 2. 更新订单赠送的优惠券编号列表
        agentParcelMapper.updateById(new AgentParcelDO().setId(orderId).setGiveCouponIds(giveCouponIds));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @AgentParcelLog(operateType = AgentParcelOperateTypeEnum.MEMBER_PAY)
    public void updateOrderPaid(Long id, Long payOrderId) {
        // 1.1 校验订单是否存在
        AgentParcelDO parcel = validateOrderExists(id);
        // 1.2 校验订单已支付
        if (!AgentParcelStatusEnum.isUnpaid(parcel.getStatus()) || parcel.getPayStatus()) {
            // 特殊：如果订单已支付，且支付单号相同，直接返回，说明重复回调
            if (ObjectUtil.equals(parcel.getPayOrderId(), payOrderId)) {
                log.warn("[updateOrderPaid][order({}) 已支付，且支付单号相同({})，直接返回]", parcel, payOrderId);
                return;
            }
            log.error("[updateOrderPaid][order({}) 支付单不匹配({})，请进行处理！order 数据是：{}]",
                    id, payOrderId, JsonUtils.toJsonString(parcel));
            throw exception(AGENT_PARCEL_UPDATE_PAID_FAIL_PAY_ORDER_ID_ERROR);
        }

        // 2. 校验支付单的合法性
        PayOrderRespDTO payOrder = validatePayOrderPaid(parcel, payOrderId);

        // 3. 更新 AgentParcelDO 状态为已支付，等待发货
        int updateCount = agentParcelMapper.updateByIdAndStatus(id, parcel.getStatus(),
                new AgentParcelDO().setStatus(AgentParcelStatusEnum.UNDELIVERED.getStatus()).setPayStatus(true)
                        .setPayTime(LocalDateTime.now()).setPayChannelCode(payOrder.getChannelCode()));
        if (updateCount == 0) {
            throw exception(AGENT_PARCEL_UPDATE_PAID_STATUS_NOT_UNPAID);
        }

        // 4. 执行 AgentParcelHandler 的后置处理
        List<AgentParcelItemDO> orderItems = agentParcelItemMapper.selectListByParcelId(id);
        parcelHandlers.forEach(handler -> handler.afterPayOrder(parcel, orderItems));

        // 5. 记录订单日志
        AgentParcelLogUtils.setOrderInfo(id, parcel.getStatus(), AgentParcelStatusEnum.UNDELIVERED.getStatus());
        AgentParcelLogUtils.setUserInfo(parcel.getUserId(), UserTypeEnum.MEMBER.getValue());

        //6.订单支付完成后操作
        // 发送 MQ 消息：用户创建
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {

            @Override
            public void afterCommit() {
                agentParcelProducer.sendParcelSuccessMessage(parcel.getId(),getCurrentLanguage().getLanguage());
            }

        });

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @AgentParcelLog(operateType = AgentParcelOperateTypeEnum.MEMBER_CANCEL)
    public void cancelParcelByMember(Long userId, Long id) {
        // 1.1 校验订单是否存在
        AgentParcelDO parcel = agentParcelMapper.selectParcelByIdAndUserId(id, userId);
        if (parcel == null) {
            throw exception(AGENT_PARCEL_NOT_EXISTS);
        }
        // 1.2 校验状态
        if (ObjectUtil.notEqual(parcel.getStatus(), AgentParcelStatusEnum.UNPAID.getStatus())) {
            throw exception(AGENT_PARCEL_CANCEL_FAIL_STATUS_NOT_UNPAID);
        }

        // 2. 取消订单
        cancelParcel0(parcel, AgentParcelCancelTypeEnum.MEMBER_CANCEL);
    }

    /**
     * 如果金额全部被退款，则取消订单
     * 如果还有未被退款的金额，则无需取消订单
     *
     * @param order       订单
     * @param refundPrice 退款金额
     */
    @AgentParcelLog(operateType = AgentParcelOperateTypeEnum.ADMIN_CANCEL_AFTER_SALE)
    public void cancelOrderByAfterSale(AgentParcelDO order, Integer refundPrice) {
        // 1. 更新订单
        if (refundPrice < order.getPayPrice()) {
            return;
        }
        agentParcelMapper.updateById(new AgentParcelDO().setId(order.getId())
                .setStatus(AgentParcelStatusEnum.CANCELED.getStatus())
                .setCancelType(AgentParcelCancelTypeEnum.AFTER_SALE_CLOSE.getType()).setCancelTime(LocalDateTime.now()));

        // 2. 执行 AgentParcelHandler 的后置处理
        List<AgentParcelItemDO> orderItems = agentParcelItemMapper.selectListByParcelId(order.getId());
        parcelHandlers.forEach(handler -> handler.afterCancelOrder(order, orderItems));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @AgentParcelLog(operateType = AgentParcelOperateTypeEnum.MEMBER_DELETE)
    public void deleteParcel(Long userId, Long id) {
        // 1.1 校验存在
        AgentParcelDO order = agentParcelMapper.selectParcelByIdAndUserId(id, userId);
        if (order == null) {
            throw exception(AGENT_PARCEL_NOT_EXISTS);
        }
        // 1.2 校验状态
        if (ObjectUtil.notEqual(order.getStatus(), AgentParcelStatusEnum.CANCELED.getStatus())) {
            throw exception(AGENT_PARCEL_DELETE_FAIL_STATUS_NOT_CANCEL);
        }
        // 2.1 删除订单
        agentParcelMapper.deleteById(id);
        // 2.2删除子表
        agentParcelItemMapper.deleteByParcelId(id);

        // 3. 记录日志
        AgentParcelLogUtils.setOrderInfo(order.getId(), order.getStatus(), order.getStatus());

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @AgentParcelLog(operateType = AgentParcelOperateTypeEnum.ADMIN_DELIVERY)
    public void deliveryParcel(AgentParcelTransportReqVO deliveryReqVO) {
        // 1.1 校验并获得包裹订单（可发货）
        AgentParcelDO parcel = validateParcelDeliverable(deliveryReqVO.getId());

        // 1.2 校验物流方案和物流公司
        LogisticsProductDO logisticsProductDO = validateLogisticsProduct(deliveryReqVO.getTransportPlanFeeId());

        // 2. 更新包裹已发货
        AgentParcelDO updateOrderObj = new AgentParcelDO();
        updateOrderObj.setStatus(AgentParcelStatusEnum.DELIVERED.getStatus())
                .setDeliveryTime(LocalDateTime.now())
                .setTransportNo(deliveryReqVO.getTransportNo());
        int updateCount = agentParcelMapper.updateByIdAndStatus(parcel.getId(), parcel.getStatus(), updateOrderObj);
        if (updateCount == 0) {
            throw exception(AGENT_PARCEL_DELIVERY_FAIL_STATUS_NOT_UNDELIVERED);
        }
        // 3. 记录订单日志
        AgentParcelLogUtils.setOrderInfo(parcel.getId(), parcel.getStatus(), AgentParcelStatusEnum.DELIVERED.getStatus(),
            MapUtil.<String, Object>builder().put("deliveryName", logisticsProductDO != null ? logisticsProductDO.getCompanyName() : "")
                    .put( "transportPlanName", logisticsProductDO != null ? logisticsProductDO.getNameZh() : "")
                .put("transportNo", logisticsProductDO != null ? deliveryReqVO.getTransportNo() : "").build());
        // 4.1 发送站内信
        agentMessageService.sendMessageWhenDeliveryOrder(new AgentParcelMessageWhenDeliveryOrderReqBO()
                .setParcelId( parcel.getId()).setUserId( parcel.getUserId()).setParcelNo( parcel.getNo()).setDeliveryName( logisticsProductDO != null ? logisticsProductDO.getCompanyName() : "").setTransportNo( deliveryReqVO.getTransportNo()).setMessage(null));
        // 4.2 发送订阅消息


    }

    @Override
    public void updateParcelRemark(AgentParcelRemarkReqVO reqVO) {
        //校验订单
        validateOrderExists(reqVO.getId());

        // 更新
        AgentParcelDO order = AgentParcelConvert.INSTANCE.convert(reqVO);
        agentParcelMapper.updateById(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @AgentParcelLog(operateType = AgentParcelOperateTypeEnum.ADMIN_UPDATE_PRICE)
    public void updateParcelPrice(AgentParcelUpdatePriceReqVO reqVO) {
        // 1.1 校验订单
        AgentParcelDO parcel = validateOrderExists(reqVO.getId());
        if (parcel.getPayStatus()) {
            throw exception(AGENT_PARCEL_UPDATE_PRICE_FAIL_PAID);
        }
        // 1.2 校验调价金额是否变化
         if (parcel.getAdjustPrice() > 0) {
             throw exception(AGENT_PARCEL_UPDATE_PRICE_FAIL_ALREADY);
         }
        // 1.3 支付价格不能为 0
        int newPayPrice = parcel.getPayPrice() + reqVO.getAdjustPrice();
        if (newPayPrice <= 0) {
            throw exception(AGENT_PARCEL_UPDATE_PRICE_FAIL_PRICE_ERROR);
        }
        // 2. 更新订单
        agentParcelMapper.updateById( new AgentParcelDO().setId(parcel.getId())
                .setAdjustPrice( reqVO.getAdjustPrice() + parcel.getAdjustPrice())
                .setPayPrice( newPayPrice));
        // 3. 更新 AgentParcelItem，需要做 adjustPrice 的分摊
        List<AgentParcelItemDO> orderItems = agentParcelItemMapper.selectListByParcelId(parcel.getId());
        List<Integer> dividePrices = AgentPriceCalculatorHelper.divideWeightPrice1(orderItems, reqVO.getAdjustPrice());
        List<AgentParcelItemDO>  updateItems = new ArrayList<>();
         for (int i = 0; i < orderItems.size(); i++) {
            AgentParcelItemDO item = orderItems.get(i);
            updateItems.add(new AgentParcelItemDO().setId(item.getId()).setAdjustPrice(item.getAdjustPrice() + dividePrices.get(i))
                    .setPayPrice((item.getPayPrice() - item.getAdjustPrice()) + dividePrices.get(i)));
        }
         agentParcelItemMapper.updateBatch(updateItems);
        // 4. 更新支付订单
         payOrderApi.updatePayOrderPrice(parcel.getPayOrderId(), newPayPrice);
        // 5. 记录订单日志
         AgentParcelLogUtils.setOrderInfo(parcel.getId(), parcel.getStatus(), parcel.getStatus(),
                 MapUtil.<String, Object>builder().put("oldPayPrice", MoneyUtils.fenToYuanStr(parcel.getPayPrice()))
                         .put("adjustPrice", MoneyUtils.fenToYuanStr(reqVO.getAdjustPrice()))
                         .put("newPayPrice", MoneyUtils.fenToYuanStr(newPayPrice)).build());
    }

    @Override
    @AgentParcelLog(operateType = AgentParcelOperateTypeEnum.ADMIN_UPDATE_ADDRESS)
    public void updateOrderAddress(AgentParcelUpdateAddressReqVO reqVO) {
        // 校验交易订单
         AgentParcelDO order = validateOrderExists(reqVO.getId());
        // 只有待发货状态，才可以修改订单收货地址；
         if (!AgentParcelStatusEnum.isUndelivered(order.getStatus())) {
              throw exception(AGENT_PARCEL_UPDATE_ADDRESS_FAIL_STATUS_NOT_DELIVERED);
         }
        // 更新
        agentParcelMapper.updateById(AgentParcelConvert.INSTANCE.convert(reqVO));

        // 记录订单日志
         AgentParcelLogUtils.setOrderInfo(order.getId(), order.getStatus(), order.getStatus());
    }


    @Override
    public void updateParcelItemWhenAfterSaleCreate(Long id, Long afterSaleId) {
        // 更新订单项
        updateParcelItemAfterSaleStatus(id, AgentParcelItemAfterSaleStatusEnum.NONE.getStatus(),
                AgentParcelItemAfterSaleStatusEnum.APPLY.getStatus(), afterSaleId);
    }

    @Override
    public void updateParcelItemWhenAfterSaleCancel(Long id) {
        // 更新订单项
        updateParcelItemAfterSaleStatus(id, AgentParcelItemAfterSaleStatusEnum.APPLY.getStatus(),
                AgentParcelItemAfterSaleStatusEnum.NONE.getStatus(), null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateParcelItemWhenAfterSaleSuccess(Long id, Integer refundPrice) {
        // 1.1 更新订单项
        updateParcelItemAfterSaleStatus(id, AgentParcelItemAfterSaleStatusEnum.APPLY.getStatus(),
                AgentParcelItemAfterSaleStatusEnum.SUCCESS.getStatus(), null);
        // 1.2 执行 AgentParcelHandler 的后置处理
        AgentParcelItemDO orderItem = agentParcelItemMapper.selectById(id);
        AgentParcelDO order = agentParcelMapper.selectById(orderItem.getParcelId());
        parcelHandlers.forEach(handler -> handler.afterCancelOrderItem(order, orderItem));

        // 2.1 更新订单的退款金额、积分
        Integer orderRefundPrice = order.getRefundPrice() + refundPrice;
        Integer orderRefundPoint = order.getRefundPoint() + orderItem.getUsePoint();
        Integer refundStatus = isAllOrderItemAfterSaleSuccess(order.getId()) ?
                AgentParcelRefundStatusEnum.ALL.getStatus() // 如果都售后成功，则需要取消订单
                : AgentParcelRefundStatusEnum.PART.getStatus();
        agentParcelMapper.updateById(new AgentParcelDO().setId(order.getId())
                .setRefundStatus(refundStatus)
                .setRefundPrice(orderRefundPrice).setRefundPoint(orderRefundPoint));
        // 2.2 如果全部退款，则进行取消订单
        getSelf().cancelOrderByAfterSale(order, orderRefundPrice);
    }

    private void updateParcelItemAfterSaleStatus(Long id, Integer oldAfterSaleStatus, Integer newAfterSaleStatus,
                                                Long afterSaleId) {
        // 更新订单项
        int updateCount = agentParcelItemMapper.updateAfterSaleStatus(id, oldAfterSaleStatus, newAfterSaleStatus, afterSaleId);
        if (updateCount <= 0) {
            throw exception(AGENT_PARCEL_ITEM_UPDATE_AFTER_SALE_STATUS_FAIL);
        }

    }

    /**
     * 判断指定订单的所有订单项，是不是都售后成功
     *
     * @param id 订单编号
     * @return 是否都售后成功
     */
    private boolean isAllOrderItemAfterSaleSuccess(Long id) {
        List<AgentParcelItemDO> orderItems = agentParcelItemMapper.selectListByParcelId(id);
        return orderItems.stream().allMatch(orderItem -> Objects.equals(orderItem.getAfterSaleStatus(),
                AgentParcelItemAfterSaleStatusEnum.SUCCESS.getStatus()));
    }

    private LogisticsProductDO validateLogisticsProduct(Long productPriceId) {
        LogisticsProductPriceDO productPrice = logisticsProductService.getLogisticsProductPrice(productPriceId);
        if (productPrice == null) {
            throw exception(TRANSPORT_PLAN_FEE_NOT_EXISTS);
        }
        LogisticsProductDO product = logisticsProductService.getLogisticsProduct(productPrice.getProductId());
         if (product == null) {
            throw exception(TRANSPORT_PLAN_NOT_EXISTS);
        }
         return product;

    }

    private AgentParcelDO validateParcelDeliverable(Long id) {
         AgentParcelDO parcel = validateOrderExists(id);
         // 1. 校验包裹是否未发货
        if(ObjectUtil.notEqual(AgentParcelRefundStatusEnum.NONE.getStatus(),parcel.getRefundStatus())){
            throw exception(AGENT_PARCEL_DELIVERY_FAIL_REFUND_STATUS_NOT_NONE);
        }
        // 2. 执行AgentParcelHandler 的前置处理
         parcelHandlers.forEach(handler -> handler.beforeDeliveryOrder(parcel));
        return parcel;
    }

    /**
     * 取消包裹订单核心实现
     *
     * @param parcel 订单
     * @param agentParcelCancelTypeEnum 取消类型
     */
    private void cancelParcel0(AgentParcelDO parcel, AgentParcelCancelTypeEnum cancelType) {
        // 1. 更新 AgentParcelDO 状态为已取消
        int updateCount = agentParcelMapper.updateByIdAndStatus(parcel.getId(), parcel.getStatus(),
                new AgentParcelDO().setStatus(AgentParcelStatusEnum.CANCELED.getStatus())
                        .setCancelType(cancelType.getType()).setCancelTime(LocalDateTime.now()));
        if (updateCount == 0) {
            throw exception(AGENT_PARCEL_CANCEL_FAIL_STATUS_NOT_UNPAID);
        }
        // 2. 执行 AgentParcelHandler 的后置处理
        List<AgentParcelItemDO> orderItems = agentParcelItemMapper.selectListByParcelId(parcel.getId());
        parcelHandlers.forEach(handler -> handler.afterCancelOrder(parcel, orderItems));

        // 3. 记录订单日志
        AgentParcelLogUtils.setOrderInfo(parcel.getId(), parcel.getStatus(), AgentParcelStatusEnum.CANCELED.getStatus());
    }

    @NotNull
    private AgentParcelDO validateOrderExists(Long id) {
        // 校验订单是否存在
        AgentParcelDO parcelDO = agentParcelMapper.selectById(id);
        if (parcelDO == null) {
            throw exception(AGENT_PARCEL_NOT_EXISTS);
        }
        return parcelDO;
    }

    /**
     * 校验支付订单的合法性
     *
     * @param order 交易订单
     * @param payOrderId 支付订单编号
     * @return 支付订单
     */
    private PayOrderRespDTO validatePayOrderPaid(AgentParcelDO parcel, Long payOrderId) {
        // 1. 校验支付单是否存在
        PayOrderRespDTO payOrder = payOrderApi.getOrder(payOrderId);
        if (payOrder == null) {
            log.error("[validatePayOrderPaid][order({}) payOrder({}) 不存在，请进行处理！]", parcel.getId(), payOrderId);
            throw exception(AGENT_PARCEL_NOT_EXISTS);
        }

        // 2.1 校验支付单已支付
        if (!PayOrderStatusEnum.isSuccess(payOrder.getStatus())) {
            log.error("[validatePayOrderPaid][order({}) payOrder({}) 未支付，请进行处理！payOrder 数据是：{}]",
                    parcel.getId(), payOrderId, JsonUtils.toJsonString(payOrder));
            throw exception(AGENT_PARCEL_UPDATE_PAID_FAIL_PAY_ORDER_STATUS_NOT_SUCCESS);
        }
        // 2.2 校验支付金额一致
        //if (ObjectUtil.notEqual(payOrder.getPrice(), order.getPayPrice())) { ding 修改为对比orderPrice
        if (ObjectUtil.notEqual(payOrder.getOrderPrice(), parcel.getPayPrice())) {
            log.error("[validatePayOrderPaid][order({}) payOrder({}) 支付金额不匹配，请进行处理！order 数据是：{}，payOrder 数据是：{}]",
                    parcel.getId(), payOrderId, JsonUtils.toJsonString(parcel), JsonUtils.toJsonString(payOrder));
            throw exception(AGENT_PARCEL_UPDATE_PAID_FAIL_PAY_PRICE_NOT_MATCH);
        }
        // 2.2 校验支付订单匹配（二次）
        if (ObjectUtil.notEqual(payOrder.getMerchantOrderId(), parcel.getId().toString())) {
            log.error("[validatePayOrderPaid][order({}) 支付单不匹配({})，请进行处理！payOrder 数据是：{}]",
                    parcel.getId(), payOrderId, JsonUtils.toJsonString(payOrder));
            throw exception(AGENT_PARCEL_UPDATE_PAID_FAIL_PAY_ORDER_ID_ERROR);
        }
        return payOrder;
    }

    private AgentParcelDO buildAgentParcel(Long userId, AppAgentParcelCreateReqVO createReqVO, AgentPriceCalculateRespBO calculateRespBO) {

        AgentParcelDO parcel = AgentParcelConvert.INSTANCE.convert(userId, createReqVO, calculateRespBO);
        parcel.setNo(agentNoRedisDAO.generate(AgentNoRedisDAO.AGENT_PARCEL_NO_PREFIX));
        parcel.setStatus(AgentParcelStatusEnum.UNPAID.getStatus());
        parcel.setProductCount(getSumValue(calculateRespBO.getItems(), AgentPriceCalculateRespBO.Item::getCount, Integer::sum));
        parcel.setUserIp(getClientIP()).setTerminal(getTerminal());
        // 使用 + 赠送优惠券
        parcel.setGiveCouponTemplateCounts(calculateRespBO.getGiveCouponTemplateCounts());
        // 支付 + 退款信息
        parcel.setAdjustPrice(0).setPayStatus(false);
        parcel.setRefundStatus(AgentParcelRefundStatusEnum.NONE.getStatus()).setRefundPrice(0);
        //申报信息
        parcel.setDeclareValue(MoneyUtils.yuanToFen(createReqVO.getDeclareValue()));
        // 物流信息 默认快递
        parcel.setDeliveryType(1);
        //根据物流方案获取物流公司
//        AgentTransportPlanDO transportPlan = transportPlanService.getTransportPlan(createReqVO.getTransportPlanId());
        LogisticsProductDO logisticsProduct = logisticsProductService.getLogisticsProduct(createReqVO.getTransportPlanId());
        if(logisticsProduct == null){
            throw exception(TRANSPORT_PLAN_NOT_EXISTS);
        }
        parcel.setTransportCompanyId(logisticsProduct.getCompanyId());

        MemberAddressRespDTO address = addressApi.getAddress(createReqVO.getAddressId(), userId);
        Assert.notNull(address, "地址({}) 不能为空", createReqVO.getAddressId()); // 价格计算时，已经计算
        parcel.setReceiverName(address.getName()).setReceiverMobile(address.getMobile())
                .setReceiverAreaId(address.getAreaId()).setReceiverDetailAddress(address.getDetailAddress())
                .setReceiverPhoneCode(address.getPhoneCode()).setReceiverCountryCode(address.getCountryCode())
                .setReceiverPostCode(address.getPostCode());

        // 重量体积
        parcel.setWeight(calculateRespBO.getInfo().getWeight());
        parcel.setVolume(calculateRespBO.getInfo().getVolume());
        parcel.setPackingWeight(calculateRespBO.getInfo().getPackageWeight());
        parcel.setLength(calculateRespBO.getInfo().getLength());
        parcel.setWidth(calculateRespBO.getInfo().getWidth());
        parcel.setHeight(calculateRespBO.getInfo().getHeight());

        //


        return parcel;

    }


    private List<AgentParcelItemDO> buildAgentParcelItems(AgentParcelDO parcelDO, AgentPriceCalculateRespBO calculateRespBO) {

        return AgentParcelConvert.INSTANCE.convertList(parcelDO, calculateRespBO);
    }

    private void afterCreateAgentParcel(AgentParcelDO order, List<AgentParcelItemDO> orderItems, AppAgentParcelCreateReqVO createReqVO) {

        // 1. 执行订单创建后置处理器
        parcelHandlers.forEach(handler -> handler.afterOrderCreate(order, orderItems));

        // 3. 生成预支付 payorder
        if (order.getPayPrice() > 0) {
            createPayOrder(order, orderItems, createReqVO);
        }

        // 4. 插入订单日志
        AgentParcelLogUtils.setOrderInfo(order.getId(), null, order.getStatus());

    }

    private void createPayOrder(AgentParcelDO order, List<AgentParcelItemDO> orderItems, AppAgentParcelCreateReqVO createReqVO) {
        // 创建支付单，用于后续的支付
        PayOrderCreateReqDTO payOrderCreateReqDTO = AgentParcelConvert.INSTANCE.convert(
                order, orderItems, agentParcelProperties,createReqVO.getCurrency());
        Long payOrderId = payOrderApi.createOrder(payOrderCreateReqDTO);

        // 更新到交易单上
        agentParcelMapper.updateById(new AgentParcelDO().setId(order.getId()).setPayOrderId(payOrderId));
        order.setPayOrderId(payOrderId);
    }

    /**
     * 获得用户地址
     *
     * @param userId    用户编号
     * @param addressId 地址编号
     * @return 地址
     */
    private MemberAddressRespDTO getAddress(Long userId, Long addressId) {
        if (addressId != null) {
            return addressApi.getAddress(addressId, userId);
        }
        return addressApi.getDefaultAddress(userId);
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private AgentParcelUpdateServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}
