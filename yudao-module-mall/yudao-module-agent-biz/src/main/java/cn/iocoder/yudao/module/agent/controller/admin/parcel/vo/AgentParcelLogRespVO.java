package cn.iocoder.yudao.module.agent.controller.admin.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 代购包裹订单日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AgentParcelLogRespVO {

    @Schema(description = "日志主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19988")
    @ExcelProperty("日志主键")
    private Long id;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "8317")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("用户类型")
    private Integer userType;

    @Schema(description = "包裹单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "8951")
    @ExcelProperty("包裹单号")
    private Long parcelId;

    @Schema(description = "操作前状态", example = "2")
    @ExcelProperty("操作前状态")
    private Integer beforeStatus;

    @Schema(description = "操作后状态", example = "2")
    @ExcelProperty("操作后状态")
    private Integer afterStatus;

    @Schema(description = "操作类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("操作类型")
    private Integer operateType;

    @Schema(description = "操作内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("操作内容")
    private String content;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}