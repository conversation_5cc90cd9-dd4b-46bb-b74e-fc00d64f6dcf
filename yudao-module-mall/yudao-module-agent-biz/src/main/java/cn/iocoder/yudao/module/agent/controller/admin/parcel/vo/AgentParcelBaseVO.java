package cn.iocoder.yudao.module.agent.controller.admin.parcel.vo;

import cn.iocoder.yudao.framework.mybatis.core.type.LongListTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 包裹订单 Base VO
 * @author: DingXiao
 * @create: 2025-05-30 09:58
 **/
@Data
public class AgentParcelBaseVO {

    // ========== 包裹订单基本信息 ==========

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "订单流水号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1146347329394184195")
    private String no;

    @Schema(description = "下单时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "订单类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "订单来源", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer terminal;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    private Long userId;

    @Schema(description = "用户 IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "127.0.0.1")
    private String userIp;

    @Schema(description = "用户备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String userRemark;

    @Schema(description = "包裹订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "购买的商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer productCount;

    @Schema(description = "订单完成时间")
    private LocalDateTime finishTime;

    @Schema(description = "订单取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "取消类型", example = "10")
    private Integer cancelType;

    @Schema(description = "商家备注", example = "你猜一下")
    private String remark;

    // ========== 价格 + 支付基本信息 ==========

    @Schema(description = "支付订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long payOrderId;

    @Schema(description = "是否已支付", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean payStatus;

    @Schema(description = "付款时间")
    private LocalDateTime payTime;

    @Schema(description = "支付渠道", requiredMode = Schema.RequiredMode.REQUIRED, example = "wx_lite")
    private String payChannelCode;

    @Schema(description = "商品原价（总）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private Integer totalPrice;

    @Schema(description = "订单优惠（总）", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer discountPrice;

    @Schema(description = "运费金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer deliveryPrice;

    @Schema(description = "订单调价（总）", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer adjustPrice;

    @Schema(description = "应付金额（总）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private Integer payPrice;


    /**
     * 保险金额
     */
    private Integer insurancePrice;
    /**
     * 服务金额
     */
    private Integer servicePrice;

    /**
     * 平台费，单位：分
     */
    private Integer platformPrice;

    // ========== 收件 + 物流基本信息 ==========

    @Schema(description = "配送方式", example = "10")
    private Integer deliveryType;



    @Schema(description = "物流方案编号", example = "1024")
    private Long transportPlanId;
    @Schema(description = "物流方案价格编号", example = "1024")
    private Long transportPlanFeeId;
    @Schema(description = "物流方案名称", example = "顺丰极速")
    private String transportPlanName;

    @Schema(description = "物流公司编号", example = "1024")
    private Long transportCompanyId;
    @Schema(description = "物流公司编号", example = "顺丰")
    private String transportCompanyName;

    @Schema(description = "发货物流单号", example = "1024")
    private String transportNo;

    @Schema(description = "物流跟踪", example = "1024")
    private String transportTrack;

    /**
     * 海关申报内容
     */
    private String declareContent;
    /**
     * 海关申报价值
     */
    private Integer declareValue;
    /**
     * 清关代码
     */
    private String clearanceCode;
    /**
     * 保险服务数组
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> insuranceServices;
    /**
     * 免费服务数组
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> freeServices;
    /**
     * 收费服务数组
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> chargeServices;

    @Schema(description = "发货时间")
    private LocalDateTime deliveryTime;

    @Schema(description = "收货时间")
    private LocalDateTime receiveTime;

    @Schema(description = "收件人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String receiverName;

    @Schema(description = "收件人手机区号", requiredMode = Schema.RequiredMode.REQUIRED, example = "86")
    private String receiverPhoneCode;

    @Schema(description = "收件人手机", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    private String receiverMobile;

    @Schema(description = "收件人地区编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "110000")
    private Integer receiverAreaId;

    @Schema(description = "收件人详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "中关村大街 1 号")
    private String receiverDetailAddress;

    @Schema(description = "收件人国家编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "US")
    private String receiverCountryCode;

    // ========== 售后基本信息 ==========

    @Schema(description = "售后状态", example = "1")
    private Integer afterSaleStatus;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer refundPrice;

    // ========== 营销基本信息 ==========

    @Schema(description = "优惠劵编号", example = "1024")
    private Long couponId;

    @Schema(description = "优惠劵减免金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer couponPrice;

    @Schema(description = "积分抵扣的金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer pointPrice;

    @Schema(description = "VIP 减免金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "888")
    private Integer vipPrice;

    @Schema(description = "推广人编号", example = "1")
    private Long brokerageUserId;
}
