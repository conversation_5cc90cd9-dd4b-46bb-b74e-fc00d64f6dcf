package cn.iocoder.yudao.module.agent.controller.admin.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @program: ruoyi-vue-pro
 * @description: 管理后台 - 包裹发货 Request VO
 * @author: DingXiao
 * @create: 2025-05-31 11:05
 **/
@Data
public class AgentParcelTransportReqVO {

    @Schema(description = "包裹订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单编号不能为空")
    private Long id;

    @Schema(description = "物流公司编号", example = "1")
    private Long transportCompanyId;

    @Schema(description = "物流公司名称", example = "1")
    private String transportCompanyName;


    @Schema(description = "物流方案编号", example = "1")
    private Long transportPlanId;

    @Schema(description = "物流方案价格编号", example = "1")
    private Long transportPlanFeeId;

    @Schema(description = "物流方案名称", example = "1")
    private String transportPlanName;

    @Schema(description = "发货物流单号", example = "SF123456789")
    private String transportNo;
}
