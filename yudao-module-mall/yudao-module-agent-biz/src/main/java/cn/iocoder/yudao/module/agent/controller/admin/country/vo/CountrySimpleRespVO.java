package cn.iocoder.yudao.module.agent.controller.admin.country.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 代购国家精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CountrySimpleRespVO {

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "CN")
    private String code;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "中国")
    private String name;
}
