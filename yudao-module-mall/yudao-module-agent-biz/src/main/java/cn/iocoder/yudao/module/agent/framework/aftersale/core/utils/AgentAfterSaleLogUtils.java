package cn.iocoder.yudao.module.agent.framework.aftersale.core.utils;


import cn.iocoder.yudao.module.agent.framework.aftersale.core.aop.AgentAfterSaleLogAspect;

import java.util.Map;

/**
 * 操作日志工具类
 * 目前主要的作用，是提供给业务代码，记录操作明细和拓展字段
 *
 * <AUTHOR>
 */
public class AgentAfterSaleLogUtils {

    public static void setAfterSaleInfo(Long id, Integer beforeStatus, Integer afterStatus) {
        setAfterSaleInfo(id, beforeStatus, afterStatus, null);
    }

    public static void setAfterSaleInfo(Long id, Integer beforeStatus, Integer afterStatus,
                                        Map<String, Object> exts) {
        AgentAfterSaleLogAspect.setAfterSale(id, beforeStatus, afterStatus, exts);
    }

}
