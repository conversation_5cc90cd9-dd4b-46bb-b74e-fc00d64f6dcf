package cn.iocoder.yudao.module.agent.controller.admin.config;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.agent.controller.admin.config.vo.AgentConfigRespVO;
import cn.iocoder.yudao.module.agent.controller.admin.config.vo.AgentConfigSaveReqVO;
import cn.iocoder.yudao.module.agent.convert.config.AgentConfigConvert;
import cn.iocoder.yudao.module.agent.dal.dataobject.config.AgentConfigDO;
import cn.iocoder.yudao.module.agent.service.config.AgentConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 代购中心配置")
@RestController
@RequestMapping("/agent/config")
@Validated
public class AgentConfigController {

    @Resource
    private AgentConfigService agentConfigService;


    @PutMapping("/save")
    @Operation(summary = "更新代购中心配置")
    @PreAuthorize("@ss.hasPermission('agent:config:save')")
    public CommonResult<Boolean> updateConfig(@Valid @RequestBody AgentConfigSaveReqVO updateReqVO) {
        agentConfigService.saveAgentConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购中心配置")
    @PreAuthorize("@ss.hasPermission('agent:config:query')")
    public CommonResult<AgentConfigRespVO> getConfig() {
        AgentConfigDO config = agentConfigService.getAgentConfig();
        AgentConfigRespVO configVO = AgentConfigConvert.INSTANCE.convert(config);
        return success(configVO);
    }


}