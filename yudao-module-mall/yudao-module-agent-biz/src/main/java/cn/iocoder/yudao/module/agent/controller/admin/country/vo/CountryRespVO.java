package cn.iocoder.yudao.module.agent.controller.admin.country.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 代购服务国家 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CountryRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16640")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "国家编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("国家编码")
    private String code;

    @Schema(description = "图标地址", example = "https://www.iocoder.cn")
    @ExcelProperty("图标地址")
    private String flagUrl;

    @Schema(description = "中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("中文名称")
    private String nameZh;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("英文名称")
    private String nameEn;

    @Schema(description = "法语名称")
    @ExcelProperty("法语名称")
    private String nameFr;

    @Schema(description = "德语名称")
    @ExcelProperty("德语名称")
    private String nameDe;

    @Schema(description = "西班牙语名称")
    @ExcelProperty("西班牙语名称")
    private String nameEs;

    @Schema(description = "阿拉伯语名称")
    @ExcelProperty("阿拉伯语名称")
    private String nameAr;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "热门", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("热门")
    private Boolean hot;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "开启状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}