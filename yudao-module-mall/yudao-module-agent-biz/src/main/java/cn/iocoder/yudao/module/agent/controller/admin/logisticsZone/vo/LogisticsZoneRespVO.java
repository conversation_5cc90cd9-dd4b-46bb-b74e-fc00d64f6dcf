package cn.iocoder.yudao.module.agent.controller.admin.logisticsZone.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 代购物流国家分区 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LogisticsZoneRespVO {

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30055")
    @ExcelProperty("分类编号")
    private Long id;

    @Schema(description = "国家编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("国家编码")
    private String countryCode;

    @Schema(description = "产品编号", example = "1601")
    @ExcelProperty("产品编号")
    private Long productId;

    @Schema(description = "分区编码")
    @ExcelProperty("分区编码")
    private String zoneCode;

    @Schema(description = "分区名称", example = "禁止配送区域")
    @ExcelProperty("分区名称")
    private String zoneName;

    @Schema(description = "一级行政区划", example = "California")
    @ExcelProperty("一级行政区划")
    private String stateProvince;

    @Schema(description = "二级行政区划", example = "Los Angeles")
    @ExcelProperty("二级行政区划")
    private String city;

    @Schema(description = "三级行政区划", example = "Beverly Hills")
    @ExcelProperty("三级行政区划")
    private String district;

    @Schema(description = "特殊区域类型", example = "ISLAND")
    @ExcelProperty("特殊区域类型")
    private String specialAreaType;

    @Schema(description = "完整区域描述", example = "California Los Angeles Beverly Hills")
    @ExcelProperty("完整区域描述")
    private String fullAreaName;

    @Schema(description = "邮编配置", example = "[\"99500-99999\"]")
    @ExcelProperty("邮编配置")
    private String postalCodes;

    @Schema(description = "限制类型", example = "FORBIDDEN")
    @ExcelProperty("限制类型")
    private String restrictionType;

    @Schema(description = "附加费公式", example = "3*weight_kg,min:48")
    @ExcelProperty("附加费公式")
    private String feeFormula;

    @Schema(description = "备注说明", example = "阿拉斯加州禁止配送")
    @ExcelProperty("备注说明")
    private String remark;

    @Schema(description = "排序", example = "1")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("开启状态")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}