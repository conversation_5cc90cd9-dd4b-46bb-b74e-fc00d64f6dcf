package cn.iocoder.yudao.module.agent.controller.admin.shipping.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运费查询 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 运费查询 Response VO")
@Data
public class ShippingQuoteRespVO {

    @Schema(description = "物流产品ID", example = "1920529007397355522")
    private String id;

    @Schema(description = "物流产品名称", example = "美国专线小包p")
    private String name;

    @Schema(description = "图标URL", example = "https://img1.cnfans.com/xxx.png")
    private String iconUrl;

    @Schema(description = "产品特色描述", example = "1、该线路为三角运输...")
    private String features;

    @Schema(description = "运输时效", example = "12-20")
    private String transitTime;

    @Schema(description = "是否包税", example = "true")
    private Boolean taxInclude;

    @Schema(description = "线路提示信息")
    private List<String> lineTips;

    @Schema(description = "费用详情")
    private FeeDetail feeDetail;

    @Schema(description = "限制信息")
    private Restrictions restrictions;

    @Schema(description = "是否可用", example = "true")
    private Boolean available;

    @Schema(description = "不可用原因", example = "超重")
    private String unavailableReason;

    @Schema(description = "排序", example = "15")
    private Integer sort;

    @Schema(description = "最小申报价值(分)", example = "500")
    private Integer minDeclareValue;

    @Schema(description = "最大申报价值(分)", example = "12000")
    private Integer maxDeclareValue;

    @Schema(description = "默认申报类型", example = "Weight")
    private String defaultDeclareType;

    @Schema(description = "每公斤申报价值(分)", example = "1200")
    private Integer declarePerKg;

    @Schema(description = "申报比例", example = "0.2")
    private String declareRatio;

    @Schema(description = "是否启用IOSS", example = "false")
    private Boolean iossEnabled;

    @Schema(description = "是否免费保险", example = "false")
    private Boolean freeInsure;

    @Schema(description = "地址最大长度", example = "100")
    private Integer addressMaxLength;

    @Schema(description = "关税税率", example = "0")
    private BigDecimal tariffRate;

    @Schema(description = "是否预收关税", example = "false")
    private Boolean prepayTariff;

    @Schema(description = "物流时效信息")
    private LogisticsTimeliness logisticsTimeliness;

    /**
     * 费用详情
     */
    @Schema(description = "费用详情")
    @Data
    public static class FeeDetail {
        @Schema(description = "重量(g)", example = "600")
        private Integer weight;

        @Schema(description = "长度(cm)", example = "30")
        private BigDecimal length;

        @Schema(description = "宽度(cm)", example = "30")
        private BigDecimal width;

        @Schema(description = "高度(cm)", example = "3")
        private BigDecimal height;

        @Schema(description = "体积重(g)", example = "338")
        private Integer volumeWeight;

        @Schema(description = "计费重量(g)", example = "700")
        private Integer chargeableWeight;

        @Schema(description = "总费用", example = "25.23")
        private String total;

        @Schema(description = "运费", example = "21.27")
        private String freight;

        @Schema(description = "清关费", example = "0.00")
        private String customsFee;

        @Schema(description = "燃油费", example = "0.00")
        private String fuelFee;

        @Schema(description = "空运附加费", example = "0.00")
        private String airSurcharge;

        @Schema(description = "操作费", example = "2.37")
        private String operationFee;

        @Schema(description = "服务费", example = "1.58")
        private String serviceFee;

        @Schema(description = "首重费用", example = "16.62")
        private String feeFirst;

        @Schema(description = "续重费用", example = "4.65")
        private String feeContinue;

        @Schema(description = "附加费", example = "null")
        private String additionalFee;

        @Schema(description = "首重重量(g)", example = "500")
        private Integer weightFirst;

        @Schema(description = "续重重量(g)", example = "200")
        private Integer weightContinue;

        @Schema(description = "是否需要体积计算", example = "true")
        private Boolean needVolumeCal;

        @Schema(description = "体积重基数", example = "8000")
        private Integer volumeBase;

        @Schema(description = "折扣", example = "null")
        private String discount;

        @Schema(description = "原始总费用", example = "null")
        private String originalTotal;

        @Schema(description = "当前总费用", example = "22.71")
        private String currentTotal;
    }

    /**
     * 限制信息
     */
    @Schema(description = "限制信息")
    @Data
    public static class Restrictions {
        @Schema(description = "最小重量(g)", example = "0")
        private Integer minWeight;

        @Schema(description = "最大重量(g)", example = "20000")
        private Integer maxWeight;

        @Schema(description = "尺寸限制描述", example = "长<=100cm，宽<=65cm，长+2*(宽+高)<=280cm")
        private String dimensionRestriction;

        @Schema(description = "体积重规则描述", example = "包裹将会被计算体积重...")
        private String volumeWeightRule;

        @Schema(description = "分类限制配置(JSON格式)", example = "[{\"id\":25,\"allowList\":[],\"blockList\":[198,197,196]}]")
        private String categoryRestrictions;
    }

    /**
     * 分类限制
     */
    @Schema(description = "分类限制")
    @Data
    public static class CategoryRestriction {
        @Schema(description = "父分类ID", example = "1")
        private Long id;

        @Schema(description = "允许列表", example = "[101, 102]")
        private List<Long> allowList;

        @Schema(description = "禁止列表", example = "[101, 102]")
        private List<Long> blockList;
    }

    /**
     * 物流时效信息
     */
    @Schema(description = "物流时效信息")
    @Data
    public static class LogisticsTimeliness {
        @Schema(description = "妥投率", example = "98.1")
        private BigDecimal deliveryRate;

        @Schema(description = "时效信息列表")
        private List<TimelinessInfo> timelinessInfos;
    }

    /**
     * 时效信息
     */
    @Schema(description = "时效信息")
    @Data
    public static class TimelinessInfo {
        @Schema(description = "时间区间", example = "8-12")
        private String timeInterval;

        @Schema(description = "比例", example = "2.2")
        private String rate;
    }

}
