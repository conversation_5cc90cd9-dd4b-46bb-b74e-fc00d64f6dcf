package cn.iocoder.yudao.module.agent.controller.admin.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @program: ruoyi-vue-pro
 * @description: 管理后台- 修改订单地址 Request VO
 * @author: DingXiao
 * @create: 2025-05-31 16:15
 **/
@Schema(description = "管理后台 - 修改订单地址 Request VO")
@Data
public class AgentParcelUpdateAddressReqVO {

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单编号不能为空")
    private Long id;

    @Schema(description = "收件人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "z张三")
    @NotEmpty(message = "收件人名称不能为空")
    private String receiverName;

    @Schema(description = "收件人手机区号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19988188888")
    @NotEmpty(message = "收件人手机区号不能为空")
    private String receiverPhoneCode;

    @Schema(description = "收件人手机", requiredMode = Schema.RequiredMode.REQUIRED, example = "19988188888")
    @NotEmpty(message = "收件人手机不能为空")
    private String receiverMobile;

    @Schema(description = "收件人地区编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7310")
    @NotNull(message = "收件人地区编号不能为空")
    private Integer receiverAreaId;

    @Schema(description = "收件人详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "昆明市五华区xxx小区xxx")
    @NotEmpty(message = "收件人详细地址不能为空")
    private String receiverDetailAddress;

    @Schema(description = "收件人邮编", requiredMode = Schema.RequiredMode.REQUIRED, example = "12345")
    @NotEmpty(message = "收件人邮编不能为空")
    private String receiverPostCode;
}
