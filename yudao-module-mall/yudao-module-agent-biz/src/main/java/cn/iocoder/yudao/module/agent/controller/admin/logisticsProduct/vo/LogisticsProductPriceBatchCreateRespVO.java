package cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 代购物流产品价格规则批量创建 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 代购物流产品价格规则批量创建 Response VO")
@Data
@Builder
public class LogisticsProductPriceBatchCreateRespVO {

    @Schema(description = "创建成功的数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer successCount;

    @Schema(description = "创建失败的数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer failureCount;

    @Schema(description = "成功创建的记录ID列表")
    private List<Long> successIds;

    @Schema(description = "失败的国家编码和原因")
    private List<Map<String, Object>> failureData;

}
