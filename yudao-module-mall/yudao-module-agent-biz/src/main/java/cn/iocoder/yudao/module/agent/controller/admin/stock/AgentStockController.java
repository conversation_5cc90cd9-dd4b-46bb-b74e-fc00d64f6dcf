package cn.iocoder.yudao.module.agent.controller.admin.stock;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

import cn.iocoder.yudao.module.agent.controller.admin.stock.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.stock.AgentStockDO;
import cn.iocoder.yudao.module.agent.service.stock.AgentStockService;

@Tag(name = "管理后台 - 代购仓库")
@RestController
@RequestMapping("/agent/stock")
@Validated
public class AgentStockController {

    @Resource
    private AgentStockService stockService;

    @PostMapping("/create")
    @Operation(summary = "创建代购仓库")
    @PreAuthorize("@ss.hasPermission('agent:stock:create')")
    public CommonResult<Long> createStock(@Valid @RequestBody AgentStockSaveReqVO createReqVO) {
        return success(stockService.createStock(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购仓库")
    @PreAuthorize("@ss.hasPermission('agent:stock:update')")
    public CommonResult<Boolean> updateStock(@Valid @RequestBody AgentStockSaveReqVO updateReqVO) {
        stockService.updateStock(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购仓库")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:stock:delete')")
    public CommonResult<Boolean> deleteStock(@RequestParam("id") Long id) {
        stockService.deleteStock(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购仓库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:stock:query')")
    public CommonResult<AgentStockRespVO> getStock(@RequestParam("id") Long id) {
        AgentStockDO stock = stockService.getStock(id);
        return success(BeanUtils.toBean(stock, AgentStockRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购仓库分页")
    @PreAuthorize("@ss.hasPermission('agent:stock:query')")
    public CommonResult<PageResult<AgentStockRespVO>> getStockPage(@Valid AgentStockPageReqVO pageReqVO) {
        PageResult<AgentStockDO> pageResult = stockService.getStockPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgentStockRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购仓库 Excel")
    @PreAuthorize("@ss.hasPermission('agent:stock:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportStockExcel(@Valid AgentStockPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentStockDO> list = stockService.getStockPage( pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购仓库.xls", "数据", AgentStockRespVO.class,
                        BeanUtils.toBean(list, AgentStockRespVO.class));
    }



}