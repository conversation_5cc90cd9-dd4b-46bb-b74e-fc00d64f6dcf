package cn.iocoder.yudao.module.agent.controller.admin.logisticsZone.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 代购物流国家分区 Excel 导入 VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免导入有问题
public class LogisticsZoneImportExcelVO {

    @ExcelProperty("国家编码")
    private String countryCode;

    @ExcelProperty("分区编码")
    private String zoneCode;

    @ExcelProperty("分区名称")
    private String zoneName;

    @ExcelProperty("一级行政区划")
    private String stateProvince;

    @ExcelProperty("二级行政区划")
    private String city;

    @ExcelProperty("三级行政区划")
    private String district;

    @ExcelProperty("特殊区域类型")
    private String specialAreaType;

    @ExcelProperty("完整区域描述")
    private String fullAreaName;

    @ExcelProperty("邮编配置JSON")
    private String postalCodes;

    @ExcelProperty("限制类型")
    private String restrictionType;

    @ExcelProperty("附加费公式")
    private String feeFormula;

    @ExcelProperty("备注说明")
    private String remark;

    @ExcelProperty("排序")
    private Integer sort;

    @ExcelProperty("状态")
    private Integer status;

}
