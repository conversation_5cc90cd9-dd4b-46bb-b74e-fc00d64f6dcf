package cn.iocoder.yudao.module.agent.controller.app.transfer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import cn.iocoder.yudao.module.agent.dal.dataobject.transferitem.AgentTransferItemDO;

@Schema(description = "用户 APP - 代购转运单新增/修改 Request VO")
@Data
@Valid
public class AppAgentTransferSaveReqVO {

    @Schema(description = "转运单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16496")
    private Long id;

    @Schema(description = "转运单流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotEmpty(message = "转运单流水号不能为空")
    private String no;

    //@Schema(description = "转运单来源终端", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotNull(message = "转运单来源终端不能为空")
    //private Integer terminal;

    //@Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19625")
    //@NotNull(message = "用户编号不能为空")
    //private Long userId;

    //@Schema(description = "用户 IP", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotEmpty(message = "用户 IP不能为空")
    //private String userIp;

    @Schema(description = "仓库编号", example = "3212")
    private Long warehouseId;

    @Schema(description = "发货物流公司编号", example = "32487")
    private Long logisticsId;

    @Schema(description = "物流公司单号")
    private String logisticsNo;

    @Schema(description = "物流备注", example = "你猜")
    private String logisticsRemark;

    //@Schema(description = "转运的商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "17170")
    //@NotNull(message = "转运的商品数量不能为空")
    //private Integer itemCount;

    //@Schema(description = "商品总价，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "32672")
    //@NotNull(message = "商品总价，单位：分不能为空")
    //private Integer totalPrice;

    //@Schema(description = "商品重量，单位：kg 千克")
    //private BigDecimal weight;
    //
    //@Schema(description = "商品体积，单位：cm^3")
    //private BigDecimal volume;
    //
    //@Schema(description = "长，单位：cm")
    //private BigDecimal length;
    //
    //@Schema(description = "宽，单位：cm")
    //private BigDecimal width;
    //
    //@Schema(description = "高，单位：cm")
    //private BigDecimal height;

    //@Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    //@NotNull(message = "状态不能为空")
    //private Integer status;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    //@Schema(description = "代购转运单明细列表")
    //private List<AgentTransferItemDO> transferItems;

    @Schema(description = "商品项数组", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品不能为空")
    private List<Item> items;

    @Data
    @Schema(description = "用户 App - 转运单商品项")
    @Valid
    public static class Item {
        /**
         * 转运商品分类编号
         */
        private Long categoryId;
        /**
         * 商品编号
         */
        //private Long productId;
        /**
         * 转运商品名称
         */
        private String productName;
        /**
         * 转运商品链接
         */
        //private String productUrl;
        /**
         * 转运商品图片
         */
        //private String picUrl;
        /**
         * 转运数量
         */
        private Integer count;
        /**
         * 商品价格（单），单位：分
         */
        private Integer price;
        /**
         * 商品重量，单位：kg 千克
         */
        private BigDecimal weight;
        /**
         * 商品体积，单位：cm^3
         */
        //private BigDecimal volume;
        /**
         * 长，单位：cm
         */
        //private BigDecimal length;
        /**
         * 宽，单位：cm
         */
        //private BigDecimal width;
        /**
         * 高，单位：cm
         */
        //private BigDecimal height;
        /**
         * 备注
         */
        //private String remark;
    }

}