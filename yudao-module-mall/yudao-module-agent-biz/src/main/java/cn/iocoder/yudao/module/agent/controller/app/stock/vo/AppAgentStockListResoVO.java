package cn.iocoder.yudao.module.agent.controller.app.stock.vo;

import cn.iocoder.yudao.module.agent.controller.app.base.sku.AppProductSkuBaseRespVO;
import cn.iocoder.yudao.module.agent.controller.app.base.spu.AppProductSpuBaseRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @program: ruoyi-vue-pro
 * @description: 用户 APP - 用户的库存列表 Response VO
 * @author: DingXiao
 * @create: 2025-04-30 16:51
 **/
@Schema(description = "用户 APP - 用户的库存列表 Response VO")
@Data
public class AppAgentStockListResoVO {

    @Schema(description = "购物项的编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer count;

    @Schema(description = "是否选中", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean selected;

    @Schema(description = "单价，单位分，代购商品用户修改的单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "9999")
    private Integer price;

    /**
     * 备注,代购商品用户填写的备注
     */
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.AUTO, example = "备注1")
    private String remark;

    /**
     * 商品 SPU
     */
    private AppProductSpuBaseRespVO spu;
    /**
     * 商品 SKU
     */
    private AppProductSkuBaseRespVO sku;

}
