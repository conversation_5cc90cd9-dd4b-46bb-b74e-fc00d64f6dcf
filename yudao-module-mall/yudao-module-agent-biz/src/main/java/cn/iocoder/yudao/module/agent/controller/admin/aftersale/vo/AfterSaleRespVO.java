package cn.iocoder.yudao.module.agent.controller.admin.aftersale.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 代购售后订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AfterSaleRespVO {

    @Schema(description = "售后编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "32498")
    @ExcelProperty("售后编号")
    private Long id;

    @Schema(description = "售后单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("售后单号")
    private String no;

    @Schema(description = "售后类型", example = "1")
    @ExcelProperty("售后类型")
    private Integer type;

    @Schema(description = "售后状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("售后状态")
    private Integer status;

    @Schema(description = "售后方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("售后方式")
    private Integer way;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15098")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "申请原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不对")
    @ExcelProperty("申请原因")
    private String applyReason;

    @Schema(description = "补充描述", example = "随便")
    @ExcelProperty("补充描述")
    private String applyDescription;

    @Schema(description = "补充凭证图片")
    @ExcelProperty("补充凭证图片")
    private String applyPicUrls;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "27505")
    @ExcelProperty("订单编号")
    private Long orderId;

    @Schema(description = "订单流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单流水号")
    private String orderNo;

    @Schema(description = "订单项编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16838")
    @ExcelProperty("订单项编号")
    private Long orderItemId;

    @Schema(description = "商品 SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15562")
    @ExcelProperty("商品 SPU 编号")
    private Long spuId;

    @Schema(description = "商品 SPU 名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("商品 SPU 名称")
    private String spuName;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "29530")
    @ExcelProperty("商品 SKU 编号")
    private Long skuId;

    @Schema(description = "商品属性数组，JSON 格式")
    @ExcelProperty("商品属性数组，JSON 格式")
    private String properties;

    @Schema(description = "商品图片", example = "https://www.iocoder.cn")
    @ExcelProperty("商品图片")
    private String picUrl;

    @Schema(description = "购买数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "30771")
    @ExcelProperty("购买数量")
    private Integer count;

    @Schema(description = "审批时间")
    @ExcelProperty("审批时间")
    private LocalDateTime auditTime;

    @Schema(description = "审批人", example = "12251")
    @ExcelProperty("审批人")
    private Long auditUserId;

    @Schema(description = "审批备注", example = "不香")
    @ExcelProperty("审批备注")
    private String auditReason;

    @Schema(description = "退款金额，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "32370")
    @ExcelProperty("退款金额，单位：分")
    private Integer refundPrice;

    @Schema(description = "支付退款编号", example = "1364")
    @ExcelProperty("支付退款编号")
    private Long payRefundId;

    @Schema(description = "退款时间")
    @ExcelProperty("退款时间")
    private LocalDateTime refundTime;

    @Schema(description = "退货物流公司编号", example = "31559")
    @ExcelProperty("退货物流公司编号")
    private Long logisticsId;

    @Schema(description = "退货物流单号")
    @ExcelProperty("退货物流单号")
    private String logisticsNo;

    @Schema(description = "退货时间")
    @ExcelProperty("退货时间")
    private LocalDateTime deliveryTime;

    @Schema(description = "收货时间")
    @ExcelProperty("收货时间")
    private LocalDateTime receiveTime;

    @Schema(description = "收货备注", example = "不喜欢")
    @ExcelProperty("收货备注")
    private String receiveReason;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}