package cn.iocoder.yudao.module.agent.controller.app.warehouse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "用户 APP - 代购平台仓库 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppAgentWarehouseRespVO {

    @Schema(description = "仓库编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12926")
    @ExcelProperty("仓库编号")
    private Long id;

    @Schema(description = "仓库编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("仓库编码")
    private String code;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("仓库名称")
    private String name;

    @Schema(description = "仓库地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("仓库地址")
    private String address;

    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("联系电话")
    private String phone;

    @Schema(description = "收件人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收件人")
    private String recipient;

    @Schema(description = "免费存放天数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("免费存放天数")
    private Integer freeDays;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Long sort;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "开启状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "是否默认", example = "2")
    @ExcelProperty("是否默认")
    private Boolean defaultStatus;

}