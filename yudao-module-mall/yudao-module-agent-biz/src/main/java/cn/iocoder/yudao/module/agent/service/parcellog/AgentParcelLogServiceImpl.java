package cn.iocoder.yudao.module.agent.service.parcellog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelLogPageReqVO;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelLogSaveReqVO;
import cn.iocoder.yudao.module.agent.convert.parcel.AgentParcelLogConvert;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcellog.AgentParcelLogDO;
import cn.iocoder.yudao.module.agent.dal.mysql.parcellog.AgentParcelLogMapper;
import cn.iocoder.yudao.module.agent.service.parcel.bo.AgentParcelLogCreateReqBO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 代购包裹订单日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AgentParcelLogServiceImpl implements AgentParcelLogService {

    @Resource
    private AgentParcelLogMapper parcelLogMapper;

    @Override
    public Long createParcelLog(AgentParcelLogSaveReqVO createReqVO) {
        // 插入
        AgentParcelLogDO parcelLog = BeanUtils.toBean(createReqVO, AgentParcelLogDO.class);
        parcelLogMapper.insert(parcelLog);
        // 返回
        return parcelLog.getId();
    }

    @Override
    public void updateParcelLog(AgentParcelLogSaveReqVO updateReqVO) {
        // 校验存在
        validateParcelLogExists(updateReqVO.getId());
        // 更新
        AgentParcelLogDO updateObj = BeanUtils.toBean(updateReqVO, AgentParcelLogDO.class);
        parcelLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteParcelLog(Long id) {
        // 校验存在
        validateParcelLogExists(id);
        // 删除
        parcelLogMapper.deleteById(id);
    }

    private void validateParcelLogExists(Long id) {
        if (parcelLogMapper.selectById(id) == null) {
            //throw exception(PARCEL_LOG_NOT_EXISTS);
        }
    }

    @Override
    public AgentParcelLogDO getParcelLog(Long id) {
        return parcelLogMapper.selectById(id);
    }

    @Override
    public PageResult<AgentParcelLogDO> getParcelLogPage(AgentParcelLogPageReqVO pageReqVO) {
        return parcelLogMapper.selectPage(pageReqVO);
    }


    @Override
    public void createParcelLog(AgentParcelLogCreateReqBO createBO) {
        parcelLogMapper.insert(AgentParcelLogConvert.INSTANCE.convert(createBO) );
    }

    @Override
    public List<AgentParcelLogDO> getParcelLogListByParcelId(Long parcelId) {
        return parcelLogMapper.setListByParcelId(parcelId);
    }

    @Override
    public List<AgentParcelLogDO> getOrderLogListByParcelId(Long id) {
        return parcelLogMapper.selectListByParcelId(id);
    }
}