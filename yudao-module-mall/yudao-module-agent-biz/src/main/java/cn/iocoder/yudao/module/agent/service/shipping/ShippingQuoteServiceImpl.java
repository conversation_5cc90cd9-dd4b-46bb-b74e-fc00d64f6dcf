package cn.iocoder.yudao.module.agent.service.shipping;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.agent.controller.admin.shipping.vo.ShippingQuoteReqVO;
import cn.iocoder.yudao.module.agent.controller.admin.shipping.vo.ShippingQuoteRespVO;
import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteReqVO;
import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteRespVO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductPriceDO;
import cn.iocoder.yudao.module.agent.dal.mysql.logisticsProduct.LogisticsProductMapper;
import cn.iocoder.yudao.module.agent.dal.mysql.logisticsProduct.LogisticsProductPriceMapper;
import cn.iocoder.yudao.module.agent.enums.logistics.AgentLogisticsPriceTypeEnum;
import cn.iocoder.yudao.module.agent.service.logisticsProduct.LogisticsProductService;
import cn.iocoder.yudao.module.agent.service.shipping.bo.LogisticsPlanBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingCalculationReqBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingQuoteRespBO;
import cn.iocoder.yudao.module.agent.util.CategoryRestrictionUtil;
import cn.iocoder.yudao.module.agent.util.SizeRestrictionUtil;
import cn.iocoder.yudao.module.agent.util.TieredIncrementalPriceCalculator;
import cn.iocoder.yudao.module.agent.util.TieredPriceCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运费查询服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShippingQuoteServiceImpl implements ShippingQuoteService {

    @Resource
    private LogisticsProductMapper logisticsProductMapper;

    @Resource
    private LogisticsProductPriceMapper logisticsProductPriceMapper;

    @Resource
    private LogisticsProductService logisticsProductService;

    @Resource
    private ShippingCalculationService shippingCalculationService;

    @Override
    public List<ShippingQuoteRespBO> getShippingQuotes(ShippingCalculationReqBO reqVO) {
        log.info("=== 开始查询运费报价 ===");
        log.info("查询参数: 国家={}, 重量={}g, 长宽高=[{},{},{}], 分类={}",
                reqVO.getCountryCode(), reqVO.getWeight(),
                reqVO.getLength(), reqVO.getWidth(), reqVO.getHeight(),
                reqVO.getCategoryIds());

        try {
            // 1. 直接查询该国家的所有有效价格规则，避免冗余查询
            List<LogisticsProductPriceDO> availablePrices = getAvailablePricesByCountry(reqVO.getCountryCode());
            log.info("查询到{}条价格规则", availablePrices.size());

            if (CollUtil.isEmpty(availablePrices)) {
                log.warn("国家{}暂无可用的物流方案", reqVO.getCountryCode());
                return new ArrayList<>();
            }

            // 打印价格规则详情并验证数据完整性
            for (LogisticsProductPriceDO price : availablePrices) {
                log.info("价格规则详情: productId={}, priceId={}, 重量限制=[{}-{}]g, 状态={}, 价格类型={}",
                        price.getProductId(), price.getId(),
                        price.getMinWeight(), price.getMaxWeight(), price.getStatus(), price.getPriceType());

                // 验证价格规则数据完整性
                validatePriceRuleData(price);
            }

            // 2. 批量获取相关的物流产品信息，减少数据库查询次数
            Set<Long> productIds = availablePrices.stream()
                    .map(LogisticsProductPriceDO::getProductId)
                    .collect(Collectors.toSet());
            log.info("需要查询的产品ID: {}", productIds);

            Map<Long, LogisticsProductDO> productMap = getProductMapByIds(productIds);
            log.info("查询到{}个产品信息", productMap.size());

            // 打印产品详情
            productMap.forEach((id, product) -> {
                log.info("产品详情: id={}, name={}, status={}, sort={}",
                        id, product.getNameZh(), product.getStatus(), product.getSort());
            });

            // 3. 并行计算运费报价，提高计算效率
            List<ShippingQuoteRespBO> quotes = availablePrices.parallelStream()
                    .map(priceRule -> calculateQuoteForPriceRuleBO(priceRule, productMap.get(priceRule.getProductId()), reqVO))
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(ShippingQuoteRespBO::getSort, Comparator.nullsLast(Integer::compareTo)))
                    .collect(Collectors.toList());

            log.info("=== 运费查询完成 ===");
            log.info("总共找到{}个方案，其中可用{}个，不可用{}个",
                    quotes.size(),
                    quotes.stream().mapToInt(q -> q.getAvailable() ? 1 : 0).sum(),
                    quotes.stream().mapToInt(q -> q.getAvailable() ? 0 : 1).sum());

            return quotes;

        } catch (Exception e) {
            log.error("运费查询失败: 国家={}, 重量={}g", reqVO.getCountryCode(), reqVO.getWeight(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据国家编码获取所有可用的价格规则
     *
     * 优化策略：
     * 1. 直接按国家编码查询，避免查询无关数据
     * 2. 只查询启用状态的价格规则
     * 3. 检查生效时间和失效时间，确保价格规则在有效期内
     * 4. 按产品排序和创建时间排序，确保结果稳定
     *
     * @param countryCode 国家编码
     * @return 该国家的所有有效价格规则列表
     */
    private List<LogisticsProductPriceDO> getAvailablePricesByCountry(String countryCode) {
        log.info("查询国家{}的价格规则", countryCode);

        LocalDateTime now = LocalDateTime.now();
        log.info("当前时间: {}", now);

        List<LogisticsProductPriceDO> prices = logisticsProductPriceMapper.selectList(
            new LambdaQueryWrapperX<LogisticsProductPriceDO>()
                .eq(LogisticsProductPriceDO::getCountryCode, countryCode)
                .eq(LogisticsProductPriceDO::getStatus, CommonStatusEnum.ENABLE.getStatus()) // 只查询启用状态
                // 生效时间检查：如果设置了生效时间，则当前时间必须大于等于生效时间
                .and(wrapper -> wrapper
                    .isNull(LogisticsProductPriceDO::getEffectiveTime) // 未设置生效时间
                    .or()
                    .le(LogisticsProductPriceDO::getEffectiveTime, now) // 或者当前时间 >= 生效时间
                )
                // 失效时间检查：如果设置了失效时间，则当前时间必须小于失效时间
                .and(wrapper -> wrapper
                    .isNull(LogisticsProductPriceDO::getExpireTime) // 未设置失效时间
                    .or()
                    .gt(LogisticsProductPriceDO::getExpireTime, now) // 或者当前时间 < 失效时间
                )
                .orderByAsc(LogisticsProductPriceDO::getProductId)
                .orderByDesc(LogisticsProductPriceDO::getCreateTime)
        );

        log.info("查询SQL: SELECT * FROM logistics_product_price WHERE country_code = '{}' AND status = 1 AND (effective_time IS NULL OR effective_time <= '{}') AND (expire_time IS NULL OR expire_time > '{}')",
                countryCode, now, now);
        log.info("查询结果: 找到{}条有效价格规则", prices.size());

        // 记录时间过滤的详细信息
        if (log.isDebugEnabled()) {
            for (LogisticsProductPriceDO price : prices) {
                log.debug("价格规则ID={}, 产品ID={}, 生效时间={}, 失效时间={}",
                        price.getId(), price.getProductId(), price.getEffectiveTime(), price.getExpireTime());
            }
        }

        return prices;
    }

    /**
     * 验证价格规则数据完整性
     *
     * 验证逻辑：
     * - 最大重量：null或0表示无限制，负数才是异常
     * - 最小重量：负数是异常
     * - 重量逻辑：最小重量不能大于最大重量（当最大重量>0时）
     *
     * @param priceRule 价格规则
     */
    private void validatePriceRuleData(LogisticsProductPriceDO priceRule) {
        List<String> issues = new ArrayList<>();

        // 检查重量限制
        if (priceRule.getMaxWeight() != null && priceRule.getMaxWeight() < 0) {
            issues.add("最大重量限制异常: " + priceRule.getMaxWeight() + "g (不能为负数)");
        }

        if (priceRule.getMinWeight() != null && priceRule.getMinWeight() < 0) {
            issues.add("最小重量限制异常: " + priceRule.getMinWeight() + "g (不能为负数)");
        }

        // 检查重量逻辑（只有当最大重量大于0时才检查）
        if (priceRule.getMinWeight() != null && priceRule.getMaxWeight() != null
            && priceRule.getMaxWeight() > 0 && priceRule.getMinWeight() > priceRule.getMaxWeight()) {
            issues.add(String.format("重量限制逻辑错误: 最小重量(%dg) > 最大重量(%dg)",
                    priceRule.getMinWeight(), priceRule.getMaxWeight()));
        }

        // 检查价格配置
        if (StrUtil.isNotBlank(priceRule.getTieredIncrementalPrices())) {
            // 阶梯递增价格配置检查
            try {
                JSONUtil.parseArray(priceRule.getTieredIncrementalPrices());
            } catch (Exception e) {
                issues.add("阶梯递增价格配置JSON格式错误");
            }
        } else if (AgentLogisticsPriceTypeEnum.TIERED.getCode().equals(priceRule.getPriceType())) {
            if (StrUtil.isBlank(priceRule.getTieredPrices())) {
                issues.add("阶梯价格类型但阶梯价格配置为空");
            }
        } else {
            if (priceRule.getFirstPrice() == null || priceRule.getFirstPrice() <= 0) {
                issues.add("首重价格配置异常: " + priceRule.getFirstPrice());
            }
            if (priceRule.getFirstUnit() == null || priceRule.getFirstUnit() <= 0) {
                issues.add("首重重量配置异常: " + priceRule.getFirstUnit());
            }
        }

        // 检查状态
        if (priceRule.getStatus() == null) {
            issues.add("状态字段为空");
        }

        // 记录重量限制配置信息
        String weightLimitInfo = String.format("重量限制配置: 最小=%s, 最大=%s",
                priceRule.getMinWeight() != null && priceRule.getMinWeight() > 0 ? priceRule.getMinWeight() + "g" : "无限制",
                priceRule.getMaxWeight() != null && priceRule.getMaxWeight() > 0 ? priceRule.getMaxWeight() + "g" : "无限制");
        log.debug("价格规则 (priceRuleId={}): {}", priceRule.getId(), weightLimitInfo);

        // 如果有问题，记录警告日志
        if (!issues.isEmpty()) {
            log.warn("价格规则数据异常 (priceRuleId={}): {}", priceRule.getId(), String.join("; ", issues));
            log.warn("完整价格规则数据: {}", priceRule);
        }
    }

    /**
     * 批量获取物流产品信息
     *
     * 优化策略：
     * 1. 使用IN查询批量获取，减少数据库交互次数
     * 2. 转换为Map结构，提高后续查找效率
     * 3. 只查询启用状态的产品
     *
     * @param productIds 产品ID集合
     * @return 产品ID到产品信息的映射
     */
    private Map<Long, LogisticsProductDO> getProductMapByIds(Set<Long> productIds) {
        if (CollUtil.isEmpty(productIds)) {
            log.info("产品ID集合为空，返回空Map");
            return new HashMap<>();
        }

        log.info("批量查询产品信息: productIds={}", productIds);

        List<LogisticsProductDO> products = logisticsProductMapper.selectList(
            new LambdaQueryWrapperX<LogisticsProductDO>()
                .in(LogisticsProductDO::getId, productIds)
                .eq(LogisticsProductDO::getStatus, 0) // 0表示启用状态
        );

        log.info("查询SQL: SELECT * FROM logistics_product WHERE id IN ({}) AND status = 0",
                productIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        log.info("查询结果: 找到{}个产品", products.size());

        if (products.size() != productIds.size()) {
            Set<Long> foundIds = products.stream().map(LogisticsProductDO::getId).collect(Collectors.toSet());
            Set<Long> missingIds = new HashSet<>(productIds);
            missingIds.removeAll(foundIds);
            log.warn("部分产品未找到或未启用: missingIds={}", missingIds);
        }

        return products.stream()
                .collect(Collectors.toMap(LogisticsProductDO::getId, product -> product));
    }

    /**
     * 基于价格规则计算运费报价
     *
     * 核心计算逻辑：
     * 1. 快速验证基础限制条件（重量、尺寸）
     * 2. 计算体积重和计费重量
     * 3. 根据价格类型选择计算方式（阶梯价格 vs 首重续重）
     * 4. 计算各项附加费用
     * 5. 构建完整的报价响应
     *
     * @param priceRule 价格规则
     * @param product 物流产品信息
     * @param reqVO 查询请求
     * @return 运费报价，如果不可用则返回null
     */
    private ShippingQuoteRespVO calculateQuoteForPriceRule(LogisticsProductPriceDO priceRule,
                                                          LogisticsProductDO product,
                                                           ShippingCalculationReqBO reqVO) {
        log.info("--- 开始计算产品运费 ---");
        log.info("产品: id={}, name={}", product != null ? product.getId() : "null",
                product != null ? product.getNameZh() : "null");
        log.info("价格规则: id={}, 重量限制=[{}-{}]g, 价格类型={}",
                priceRule.getId(), priceRule.getMinWeight(), priceRule.getMaxWeight(), priceRule.getPriceType());

        if (product == null) {
            log.warn("物流产品不存在: productId={}", priceRule.getProductId());
            return null;
        }

        try {
            // 1. 快速检查重量限制，避免不必要的计算
            log.info("检查重量限制: 请求重量={}g, 限制范围=[{}-{}]g",
                    reqVO.getWeight(), priceRule.getMinWeight(), priceRule.getMaxWeight());

            String weightCheckResult = checkWeightLimits(priceRule, reqVO.getWeight());
            if (weightCheckResult != null) {
                log.warn("重量检查失败: {}", weightCheckResult);
                return createUnavailableQuote(product, priceRule, weightCheckResult);
            }
            log.info("重量检查通过");

            // 2. 检查尺寸限制（如果提供了尺寸信息）
            if (reqVO.getLength() != null && reqVO.getWidth() != null && reqVO.getHeight() != null) {
                log.info("检查尺寸限制: 长宽高=[{},{},{}]cm", reqVO.getLength(), reqVO.getWidth(), reqVO.getHeight());
                String sizeCheckResult = checkSizeLimits(priceRule, reqVO);
                if (sizeCheckResult != null) {
                    log.warn("尺寸检查失败: {}", sizeCheckResult);
                    return createUnavailableQuote(product, priceRule, sizeCheckResult);
                }
                log.info("尺寸检查通过");
            } else {
                log.info("未提供尺寸信息，跳过尺寸检查");
            }

            // 3. 检查商品分类限制
            String categoryCheckResult = checkCategoryLimits(product, reqVO);
            if (categoryCheckResult != null) {
                log.warn("分类检查失败: {}", categoryCheckResult);
                return createUnavailableQuote(product, priceRule, categoryCheckResult);
            }
            log.info("分类检查通过");

            // 4. 计算体积重和计费重量
            VolumeWeightResult volumeResult = calculateVolumeWeight(reqVO);
            Integer chargeableWeight = Math.max(reqVO.getWeight(), volumeResult.getVolumeWeight());
            log.info("体积重计算: 实重={}g, 体积重={}g, 计费重量={}g",
                    reqVO.getWeight(), volumeResult.getVolumeWeight(), chargeableWeight);

            // 5. 计算运费详情
            log.info("开始计算运费详情");
            ShippingQuoteRespVO.FeeDetail feeDetail = calculateOptimizedFeeDetail(
                product, priceRule, reqVO, volumeResult, chargeableWeight);
            log.info("运费计算完成: 总费用={}", feeDetail != null ? feeDetail.getTotal() : "null");

            // 6. 构建完整的报价响应
            ShippingQuoteRespVO result = buildOptimizedQuoteResponse(product, priceRule, feeDetail);
            log.info("--- 产品运费计算完成 ---");
            log.info("结果: 可用={}, 总费用={}", result.getAvailable(),
                    result.getFeeDetail() != null ? result.getFeeDetail().getTotal() : "null");

            return result;

        } catch (Exception e) {
            log.error("计算运费失败: productId={}, priceId={}, country={}",
                     priceRule.getProductId(), priceRule.getId(), reqVO.getCountryCode(), e);
            return null;
        }
    }

    /**
     * 体积重计算结果
     */
    private static class VolumeWeightResult {
        private final Integer volumeWeight;
        private final boolean needVolumeCal;
        private final Integer volumeBase;

        public VolumeWeightResult(Integer volumeWeight, boolean needVolumeCal, Integer volumeBase) {
            this.volumeWeight = volumeWeight;
            this.needVolumeCal = needVolumeCal;
            this.volumeBase = volumeBase;
        }

        public Integer getVolumeWeight() { return volumeWeight; }
        public boolean isNeedVolumeCal() { return needVolumeCal; }
        public Integer getVolumeBase() { return volumeBase; }
    }

    /**
     * 快速检查重量限制
     *
     * 业务逻辑：
     * - 最小重量限制：如果设置了且大于0，则检查
     * - 最大重量限制：如果为null或0，表示无限制；如果大于0，则检查
     *
     * @param priceRule 价格规则
     * @param weight 重量(g)
     * @return 如果超限返回错误信息，否则返回null
     */
    private String checkWeightLimits(LogisticsProductPriceDO priceRule, Integer weight) {
        log.debug("检查重量限制详情: priceRuleId={}, 重量={}g, 最小重量={}g, 最大重量={}g",
                priceRule.getId(), weight, priceRule.getMinWeight(), priceRule.getMaxWeight());

        // 检查最小重量限制（如果设置了且大于0）
        if (priceRule.getMinWeight() != null && priceRule.getMinWeight() > 0 && weight < priceRule.getMinWeight()) {
            String errorMsg = String.format("重量%dg低于最小限制%dg", weight, priceRule.getMinWeight());
            log.info("重量检查失败: {}", errorMsg);
            return errorMsg;
        }

        // 检查最大重量限制（只有当设置了且大于0时才检查）
        if (priceRule.getMaxWeight() != null && priceRule.getMaxWeight() > 0 && weight > priceRule.getMaxWeight()) {
            String errorMsg = String.format("重量%dg超过最大限制%dg", weight, priceRule.getMaxWeight());
            log.info("重量检查失败: {}", errorMsg);
            return errorMsg;
        }

        // 记录重量限制状态
        if (priceRule.getMaxWeight() == null || priceRule.getMaxWeight() <= 0) {
            log.debug("该产品无最大重量限制");
        }
        if (priceRule.getMinWeight() == null || priceRule.getMinWeight() <= 0) {
            log.debug("该产品无最小重量限制");
        }

        log.debug("重量检查通过");
        return null;
    }

    /**
     * 快速检查尺寸限制
     *
     * @param priceRule 价格规则
     * @param reqVO 查询请求
     * @return 如果超限返回错误信息，否则返回null
     */
    private String checkSizeLimits(LogisticsProductPriceDO priceRule, ShippingCalculationReqBO reqVO) {
        // 如果没有提供尺寸信息，跳过尺寸检查
        if (reqVO.getLength() == null || reqVO.getWidth() == null || reqVO.getHeight() == null) {
            return null;
        }

        // 使用现有的尺寸检查工具
        SizeRestrictionUtil.SizeCheckResult sizeCheck = SizeRestrictionUtil.checkSize(
            reqVO.getLength(), reqVO.getWidth(), reqVO.getHeight(), priceRule.getSizeRestrictions());

        return sizeCheck.isPassed() ? null : sizeCheck.getErrorMessage();
    }

    /**
     * 快速检查商品分类限制
     *
     * @param product 物流产品
     * @param reqVO 查询请求
     * @return 如果被限制返回错误信息，否则返回null
     */
    private String checkCategoryLimits(LogisticsProductDO product, ShippingCalculationReqBO reqVO) {
        // 如果没有提供分类ID，跳过检查
        if (CollUtil.isEmpty(reqVO.getCategoryIds())) {
            log.debug("未提供分类ID，跳过分类限制检查");
            return null;
        }

        // 如果产品没有分类限制配置，跳过检查
        if (StrUtil.isBlank(product.getCategoryRestrictions())) {
            log.debug("产品无分类限制配置，跳过检查");
            return null;
        }

        log.info("检查商品分类限制: 产品ID={}, 分类ID={}", product.getId(), reqVO.getCategoryIds());
        log.debug("分类限制配置: {}", product.getCategoryRestrictions());

        // 使用工具类进行检查
        CategoryRestrictionUtil.CategoryCheckResult checkResult =
            CategoryRestrictionUtil.checkCategoryRestrictions(
                product.getCategoryRestrictions(),
                reqVO.getCategoryIds()
            );

        return checkResult.isPassed() ? null : checkResult.getErrorMessage();
    }

    /**
     * 高效计算体积重
     *
     * 计算逻辑：
     * 1. 如果没有提供完整尺寸信息，体积重为0
     * 2. 使用标准公式：长×宽×高(cm) ÷ 8000 × 1000 = 体积重(g)
     * 3. 判断是否需要按体积重计费
     *
     * @param reqVO 查询请求
     * @return 体积重计算结果
     */
    private VolumeWeightResult calculateVolumeWeight(ShippingCalculationReqBO reqVO) {
        // 如果没有提供完整的尺寸信息，体积重为0
        if (reqVO.getLength() == null || reqVO.getWidth() == null || reqVO.getHeight() == null) {
            return new VolumeWeightResult(0, false, 8000);
        }

        // 计算体积重：长×宽×高(cm) ÷ 8000 × 1000 = 体积重(g)
        BigDecimal volume = reqVO.getLength()
            .multiply(reqVO.getWidth())
            .multiply(reqVO.getHeight());

        Integer volumeWeight = volume.divide(new BigDecimal(8000), 0, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal(1000))
                                    .intValue();

        // 判断是否需要按体积重计费
        boolean needVolumeCal = volumeWeight > reqVO.getWeight();

        return new VolumeWeightResult(volumeWeight, needVolumeCal, 8000);
    }

    /**
     * 创建不可用的报价
     *
     * @param product 物流产品
     * @param priceRule 价格规则
     * @param reason 不可用原因
     * @return 不可用的报价对象
     */
    private ShippingQuoteRespVO createUnavailableQuote(LogisticsProductDO product,
                                                      LogisticsProductPriceDO priceRule,
                                                      String reason) {
        return buildOptimizedQuoteResponse(product, priceRule, null, false, reason);
    }

    /**
     * 优化的费用详情计算
     *
     * 计算策略：
     * 1. 根据价格类型选择最优计算方式
     * 2. 阶梯价格：使用TieredPriceCalculator进行精确计算
     * 3. 首重续重：使用传统递增计算方式
     * 4. 统一处理各项附加费用
     *
     * @param product 物流产品
     * @param priceRule 价格规则
     * @param reqVO 查询请求
     * @param volumeResult 体积重计算结果
     * @param chargeableWeight 计费重量
     * @return 费用详情
     */
    private ShippingQuoteRespVO.FeeDetail calculateOptimizedFeeDetail(LogisticsProductDO product,
                                                                     LogisticsProductPriceDO priceRule,
                                                                      ShippingCalculationReqBO reqVO,
                                                                     VolumeWeightResult volumeResult,
                                                                     Integer chargeableWeight) {
        ShippingQuoteRespVO.FeeDetail feeDetail = new ShippingQuoteRespVO.FeeDetail();

        // 设置基础信息
        setBasicFeeInfo(feeDetail, reqVO, volumeResult, chargeableWeight);

        // 计算基础运费和挂号费
        FeeCalculationResult feeResult = calculateBaseFeeAndRegistration(priceRule, chargeableWeight);

        // 设置运费信息
        setFreightInfo(feeDetail, priceRule, feeResult);

        // 设置附加费用
        setAdditionalFees(feeDetail, priceRule);

        // 计算总费用
        calculateTotalFee(feeDetail);

        return feeDetail;
    }

    /**
     * 费用计算结果
     */
    private static class FeeCalculationResult {
        private final BigDecimal baseFee;
        private final BigDecimal registrationFee;
        private final boolean useTieredRegistrationFee;

        public FeeCalculationResult(BigDecimal baseFee, BigDecimal registrationFee, boolean useTieredRegistrationFee) {
            this.baseFee = baseFee;
            this.registrationFee = registrationFee;
            this.useTieredRegistrationFee = useTieredRegistrationFee;
        }

        public BigDecimal getBaseFee() { return baseFee; }
        public BigDecimal getRegistrationFee() { return registrationFee; }
        public boolean isUseTieredRegistrationFee() { return useTieredRegistrationFee; }
    }

    /**
     * 计算基础运费和挂号费
     *
     * @param priceRule 价格规则
     * @param chargeableWeight 计费重量
     * @return 费用计算结果
     */
    private FeeCalculationResult calculateBaseFeeAndRegistration(LogisticsProductPriceDO priceRule, Integer chargeableWeight) {
        log.info("=== 开始计算基础运费 ===");
        log.info("价格类型: {}, 计费重量: {}g", priceRule.getPriceType(), chargeableWeight);

        BigDecimal baseFee;
        BigDecimal registrationFee;
        boolean useTieredRegistrationFee = false;

        // 根据价格类型和配置选择计算方式
        if (StrUtil.isNotBlank(priceRule.getTieredIncrementalPrices())) {
            log.info("使用阶梯递增价格计算");
            log.info("阶梯递增价格配置: {}", priceRule.getTieredIncrementalPrices());
            log.info("默认挂号费配置: {}", priceRule.getRegistrationFee());

            // 阶梯递增价格计算
            TieredIncrementalPriceCalculator.TieredIncrementalResult tieredIncrementalResult =
                TieredIncrementalPriceCalculator.calculateTieredIncrementalPrice(
                    priceRule.getTieredIncrementalPrices(),
                    chargeableWeight,
                    priceRule.getRegistrationFee()
                );

            baseFee = tieredIncrementalResult.getBaseFee();
            registrationFee = tieredIncrementalResult.getRegistrationFee();
            useTieredRegistrationFee = tieredIncrementalResult.isUseTieredRegistrationFee();

            log.info("阶梯递增价格计算结果: 基础运费={}分, 挂号费={}分, 使用阶梯挂号费={}",
                    baseFee, registrationFee, useTieredRegistrationFee);

        } else if (AgentLogisticsPriceTypeEnum.TIERED.getCode().equals(priceRule.getPriceType()) && StrUtil.isNotBlank(priceRule.getTieredPrices())) {
            log.info("使用纯阶梯价格计算");
            log.info("阶梯价格配置: {}", priceRule.getTieredPrices());
            log.info("挂号费配置: {}", priceRule.getRegistrationFee());

            // 纯阶梯价格计算
            TieredPriceCalculator.TieredPriceResult tieredResult =
                TieredPriceCalculator.calculateTieredPrice(
                    priceRule.getTieredPrices(),
                    chargeableWeight,
                    priceRule.getRegistrationFee()
                );

            baseFee = tieredResult.getBaseFee();
            registrationFee = tieredResult.getRegistrationFee();
            useTieredRegistrationFee = tieredResult.isUseTieredRegistrationFee();

            log.info("纯阶梯价格计算结果: 基础运费={}分, 挂号费={}分, 使用阶梯挂号费={}",
                    baseFee, registrationFee, useTieredRegistrationFee);

        } else {
            log.info("使用首重续重计算");
            log.info("首重: {}g = {}分, 续重: {}g = {}分",
                    priceRule.getFirstUnit(), priceRule.getFirstPrice(),
                    priceRule.getAdditionalUnit(), priceRule.getAdditionalPrice());

            // 首重续重计算
            baseFee = calculateIncrementalFee(priceRule, chargeableWeight);
            registrationFee = priceRule.getRegistrationFee() != null ?
                new BigDecimal(priceRule.getRegistrationFee()) : BigDecimal.ZERO;

            log.info("首重续重计算结果: 基础运费={}分, 挂号费={}分", baseFee, registrationFee);
        }

        log.info("=== 基础运费计算完成 ===");
        return new FeeCalculationResult(baseFee, registrationFee, useTieredRegistrationFee);
    }

    /**
     * 设置基础费用信息
     */
    private void setBasicFeeInfo(ShippingQuoteRespVO.FeeDetail feeDetail,
                                 ShippingCalculationReqBO reqVO,
                                VolumeWeightResult volumeResult,
                                Integer chargeableWeight) {
        feeDetail.setWeight(reqVO.getWeight());
        feeDetail.setLength(reqVO.getLength());
        feeDetail.setWidth(reqVO.getWidth());
        feeDetail.setHeight(reqVO.getHeight());
        feeDetail.setVolumeWeight(volumeResult.getVolumeWeight());
        feeDetail.setChargeableWeight(chargeableWeight);
        feeDetail.setNeedVolumeCal(volumeResult.isNeedVolumeCal());
        feeDetail.setVolumeBase(volumeResult.getVolumeBase());

        log.info("基础费用信息: 实重={}g, 体积重={}g, 计费重量={}g, 货币=CNY(分)", reqVO.getWeight(), volumeResult.getVolumeWeight(), chargeableWeight);
    }

    /**
     * 设置运费信息
     */
    private void setFreightInfo(ShippingQuoteRespVO.FeeDetail feeDetail,
                               LogisticsProductPriceDO priceRule,
                               FeeCalculationResult feeResult) {
        log.info("设置运费信息: 基础运费={}分, 挂号费={}分", feeResult.getBaseFee(), feeResult.getRegistrationFee());

        // 保持分为单位，不进行转换
        feeDetail.setFreight(feeResult.getBaseFee().toString());

        // 设置挂号费（如果有的话）
        if (feeResult.getRegistrationFee() != null && feeResult.getRegistrationFee().compareTo(BigDecimal.ZERO) > 0) {
            feeDetail.setAdditionalFee(feeResult.getRegistrationFee().toString());
            log.info("挂号费: {}分", feeResult.getRegistrationFee());
        } else {
            feeDetail.setAdditionalFee("0");
            log.info("无挂号费");
        }

        // 设置首重续重信息
        if (priceRule.getFirstUnit() != null) {
            feeDetail.setWeightFirst(priceRule.getFirstUnit());
            if (priceRule.getFirstPrice() != null) {
                feeDetail.setFeeFirst(priceRule.getFirstPrice().toString());
                log.info("首重信息: {}g = {}分", priceRule.getFirstUnit(), priceRule.getFirstPrice());
            }
        }

        if (priceRule.getAdditionalUnit() != null) {
            feeDetail.setWeightContinue(priceRule.getAdditionalUnit());
            if (priceRule.getAdditionalPrice() != null) {
                feeDetail.setFeeContinue(priceRule.getAdditionalPrice().toString());
                log.info("续重信息: {}g = {}分", priceRule.getAdditionalUnit(), priceRule.getAdditionalPrice());
            }
        }
    }

    /**
     * 设置附加费用
     */
    private void setAdditionalFees(ShippingQuoteRespVO.FeeDetail feeDetail, LogisticsProductPriceDO priceRule) {
        log.info("=== 设置附加费用 ===");

        BigDecimal operationFee = priceRule.getOperationFee() != null ?
            new BigDecimal(priceRule.getOperationFee()) : BigDecimal.ZERO;
        feeDetail.setOperationFee(operationFee.toString());
        log.info("操作费: {}分", operationFee);

        BigDecimal serviceFee = priceRule.getServiceFee() != null ?
            new BigDecimal(priceRule.getServiceFee()) : BigDecimal.ZERO;
        feeDetail.setServiceFee(serviceFee.toString());
        log.info("服务费: {}分", serviceFee);

        BigDecimal customsFee = priceRule.getCustomsFee() != null ?
            new BigDecimal(priceRule.getCustomsFee()) : BigDecimal.ZERO;
        feeDetail.setCustomsFee(customsFee.toString());
        log.info("清关费: {}分", customsFee);

        // 燃油费和空运附加费暂时设为0
        feeDetail.setFuelFee("0");
        feeDetail.setAirSurcharge("0");
        log.info("燃油费: 0分, 空运附加费: 0分");

        log.info("=== 附加费用设置完成 ===");
    }

    /**
     * 计算总费用
     */
    private void calculateTotalFee(ShippingQuoteRespVO.FeeDetail feeDetail) {
        log.info("=== 计算总费用 ===");

        BigDecimal freight = new BigDecimal(feeDetail.getFreight());
        BigDecimal operationFee = new BigDecimal(feeDetail.getOperationFee());
        BigDecimal serviceFee = new BigDecimal(feeDetail.getServiceFee());
        BigDecimal customsFee = new BigDecimal(feeDetail.getCustomsFee());

        // 添加挂号费到总费用计算中
        BigDecimal additionalFee = BigDecimal.ZERO;
        if (feeDetail.getAdditionalFee() != null && !feeDetail.getAdditionalFee().equals("0")) {
            additionalFee = new BigDecimal(feeDetail.getAdditionalFee());
        }

        BigDecimal total = freight.add(operationFee).add(serviceFee).add(customsFee).add(additionalFee);

        log.info("费用明细: 运费={}分, 操作费={}分, 服务费={}分, 清关费={}分, 挂号费={}分",
                freight, operationFee, serviceFee, customsFee, additionalFee);
        log.info("总费用: {} + {} + {} + {} + {} = {}分",
                freight, operationFee, serviceFee, customsFee, additionalFee, total);

        // 保持分为单位
        feeDetail.setTotal(total.toString());
        feeDetail.setCurrentTotal(total.toString());

        log.info("=== 总费用计算完成 ===");
    }

    /**
     * 计算体积重
     */
    private Integer calculateVolumeWeight(ShippingQuoteReqVO reqVO, LogisticsProductDO product) {
        if (reqVO.getLength() == null || reqVO.getWidth() == null || reqVO.getHeight() == null) {
            return 0;
        }

        // 体积重(g) = 长*宽*高(cm) / 8000 * 1000
        BigDecimal volume = reqVO.getLength()
            .multiply(reqVO.getWidth())
            .multiply(reqVO.getHeight());
        
        return volume.divide(new BigDecimal(8000), 0, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(1000))
                    .intValue();
    }

    /**
     * 计算各项费用
     */
    private void calculateFees(ShippingQuoteRespVO.FeeDetail feeDetail, 
                              LogisticsProductPriceDO productPrice, 
                              Integer chargeableWeight) {
        
        BigDecimal baseFee = BigDecimal.ZERO;
        BigDecimal registrationFee = BigDecimal.ZERO;
        
        // 根据价格类型计算基础运费
        if (AgentLogisticsPriceTypeEnum.TIERED.getCode().equals(productPrice.getPriceType()) &&
            StrUtil.isNotBlank(productPrice.getTieredPrices())) {
            
            // 阶梯价格计算
            TieredPriceCalculator.TieredPriceResult tieredResult = 
                TieredPriceCalculator.calculateTieredPrice(
                    productPrice.getTieredPrices(), 
                    chargeableWeight, 
                    productPrice.getRegistrationFee()
                );
            
            baseFee = tieredResult.getBaseFee();
            registrationFee = tieredResult.getRegistrationFee();
            
        } else {
            // 首重续重计算
            baseFee = calculateIncrementalFee(productPrice, chargeableWeight);
            if (productPrice.getRegistrationFee() != null) {
                registrationFee = new BigDecimal(productPrice.getRegistrationFee());
            }
        }

        // 设置首重续重信息（修正为分为单位）
        if (productPrice.getFirstUnit() != null) {
            feeDetail.setWeightFirst(productPrice.getFirstUnit());
            if (productPrice.getFirstPrice() != null) {
                feeDetail.setFeeFirst(productPrice.getFirstPrice().toString());
            }
        }

        if (productPrice.getAdditionalUnit() != null) {
            feeDetail.setWeightContinue(productPrice.getAdditionalUnit());
            if (productPrice.getAdditionalPrice() != null) {
                feeDetail.setFeeContinue(productPrice.getAdditionalPrice().toString());
            }
        }

        // 设置各项费用（修正为分为单位）
        feeDetail.setFreight(baseFee.toString());

        BigDecimal operationFee = productPrice.getOperationFee() != null ?
            new BigDecimal(productPrice.getOperationFee()) : BigDecimal.ZERO;
        feeDetail.setOperationFee(operationFee.toString());

        BigDecimal serviceFee = productPrice.getServiceFee() != null ?
            new BigDecimal(productPrice.getServiceFee()) : BigDecimal.ZERO;
        feeDetail.setServiceFee(serviceFee.toString());

        BigDecimal customsFee = productPrice.getCustomsFee() != null ?
            new BigDecimal(productPrice.getCustomsFee()) : BigDecimal.ZERO;
        feeDetail.setCustomsFee(customsFee.toString());

        // 设置挂号费
        feeDetail.setAdditionalFee(registrationFee.toString());

        // 燃油费和空运附加费暂时设为0
        feeDetail.setFuelFee("0");
        feeDetail.setAirSurcharge("0");

        // 计算总费用
        BigDecimal totalFee = baseFee.add(registrationFee).add(operationFee).add(serviceFee).add(customsFee);
        feeDetail.setTotal(totalFee.toString());
        feeDetail.setCurrentTotal(totalFee.toString());
    }

    /**
     * 计算递增式运费（首重+续重）
     */
    private BigDecimal calculateIncrementalFee(LogisticsProductPriceDO productPrice, Integer weightGrams) {
        log.info("--- 首重续重详细计算 ---");

        if (productPrice.getFirstUnit() == null || productPrice.getFirstPrice() == null) {
            log.warn("首重配置不完整: 首重重量={}, 首重价格={}",
                    productPrice.getFirstUnit(), productPrice.getFirstPrice());
            return BigDecimal.ZERO;
        }

        // 首重费用
        BigDecimal totalFee = new BigDecimal(productPrice.getFirstPrice());
        log.info("首重费用: {}g = {}分", productPrice.getFirstUnit(), totalFee);

        // 计算续重费用
        if (weightGrams > productPrice.getFirstUnit() &&
            productPrice.getAdditionalUnit() != null &&
            productPrice.getAdditionalPrice() != null) {

            int additionalWeight = weightGrams - productPrice.getFirstUnit();
            int additionalUnits = (int) Math.ceil((double) additionalWeight / productPrice.getAdditionalUnit());
            BigDecimal additionalFee = new BigDecimal(productPrice.getAdditionalPrice()).multiply(new BigDecimal(additionalUnits));

            log.info("续重计算: 超出重量={}g, 续重单位={}g, 续重次数={}, 续重单价={}分",
                    additionalWeight, productPrice.getAdditionalUnit(), additionalUnits, productPrice.getAdditionalPrice());
            log.info("续重费用: {} × {} = {}分", productPrice.getAdditionalPrice(), additionalUnits, additionalFee);

            totalFee = totalFee.add(additionalFee);
        } else {
            log.info("重量{}g未超过首重{}g，无续重费用", weightGrams, productPrice.getFirstUnit());
        }

        log.info("首重续重总费用: {}分", totalFee);
        log.info("--- 首重续重计算完成 ---");
        return totalFee;
    }

    /**
     * 格式化金额（已废弃 - 现在直接返回分为单位）
     *
     * @deprecated 不再使用，金额直接以分为单位返回给前端
     */
    @Deprecated
    private String formatMoney(BigDecimal cents) {
        log.warn("formatMoney方法已废弃，应该直接返回分为单位的金额");
        if (cents == null) {
            return "0";
        }
        return cents.toString(); // 直接返回分为单位
    }

    /**
     * 构建优化的报价响应对象
     *
     * 构建策略：
     * 1. 复用对象创建，减少内存分配
     * 2. 按需设置字段，避免不必要的计算
     * 3. 统一处理国际化字段
     *
     * @param product 物流产品
     * @param priceRule 价格规则
     * @param feeDetail 费用详情
     * @return 完整的报价响应
     */
    private ShippingQuoteRespVO buildOptimizedQuoteResponse(LogisticsProductDO product,
                                                           LogisticsProductPriceDO priceRule,
                                                           ShippingQuoteRespVO.FeeDetail feeDetail) {
        return buildOptimizedQuoteResponse(product, priceRule, feeDetail, true, null);
    }

    /**
     * 构建优化的报价响应对象（完整版本）
     *
     * @param product 物流产品
     * @param priceRule 价格规则
     * @param feeDetail 费用详情
     * @param available 是否可用
     * @param unavailableReason 不可用原因
     * @return 完整的报价响应
     */
    private ShippingQuoteRespVO buildOptimizedQuoteResponse(LogisticsProductDO product,
                                                           LogisticsProductPriceDO priceRule,
                                                           ShippingQuoteRespVO.FeeDetail feeDetail,
                                                           boolean available,
                                                           String unavailableReason) {
        ShippingQuoteRespVO quote = new ShippingQuoteRespVO();

        // 基础产品信息
        setBasicProductInfo(quote, product);

        // 价格相关信息
        setPriceRelatedInfo(quote, priceRule);

        // 费用和可用性信息
        quote.setFeeDetail(feeDetail);
        quote.setAvailable(available);
        quote.setUnavailableReason(unavailableReason);

        // 限制和时效信息
        setRestrictionsAndTimeliness(quote, product, priceRule);

        return quote;
    }

    /**
     * 设置基础产品信息
     */
    private void setBasicProductInfo(ShippingQuoteRespVO quote, LogisticsProductDO product) {
        quote.setId(product.getId().toString());
        quote.setName(product.getNameZh()); // 使用中文名称
        quote.setIconUrl(product.getIconUrl());
        quote.setFeatures(product.getFeaturesZh()); // 使用中文特色描述
        quote.setSort(product.getSort());
        quote.setLineTips(new ArrayList<>()); // 暂时为空
    }

    /**
     * 设置价格相关信息
     */
    private void setPriceRelatedInfo(ShippingQuoteRespVO quote, LogisticsProductPriceDO priceRule) {
        if (priceRule == null) return;

        quote.setTransitTime(priceRule.getTransitTime());
        quote.setTariffRate(priceRule.getTariffRate() != null ? priceRule.getTariffRate() : BigDecimal.ZERO);
        quote.setPrepayTariff(priceRule.getPrepayTariff() != null ? priceRule.getPrepayTariff() : false);
    }

    /**
     * 设置限制和时效信息
     */
    private void setRestrictionsAndTimeliness(ShippingQuoteRespVO quote,
                                             LogisticsProductDO product,
                                             LogisticsProductPriceDO priceRule) {
        // 设置限制信息
        quote.setRestrictions(buildOptimizedRestrictions(product, priceRule));

        // 设置申报相关信息
        setDeclarationInfo(quote, product);

        // 设置时效信息
        if (priceRule != null && StrUtil.isNotBlank(priceRule.getTimelinessInfo())) {
            quote.setLogisticsTimeliness(parseTimelinessInfo(priceRule.getTimelinessInfo()));
        }
    }

    /**
     * 设置申报相关信息
     */
    private void setDeclarationInfo(ShippingQuoteRespVO quote, LogisticsProductDO product) {
        quote.setMinDeclareValue(product.getMinDeclareValue() != null ? product.getMinDeclareValue().intValue() : null);
        quote.setMaxDeclareValue(product.getMaxDeclareValue() != null ? product.getMaxDeclareValue().intValue() : null);
        quote.setDefaultDeclareType(product.getDefaultDeclareType());
        quote.setDeclarePerKg(product.getDeclarePerKg() != null ? product.getDeclarePerKg().intValue() : null);
        quote.setDeclareRatio(product.getDeclareRatio() != null ? product.getDeclareRatio().toString() : null);
        quote.setIossEnabled(product.getIossEnabled() != null ? product.getIossEnabled() : false);
        quote.setFreeInsure(product.getFreeInsure() != null ? product.getFreeInsure() : false);
        quote.setTaxInclude(product.getTaxInclude() != null ? product.getTaxInclude() : false);
    }

    /**
     * 构建优化的限制信息
     */
    private ShippingQuoteRespVO.Restrictions buildOptimizedRestrictions(LogisticsProductDO product,
                                                                        LogisticsProductPriceDO priceRule) {
        ShippingQuoteRespVO.Restrictions restrictions = new ShippingQuoteRespVO.Restrictions();

        if (priceRule != null) {
            restrictions.setMinWeight(priceRule.getMinWeight());
            restrictions.setMaxWeight(priceRule.getMaxWeight());
        }

        // 尺寸限制描述（简化处理）
        restrictions.setDimensionRestriction("请参考具体产品限制");

        // 体积重规则描述
        restrictions.setVolumeWeightRule(product.getVolumeWeightRuleZh());

        // 直接设置分类限制原始JSON，前端自行解析
        restrictions.setCategoryRestrictions(product.getCategoryRestrictions());

        log.debug("构建限制信息: 重量=[{}-{}]g, 分类限制配置={}",
                restrictions.getMinWeight(), restrictions.getMaxWeight(),
                StrUtil.isBlank(product.getCategoryRestrictions()) ? "无" : "已设置");

        return restrictions;
    }

    /**
     * 构建报价响应对象（旧版本，保持兼容性）
     */
    private ShippingQuoteRespVO buildQuoteResponse(LogisticsProductDO product,
                                                  LogisticsProductPriceDO productPrice,
                                                  ShippingQuoteRespVO.FeeDetail feeDetail,
                                                  boolean available,
                                                  String unavailableReason) {
        ShippingQuoteRespVO quote = new ShippingQuoteRespVO();
        
        // 基础信息
        quote.setId(product.getId().toString());
        quote.setName(product.getNameZh()); // 使用中文名称
        quote.setIconUrl(product.getIconUrl());
        quote.setFeatures(product.getFeaturesZh()); // 使用中文特色描述
        quote.setTransitTime(productPrice.getTransitTime());
        quote.setTaxInclude(product.getTaxInclude());
        quote.setLineTips(new ArrayList<>()); // 暂时为空
        
        quote.setFeeDetail(feeDetail);
        quote.setRestrictions(buildRestrictions(product, productPrice));
        
        quote.setAvailable(available);
        quote.setUnavailableReason(unavailableReason);
        quote.setSort(product.getSort());
        
        // 申报相关信息
        quote.setMinDeclareValue(product.getMinDeclareValue() != null ? product.getMinDeclareValue().intValue() : null);
        quote.setMaxDeclareValue(product.getMaxDeclareValue() != null ? product.getMaxDeclareValue().intValue() : null);
        quote.setDefaultDeclareType(product.getDefaultDeclareType());
        quote.setDeclarePerKg(product.getDeclarePerKg() != null ? product.getDeclarePerKg().intValue() : null);
        quote.setDeclareRatio(product.getDeclareRatio() != null ? product.getDeclareRatio().toString() : null);
        
        quote.setIossEnabled(product.getIossEnabled() != null ? product.getIossEnabled() : false);
        quote.setFreeInsure(product.getFreeInsure() != null ? product.getFreeInsure() : false);
        quote.setTariffRate(productPrice != null ? productPrice.getTariffRate() : BigDecimal.ZERO);
        quote.setPrepayTariff(productPrice != null && productPrice.getPrepayTariff() != null ? productPrice.getPrepayTariff() : false);
        
        // 时效信息
        if (productPrice != null && StrUtil.isNotBlank(productPrice.getTimelinessInfo())) {
            quote.setLogisticsTimeliness(parseTimelinessInfo(productPrice.getTimelinessInfo()));
        }
        
        return quote;
    }

    /**
     * 构建限制信息
     */
    private ShippingQuoteRespVO.Restrictions buildRestrictions(LogisticsProductDO product, 
                                                              LogisticsProductPriceDO productPrice) {
        ShippingQuoteRespVO.Restrictions restrictions = new ShippingQuoteRespVO.Restrictions();
        
        if (productPrice != null) {
            restrictions.setMinWeight(productPrice.getMinWeight());
            restrictions.setMaxWeight(productPrice.getMaxWeight());
        }
        
        // 尺寸限制描述（简化处理）
        restrictions.setDimensionRestriction("请参考具体产品限制");
        
        // 体积重规则描述
        restrictions.setVolumeWeightRule(product.getVolumeWeightRuleZh());
        
        // 直接设置分类限制原始JSON，前端自行解析
        restrictions.setCategoryRestrictions(product.getCategoryRestrictions());
        
        return restrictions;
    }

    /**
     * 解析时效信息
     */
    private ShippingQuoteRespVO.LogisticsTimeliness parseTimelinessInfo(String timelinessInfoJson) {
        try {
            JSONObject json = JSONUtil.parseObj(timelinessInfoJson);
            ShippingQuoteRespVO.LogisticsTimeliness timeliness = new ShippingQuoteRespVO.LogisticsTimeliness();
            
            if (json.containsKey("deliveryRate")) {
                timeliness.setDeliveryRate(json.getBigDecimal("deliveryRate"));
            }
            
            if (json.containsKey("timelinessInfos")) {
                List<ShippingQuoteRespVO.TimelinessInfo> timelinessInfos = json.getBeanList("timelinessInfos", ShippingQuoteRespVO.TimelinessInfo.class);
                timeliness.setTimelinessInfos(timelinessInfos);
            }
            
            return timeliness;
        } catch (Exception e) {
            log.warn("解析时效信息失败: {}", timelinessInfoJson, e);
            return null;
        }
    }

    @Override
    public List<AppShippingQuoteRespVO> getAppShippingQuotes(AppShippingQuoteReqVO reqVO) {
        log.info("=== 开始用户端运费查询 ===");
        log.info("用户端查询参数: 国家={}, 重量={}g, 长宽高=[{},{},{}], 分类={}",
                reqVO.getCountryCode(), reqVO.getWeight(),
                reqVO.getLength(), reqVO.getWidth(), reqVO.getHeight(),
                reqVO.getCategoryIds());

        try {
            // 转换为内部请求对象
            ShippingCalculationReqBO internalReqVO = BeanUtils.toBean(reqVO, ShippingCalculationReqBO.class);
            log.info("参数转换完成，调用内部查询方法");

            // 调用内部方法获取报价（BO版本）
            List<ShippingQuoteRespBO> internalQuotes = getShippingQuotes(internalReqVO);
            log.info("内部查询完成，获得{}个报价", internalQuotes.size());

            // 转换为用户端响应对象（BO转VO）
            List<AppShippingQuoteRespVO> appQuotes = convertBOToAppResponse(internalQuotes);

            log.info("=== 用户端运费查询完成 ===");
            log.info("最终返回{}个方案给用户端", appQuotes.size());
            return appQuotes;

        } catch (Exception e) {
            log.error("用户端运费查询失败: 国家={}, 重量={}g", reqVO.getCountryCode(), reqVO.getWeight(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LogisticsPlanBO> getEnableShippingPlans(ShippingCalculationReqBO reqVO) {
        log.info("=== 开始获取可用的物流方案 ===");
        log.info("查询参数: 国家={}, 重量={}g, 邮编={}", reqVO.getCountryCode(), reqVO.getWeight(), reqVO.getPostCode());

        try {
            // 1. 获取运费报价列表（使用BO版本）
            List<ShippingQuoteRespBO> quotes = getShippingQuotes(reqVO);
            log.info("获取到{}个运费报价", quotes.size());

            if (CollUtil.isEmpty(quotes)) {
                log.warn("国家{}暂无可用的物流方案", reqVO.getCountryCode());
                return new ArrayList<>();
            }

            // 2. 转换为LogisticsPlanBO列表
            List<LogisticsPlanBO> plans = new ArrayList<>();
            for (ShippingQuoteRespBO quote : quotes) {
                LogisticsPlanBO plan = convertToLogisticsPlanFromBO(quote);
                if (plan != null) {
                    plans.add(plan);
                }
            }

            // 3. 按排序字段和价格排序
            plans.sort((p1, p2) -> {
                // 首先按可用性排序，可用的在前
                if (!p1.getAvailable().equals(p2.getAvailable())) {
                    return p2.getAvailable().compareTo(p1.getAvailable());
                }
                // 然后按排序字段排序
                if (p1.getSort() != null && p2.getSort() != null) {
                    int sortCompare = p1.getSort().compareTo(p2.getSort());
                    if (sortCompare != 0) {
                        return sortCompare;
                    }
                }
                // 最后按价格排序，价格低的在前
                if (p1.getTotalFee() != null && p2.getTotalFee() != null) {
                    return p1.getTotalFee().compareTo(p2.getTotalFee());
                }
                return 0;
            });

            log.info("=== 物流方案获取完成 ===");
            log.info("总共返回{}个方案，其中可用{}个",
                    plans.size(),
                    plans.stream().mapToInt(p -> p.getAvailable() ? 1 : 0).sum());

            return plans;

        } catch (Exception e) {
            log.error("获取物流方案失败: 国家={}, 重量={}g", reqVO.getCountryCode(), reqVO.getWeight(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 将ShippingQuoteRespBO转换为LogisticsPlanBO
     *
     * 注意：这里不需要进行类型转换，因为BO中的费用字段已经是Integer类型
     *
     * @param quote 运费报价BO
     * @return 物流方案BO
     */
    private LogisticsPlanBO convertToLogisticsPlanFromBO(ShippingQuoteRespBO quote) {
        if (quote == null) {
            return null;
        }

        try {
            LogisticsPlanBO plan = new LogisticsPlanBO();

            // 基础信息
            plan.setProductId(quote.getProductId());
            plan.setPriceId(quote.getPriceId());
            plan.setName(quote.getName());
            plan.setIconUrl(quote.getIconUrl());
            plan.setFeatures(quote.getFeatures());
            plan.setTransitTime(quote.getTransitTime());
            plan.setTaxInclude(quote.getTaxInclude());
            plan.setAvailable(quote.getAvailable());
            plan.setUnavailableReason(quote.getUnavailableReason());
            plan.setSort(quote.getSort());
            plan.setFreeInsure(quote.getFreeInsure());

            // 费用信息 - 直接使用Integer，无需转换
            if (quote.getFeeDetail() != null) {
                ShippingQuoteRespBO.FeeDetail feeDetail = quote.getFeeDetail();
                plan.setChargeableWeight(feeDetail.getChargeableWeight());
                plan.setTotalFee(feeDetail.getTotal());
                plan.setFreight(feeDetail.getFreight());
                plan.setOperationFee(feeDetail.getOperationFee());
                plan.setServiceFee(feeDetail.getServiceFee());
                plan.setCustomsFee(feeDetail.getCustomsFee());
                plan.setFuelFee(feeDetail.getFuelFee());
                plan.setAirSurcharge(feeDetail.getAirSurcharge());
                plan.setAdditionalFee(feeDetail.getAdditionalFee());

                // 基础运费设为总费用
                plan.setBasePrice(plan.getTotalFee());
            }

            // 限制信息
            if (quote.getRestrictions() != null) {
                ShippingQuoteRespBO.Restrictions restrictions = quote.getRestrictions();
                plan.setMinWeight(restrictions.getMinWeight());
                plan.setMaxWeight(restrictions.getMaxWeight());
            }

            return plan;

        } catch (Exception e) {
            log.error("转换物流方案失败: productId={}, quoteName={}", quote.getProductId(), quote.getName(), e);
            return null;
        }
    }



    /**
     * 转换为用户端响应对象
     */
    private List<AppShippingQuoteRespVO> convertToAppResponse(List<ShippingQuoteRespVO> internalQuotes) {
        List<AppShippingQuoteRespVO> appQuotes = new ArrayList<>();

        for (ShippingQuoteRespVO internalQuote : internalQuotes) {
            AppShippingQuoteRespVO appQuote = new AppShippingQuoteRespVO();

            // 复制基础字段
            BeanUtils.copyProperties(internalQuote, appQuote);

            // 转换费用详情
            if (internalQuote.getFeeDetail() != null) {
                AppShippingQuoteRespVO.FeeDetail appFeeDetail = new AppShippingQuoteRespVO.FeeDetail();
                BeanUtils.copyProperties(internalQuote.getFeeDetail(), appFeeDetail);
                appQuote.setFeeDetail(appFeeDetail);
            }

            // 转换限制信息
            if (internalQuote.getRestrictions() != null) {
                AppShippingQuoteRespVO.Restrictions appRestrictions = new AppShippingQuoteRespVO.Restrictions();
                BeanUtils.copyProperties(internalQuote.getRestrictions(), appRestrictions);

                // 直接复制原始JSON配置
                appRestrictions.setCategoryRestrictions(internalQuote.getRestrictions().getCategoryRestrictions());

                appQuote.setRestrictions(appRestrictions);
            }

            // 转换时效信息
            if (internalQuote.getLogisticsTimeliness() != null) {
                AppShippingQuoteRespVO.LogisticsTimeliness appTimeliness = new AppShippingQuoteRespVO.LogisticsTimeliness();
                BeanUtils.copyProperties(internalQuote.getLogisticsTimeliness(), appTimeliness);

                // 转换时效详情
                if (internalQuote.getLogisticsTimeliness().getTimelinessInfos() != null) {
                    List<AppShippingQuoteRespVO.TimelinessInfo> appTimelinessInfos = new ArrayList<>();
                    for (ShippingQuoteRespVO.TimelinessInfo internalTimelinessInfo : internalQuote.getLogisticsTimeliness().getTimelinessInfos()) {
                        AppShippingQuoteRespVO.TimelinessInfo appTimelinessInfo = new AppShippingQuoteRespVO.TimelinessInfo();
                        BeanUtils.copyProperties(internalTimelinessInfo, appTimelinessInfo);
                        appTimelinessInfos.add(appTimelinessInfo);
                    }
                    appTimeliness.setTimelinessInfos(appTimelinessInfos);
                }

                appQuote.setLogisticsTimeliness(appTimeliness);
            }

            appQuotes.add(appQuote);
        }

        return appQuotes;
    }

    /**
     * 基于价格规则计算运费报价（BO版本）
     *
     * @param priceRule 价格规则
     * @param product 物流产品信息
     * @param reqVO 查询请求
     * @return 运费报价BO，如果不可用则返回null
     */
    private ShippingQuoteRespBO calculateQuoteForPriceRuleBO(LogisticsProductPriceDO priceRule,
                                                            LogisticsProductDO product,
                                                            ShippingCalculationReqBO reqVO) {
        log.info("--- 开始计算产品运费（BO版本） ---");
        log.info("产品: id={}, name={}", product != null ? product.getId() : "null",
                product != null ? product.getNameZh() : "null");

        if (product == null) {
            log.warn("物流产品不存在: productId={}", priceRule.getProductId());
            return null;
        }

        try {
            // 1. 检查重量限制
            String weightCheckResult = checkWeightLimits(priceRule, reqVO.getWeight());
            if (weightCheckResult != null) {
                log.warn("重量检查失败: {}", weightCheckResult);
                return createUnavailableQuoteBO(product, priceRule, weightCheckResult);
            }

            // 2. 检查分类限制
            CategoryRestrictionUtil.CategoryCheckResult categoryCheckResult =
                CategoryRestrictionUtil.checkCategoryRestrictions(product.getCategoryRestrictions(), reqVO.getCategoryIds());
            if (!categoryCheckResult.isPassed()) {
                log.warn("分类限制检查失败: {}", categoryCheckResult.getErrorMessage());
                return createUnavailableQuoteBO(product, priceRule, categoryCheckResult.getErrorMessage());
            }

            // 3. 计算体积重和计费重量
            VolumeWeightResult volumeResult = calculateVolumeWeight(reqVO);
            Integer chargeableWeight = Math.max(reqVO.getWeight(), volumeResult.getVolumeWeight());

            // 4. 计算运费详情
            ShippingQuoteRespBO.FeeDetail feeDetail = calculateFeeDetailBO(product, priceRule, reqVO, volumeResult, chargeableWeight);

            // 5. 构建完整的报价响应
            ShippingQuoteRespBO result = buildQuoteResponseBO(product, priceRule, feeDetail);

            log.info("--- 产品运费计算完成（BO版本） ---");
            return result;

        } catch (Exception e) {
            log.error("计算运费失败: productId={}, priceId={}", priceRule.getProductId(), priceRule.getId(), e);
            return null;
        }
    }

    /**
     * 创建不可用的运费报价BO
     */
    private ShippingQuoteRespBO createUnavailableQuoteBO(LogisticsProductDO product,
                                                        LogisticsProductPriceDO priceRule,
                                                        String reason) {
        ShippingQuoteRespBO quote = new ShippingQuoteRespBO();
        quote.setProductId(product.getId());
        quote.setPriceId(priceRule.getId());
        quote.setName(product.getNameZh());
        quote.setIconUrl(product.getIconUrl());
        quote.setFeatures(product.getFeaturesZh());
        quote.setAvailable(false);
        quote.setUnavailableReason(reason);
        quote.setSort(product.getSort());
        return quote;
    }

    /**
     * 计算费用详情BO
     */
    private ShippingQuoteRespBO.FeeDetail calculateFeeDetailBO(LogisticsProductDO product,
                                                              LogisticsProductPriceDO priceRule,
                                                              ShippingCalculationReqBO reqVO,
                                                              VolumeWeightResult volumeResult,
                                                              Integer chargeableWeight) {
        ShippingQuoteRespBO.FeeDetail feeDetail = new ShippingQuoteRespBO.FeeDetail();

        // 设置基础信息
        feeDetail.setWeight(reqVO.getWeight());
        feeDetail.setLength(reqVO.getLength());
        feeDetail.setWidth(reqVO.getWidth());
        feeDetail.setHeight(reqVO.getHeight());
        feeDetail.setVolumeWeight(volumeResult.getVolumeWeight());
        feeDetail.setChargeableWeight(chargeableWeight);
        feeDetail.setNeedVolumeCal(volumeResult.isNeedVolumeCal());
        feeDetail.setVolumeBase(volumeResult.getVolumeBase());

        // 计算基础运费和挂号费
        FeeCalculationResult feeResult = calculateBaseFeeAndRegistration(priceRule, chargeableWeight);

        // 设置运费信息（直接使用Integer，不需要toString）
        feeDetail.setFreight(feeResult.getBaseFee() != null ? feeResult.getBaseFee().intValue() : 0);
        feeDetail.setAdditionalFee(feeResult.getRegistrationFee() != null ? feeResult.getRegistrationFee().intValue() : 0);

        // 设置首重续重信息
        if (priceRule.getFirstUnit() != null) {
            feeDetail.setWeightFirst(priceRule.getFirstUnit());
            if (priceRule.getFirstPrice() != null) {
                feeDetail.setFeeFirst(priceRule.getFirstPrice());
            }
        }

        if (priceRule.getAdditionalUnit() != null) {
            feeDetail.setWeightContinue(priceRule.getAdditionalUnit());
            if (priceRule.getAdditionalPrice() != null) {
                feeDetail.setFeeContinue(priceRule.getAdditionalPrice());
            }
        }

        // 设置附加费用（直接使用Integer）
        feeDetail.setOperationFee(priceRule.getOperationFee() != null ? priceRule.getOperationFee() : 0);
        feeDetail.setServiceFee(priceRule.getServiceFee() != null ? priceRule.getServiceFee() : 0);
        feeDetail.setCustomsFee(priceRule.getCustomsFee() != null ? priceRule.getCustomsFee() : 0);
        feeDetail.setFuelFee(0);
        feeDetail.setAirSurcharge(0);

        // 计算总费用
        Integer total = feeDetail.getFreight() + feeDetail.getOperationFee() + feeDetail.getServiceFee() +
                       feeDetail.getCustomsFee() + feeDetail.getAdditionalFee();
        feeDetail.setTotal(total);
        feeDetail.setCurrentTotal(total);

        return feeDetail;
    }

    /**
     * 构建运费报价响应BO
     */
    private ShippingQuoteRespBO buildQuoteResponseBO(LogisticsProductDO product,
                                                    LogisticsProductPriceDO priceRule,
                                                    ShippingQuoteRespBO.FeeDetail feeDetail) {
        ShippingQuoteRespBO quote = new ShippingQuoteRespBO();

        // 基础产品信息
        quote.setProductId(product.getId());
        quote.setPriceId(priceRule.getId());
        quote.setName(product.getNameZh());
        quote.setIconUrl(product.getIconUrl());
        quote.setFeatures(product.getFeaturesZh());
        quote.setTransitTime(priceRule.getTransitTime());
        quote.setTaxInclude(product.getTaxInclude());
        quote.setLineTips(new ArrayList<>());

        // 费用和可用性信息
        quote.setFeeDetail(feeDetail);
        quote.setAvailable(true);
        quote.setSort(product.getSort());

        // 申报相关信息
        quote.setMinDeclareValue(product.getMinDeclareValue() != null ? product.getMinDeclareValue().intValue() : null);
        quote.setMaxDeclareValue(product.getMaxDeclareValue() != null ? product.getMaxDeclareValue().intValue() : null);
        quote.setDefaultDeclareType(product.getDefaultDeclareType());
        quote.setDeclarePerKg(product.getDeclarePerKg() != null ? product.getDeclarePerKg().intValue() : null);
        quote.setDeclareRatio(product.getDeclareRatio());
        quote.setIossEnabled(product.getIossEnabled());
        quote.setFreeInsure(product.getFreeInsure());

        // 限制信息
        quote.setRestrictions(buildRestrictionsBO(product, priceRule));

        return quote;
    }

    /**
     * 构建限制信息BO
     */
    private ShippingQuoteRespBO.Restrictions buildRestrictionsBO(LogisticsProductDO product,
                                                                LogisticsProductPriceDO priceRule) {
        ShippingQuoteRespBO.Restrictions restrictions = new ShippingQuoteRespBO.Restrictions();
        restrictions.setMinWeight(priceRule.getMinWeight());
        restrictions.setMaxWeight(priceRule.getMaxWeight());
        restrictions.setDimensionRestriction("请参考具体产品限制");
        restrictions.setVolumeWeightRule(product.getVolumeWeightRuleZh());
        restrictions.setCategoryRestrictions(product.getCategoryRestrictions());
        return restrictions;
    }

    /**
     * 将BO转换为用户端响应VO
     *
     * 在这里进行Integer到String的转换，用于前端显示
     */
    private List<AppShippingQuoteRespVO> convertBOToAppResponse(List<ShippingQuoteRespBO> boQuotes) {
        List<AppShippingQuoteRespVO> appQuotes = new ArrayList<>();

        for (ShippingQuoteRespBO boQuote : boQuotes) {
            AppShippingQuoteRespVO appQuote = new AppShippingQuoteRespVO();

            // 复制基础字段
            appQuote.setId(boQuote.getProductId() != null ? boQuote.getProductId().toString() : null);
            appQuote.setName(boQuote.getName());
            appQuote.setIconUrl(boQuote.getIconUrl());
            appQuote.setFeatures(boQuote.getFeatures());
            appQuote.setTransitTime(boQuote.getTransitTime());
            appQuote.setTaxInclude(boQuote.getTaxInclude());
            appQuote.setLineTips(boQuote.getLineTips());
            appQuote.setAvailable(boQuote.getAvailable());
            appQuote.setUnavailableReason(boQuote.getUnavailableReason());
            appQuote.setSort(boQuote.getSort());
            appQuote.setMinDeclareValue(boQuote.getMinDeclareValue());
            appQuote.setMaxDeclareValue(boQuote.getMaxDeclareValue());
            appQuote.setDefaultDeclareType(boQuote.getDefaultDeclareType());
            appQuote.setDeclarePerKg(boQuote.getDeclarePerKg());
            appQuote.setDeclareRatio(boQuote.getDeclareRatio() != null ? boQuote.getDeclareRatio().toString() : null);
            appQuote.setIossEnabled(boQuote.getIossEnabled());
            appQuote.setFreeInsure(boQuote.getFreeInsure());

            // 转换费用详情（Integer转String）
            if (boQuote.getFeeDetail() != null) {
                AppShippingQuoteRespVO.FeeDetail appFeeDetail = new AppShippingQuoteRespVO.FeeDetail();
                ShippingQuoteRespBO.FeeDetail boFeeDetail = boQuote.getFeeDetail();

                appFeeDetail.setWeight(boFeeDetail.getWeight());
                appFeeDetail.setLength(boFeeDetail.getLength());
                appFeeDetail.setWidth(boFeeDetail.getWidth());
                appFeeDetail.setHeight(boFeeDetail.getHeight());
                appFeeDetail.setVolumeWeight(boFeeDetail.getVolumeWeight());
                appFeeDetail.setChargeableWeight(boFeeDetail.getChargeableWeight());
                appFeeDetail.setNeedVolumeCal(boFeeDetail.getNeedVolumeCal());
                appFeeDetail.setVolumeBase(boFeeDetail.getVolumeBase());
                appFeeDetail.setWeightFirst(boFeeDetail.getWeightFirst());
                appFeeDetail.setWeightContinue(boFeeDetail.getWeightContinue());
                appFeeDetail.setCurrency("CNY");

                // 费用转换：Integer（分）转String（分）
                appFeeDetail.setTotal(boFeeDetail.getTotal() != null ? boFeeDetail.getTotal().toString() : "0");
                appFeeDetail.setFreight(boFeeDetail.getFreight() != null ? boFeeDetail.getFreight().toString() : "0");
                appFeeDetail.setCustomsFee(boFeeDetail.getCustomsFee() != null ? boFeeDetail.getCustomsFee().toString() : "0");
                appFeeDetail.setFuelFee(boFeeDetail.getFuelFee() != null ? boFeeDetail.getFuelFee().toString() : "0");
                appFeeDetail.setAirSurcharge(boFeeDetail.getAirSurcharge() != null ? boFeeDetail.getAirSurcharge().toString() : "0");
                appFeeDetail.setOperationFee(boFeeDetail.getOperationFee() != null ? boFeeDetail.getOperationFee().toString() : "0");
                appFeeDetail.setServiceFee(boFeeDetail.getServiceFee() != null ? boFeeDetail.getServiceFee().toString() : "0");
                appFeeDetail.setFeeFirst(boFeeDetail.getFeeFirst() != null ? boFeeDetail.getFeeFirst().toString() : "0");
                appFeeDetail.setFeeContinue(boFeeDetail.getFeeContinue() != null ? boFeeDetail.getFeeContinue().toString() : "0");
                appFeeDetail.setAdditionalFee(boFeeDetail.getAdditionalFee() != null ? boFeeDetail.getAdditionalFee().toString() : "0");
                appFeeDetail.setDiscount(boFeeDetail.getDiscount() != null ? boFeeDetail.getDiscount().toString() : "0");
                appFeeDetail.setOriginalTotal(boFeeDetail.getOriginalTotal() != null ? boFeeDetail.getOriginalTotal().toString() : null);
                appFeeDetail.setCurrentTotal(boFeeDetail.getCurrentTotal() != null ? boFeeDetail.getCurrentTotal().toString() : appFeeDetail.getTotal());

                appQuote.setFeeDetail(appFeeDetail);
            }

            // 转换限制信息
            if (boQuote.getRestrictions() != null) {
                AppShippingQuoteRespVO.Restrictions appRestrictions = new AppShippingQuoteRespVO.Restrictions();
                ShippingQuoteRespBO.Restrictions boRestrictions = boQuote.getRestrictions();

                appRestrictions.setMinWeight(boRestrictions.getMinWeight());
                appRestrictions.setMaxWeight(boRestrictions.getMaxWeight());
                appRestrictions.setDimensionRestriction(boRestrictions.getDimensionRestriction());
                appRestrictions.setVolumeWeightRule(boRestrictions.getVolumeWeightRule());
                appRestrictions.setCategoryRestrictions(boRestrictions.getCategoryRestrictions());

                appQuote.setRestrictions(appRestrictions);
            }

            appQuotes.add(appQuote);
        }

        return appQuotes;
    }

}
