package cn.iocoder.yudao.module.agent.service.transfer;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.agent.controller.admin.transfer.vo.*;
import cn.iocoder.yudao.module.agent.controller.app.transfer.vo.AppAgentTransferPageReqVO;
import cn.iocoder.yudao.module.agent.controller.app.transfer.vo.AppAgentTransferSaveReqVO;
import cn.iocoder.yudao.module.agent.dal.dataobject.transfer.AgentTransferDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.transferitem.AgentTransferItemDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 代购转运单 Service 接口
 *
 * <AUTHOR>
 */
public interface AgentTransferService {

    //-------------------------------ADMIN--------------------------------

    /**
     * 创建代购转运单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransfer(@Valid AgentTransferSaveReqVO createReqVO);

    /**
     * 更新代购转运单
     *
     * @param updateReqVO 更新信息
     */
    void updateTransfer(@Valid AgentTransferSaveReqVO updateReqVO);

    /**
     * 删除代购转运单
     *
     * @param id 编号
     */
    void deleteTransfer(Long id);

    /**
     * 获得代购转运单
     *
     * @param id 编号
     * @return 代购转运单
     */
    AgentTransferDO getTransfer(Long id);

    /**
     * 获得代购转运单分页
     *
     * @param pageReqVO 分页查询
     * @return 代购转运单分页
     */
    PageResult<AgentTransferDO> getTransferPage(AgentTransferPageReqVO pageReqVO);

    // ==================== 子表（代购转运单明细） ====================

    /**
     * 获得代购转运单明细列表
     *
     * @param transferId 转运单编号
     * @return 代购转运单明细列表
     */
    List<AgentTransferItemDO> getTransferItemListByTransferId(Long transferId);

    //--------------------------APP---------------------------

    /**
     * 创建代购转运单 用户app
     * @param userId
     * @param createReqVO
     * @return
     */
    Long createTransferApp(Long userId,AppAgentTransferSaveReqVO createReqVO);

    /**
     * 更新代购转运单 用户app
     * @param updateReqVO
     */
    void updateTransferApp(AppAgentTransferSaveReqVO updateReqVO);

    /**
     * 获得代购转运单 用户app
     * @param loginUserId
     * @param id
     * @return
     */
    PageResult<AgentTransferDO> getTransferPageApp(Long userId, AppAgentTransferPageReqVO pageReqVO);
}