package cn.iocoder.yudao.module.agent.controller.app.parcel.vo;

import cn.iocoder.yudao.module.agent.controller.app.base.property.AppProductPropertyValueDetailRespVO;
import cn.iocoder.yudao.module.agent.controller.app.base.server.AgentServeBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "用户 APP - 代购包裹 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppAgentParcelRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "28896")
    private Long id;

    @Schema(description = "流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3180")
    private Long userId;

    @Schema(description = "用户备注")
    private String userRemark;

    @Schema(description = "包裹状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("agent_parcel_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "支付订单编号", example = "1746")
    @ExcelProperty("支付订单编号")
    private Long payOrderId;

    @Schema(description = "是否支付", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Boolean payStatus;

    @Schema(description = "支付成功的支付渠道")
    private String payChannelCode;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "包裹完成时间")
    private LocalDateTime finishTime;

    @Schema(description = "包裹取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "订单原价", requiredMode = Schema.RequiredMode.REQUIRED, example = "12778")
    private Integer totalPrice;

    @Schema(description = "订单优惠", requiredMode = Schema.RequiredMode.REQUIRED, example = "442")
    private Integer discountPrice;

    @Schema(description = "运费金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "17163")
    private Integer deliveryPrice;

    @Schema(description = "保险金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "11327")
    private Integer insurancePrice;

    @Schema(description = "服务金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "8890")
    private Integer servicePrice;

    @Schema(description = "平台服务费", requiredMode = Schema.RequiredMode.REQUIRED, example = "11327")
    private Integer platformPrice;

    @Schema(description = "订单调价", requiredMode = Schema.RequiredMode.REQUIRED, example = "24474")
    private Integer adjustPrice;

    @Schema(description = "优惠劵减免金额")
    private Integer couponPrice;

    @Schema(description = "使用的积分")
    private Integer usePoint;

    @Schema(description = "积分抵扣的金额")
    private Integer pointPrice;

    @Schema(description = "应付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "13447")
    private Integer payPrice;

    @Schema(description = "商品重量")
    private Integer weight;

    @Schema(description = "商品体积")
    private BigDecimal volume;

    @Schema(description = "包装重量")
    private Integer packingWeight;

    @Schema(description = "配送类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer deliveryType;

    @Schema(description = "物流方案编号", example = "27258")
    private Long transportPlanId;

    @Schema(description = "物流方案名称", example = "云途极速")
    private Long transportPlanName;

    @Schema(description = "物流公司编号", example = "14569")
    private Long transportCompanyId;

    @Schema(description = "物流公司", example = "顺丰")
    private Long transportCompanyName;

    @Schema(description = "物流单号")
    private String transportNo;

    @Schema(description = "海关申报价值")
    private Integer declareValue;

    @Schema(description = "海关申报内容")
    private String declareContent;

    @Schema(description = "海关清关代码")
    private String clearanceCode;

    @Schema(description = "发货时间")
    private LocalDateTime deliveryTime;

    @Schema(description = "收货时间")
    private LocalDateTime receiveTime;

    @Schema(description = "收件人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String receiverName;

    @Schema(description = "收件人手机", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    private String receiverMobile;

    @Schema(description = "收件人地区编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "110000")
    private Integer receiverAreaId;

    @Schema(description = "收件人地区名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "上海 上海市 普陀区")
    private String receiverAreaName;

    @Schema(description = "收件人详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "中关村大街 1 号")
    private String receiverDetailAddress;

    @Schema(description = "收件人手机区号", requiredMode = Schema.RequiredMode.REQUIRED, example = "86")
    private String receiverPhoneCode;

    @Schema(description = "收件人邮编", requiredMode = Schema.RequiredMode.REQUIRED, example = "221700")
    private String receiverPostCode;

    @Schema(description = "售后状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer refundStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "包裹项")
    List<Item> items;

    @Schema(description = "保险服务")
    List<AgentServeBaseVO> insuranceServices;

    @Schema(description = "免费服务")
    List<AgentServeBaseVO> freeServices;

    @Schema(description = "收费服务")
    List<AgentServeBaseVO> chargeServices;


    @Schema(description = "包裹项")
    @Data
    public static class Item {

        // ========== SPU 信息 ==========

        @Schema(description = "SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
        private Long spuId;
        @Schema(description = "SPU 名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "Apple iPhone 12")
        private String spuName;

        // ========== SKU 信息 ==========

        @Schema(description = "SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
        private Integer skuId;
        @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/1.png")
        private String picUrl;

        @Schema(description = "购买数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer count;

        @Schema(description = "重量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer weight;

        @Schema(description = "体积", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private BigDecimal volume;

        List<AppProductPropertyValueDetailRespVO> properties;

    }



}