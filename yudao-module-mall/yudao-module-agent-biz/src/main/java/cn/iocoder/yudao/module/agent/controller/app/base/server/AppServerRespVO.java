package cn.iocoder.yudao.module.agent.controller.app.base.server;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @program: ruoyi-vue-pro
 * @description: 订单项服务Respon VO
 * @author: Ding<PERSON><PERSON><PERSON>
 * @create: 2025-05-09 10:04
 **/
@Data
public class AppServerRespVO {
    @Schema(description = "服务编号", example = "2048")
    private Long id;

    @Schema(description = "服务名称", example = "测试服务")
    private String name;

    @Schema(description = "服务价格", example = "100")
    private Integer price;
}
