package cn.iocoder.yudao.module.agent.controller.admin.logisticsZone;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.Parameters;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.admin.logisticsZone.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsZone.LogisticsZoneDO;
import cn.iocoder.yudao.module.agent.service.logisticsZone.LogisticsZoneService;

@Tag(name = "管理后台 - 代购物流国家分区")
@RestController
@RequestMapping("/agent/logistics-zone")
@Validated
public class LogisticsZoneController {

    @Resource
    private LogisticsZoneService logisticsZoneService;

    @PostMapping("/create")
    @Operation(summary = "创建代购物流国家分区")
    @PreAuthorize("@ss.hasPermission('agent:logistics-zone:create')")
    public CommonResult<Long> createLogisticsZone(@Valid @RequestBody LogisticsZoneSaveReqVO createReqVO) {
        return success(logisticsZoneService.createLogisticsZone(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购物流国家分区")
    @PreAuthorize("@ss.hasPermission('agent:logistics-zone:update')")
    public CommonResult<Boolean> updateLogisticsZone(@Valid @RequestBody LogisticsZoneSaveReqVO updateReqVO) {
        logisticsZoneService.updateLogisticsZone(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购物流国家分区")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:logistics-zone:delete')")
    public CommonResult<Boolean> deleteLogisticsZone(@RequestParam("id") Long id) {
        logisticsZoneService.deleteLogisticsZone(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购物流国家分区")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:logistics-zone:query')")
    public CommonResult<LogisticsZoneRespVO> getLogisticsZone(@RequestParam("id") Long id) {
        LogisticsZoneDO logisticsZone = logisticsZoneService.getLogisticsZone(id);
        return success(BeanUtils.toBean(logisticsZone, LogisticsZoneRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购物流国家分区分页")
    @PreAuthorize("@ss.hasPermission('agent:logistics-zone:query')")
    public CommonResult<PageResult<LogisticsZoneRespVO>> getLogisticsZonePage(@Valid LogisticsZonePageReqVO pageReqVO) {
        PageResult<LogisticsZoneDO> pageResult = logisticsZoneService.getLogisticsZonePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LogisticsZoneRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购物流国家分区 Excel")
    @PreAuthorize("@ss.hasPermission('agent:logistics-zone:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLogisticsZoneExcel(@Valid LogisticsZonePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LogisticsZoneDO> list = logisticsZoneService.getLogisticsZonePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购物流国家分区.xls", "数据", LogisticsZoneRespVO.class,
                        BeanUtils.toBean(list, LogisticsZoneRespVO.class));
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得代购物流国家分区导入模板")
    public void getLogisticsZoneImportTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<LogisticsZoneImportExcelVO> list = Arrays.asList(
                LogisticsZoneImportExcelVO.builder()
                        .countryCode("US").zoneCode("FORBIDDEN").zoneName("禁止配送区域")
                        .stateProvince("Alaska").city(null).district(null)
                        .specialAreaType(null).fullAreaName("Alaska")
                        .postalCodes("[\"99500-99999\"]").restrictionType("FORBIDDEN")
                        .feeFormula(null).remark("阿拉斯加州禁止配送").sort(1).status(1).build(),
                LogisticsZoneImportExcelVO.builder()
                        .countryCode("US").zoneCode("REMOTE").zoneName("偏远地区")
                        .stateProvince("Hawaii").city("Honolulu").district(null)
                        .specialAreaType("ISLAND").fullAreaName("Hawaii Honolulu")
                        .postalCodes("[\"96700-96899\"]").restrictionType("REMOTE_FEE")
                        .feeFormula("8*weight_kg,min:50").remark("夏威夷偏远费").sort(2).status(1).build(),
                LogisticsZoneImportExcelVO.builder()
                        .countryCode("AU").zoneCode("ZONE1").zoneName("澳洲1区")
                        .stateProvince("New South Wales").city("Sydney").district(null)
                        .specialAreaType(null).fullAreaName("New South Wales Sydney")
                        .postalCodes("[\"2000-2999\"]").restrictionType("NORMAL")
                        .feeFormula(null).remark("悉尼主要区域").sort(1).status(1).build()
        );

        // 输出
        ExcelUtils.write(response, "代购物流国家分区导入模板.xls", "分区列表", LogisticsZoneImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入代购物流国家分区")
    @Parameters({
            @Parameter(name = "productId", description = "产品编号", required = true),
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('agent:logistics-zone:import')")
    public CommonResult<LogisticsZoneImportRespVO> importLogisticsZoneExcel(
            @RequestParam("productId") Long productId,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<LogisticsZoneImportExcelVO> list = ExcelUtils.read(file, LogisticsZoneImportExcelVO.class);
        return success(logisticsZoneService.importLogisticsZoneList(productId, list, updateSupport));
    }



}