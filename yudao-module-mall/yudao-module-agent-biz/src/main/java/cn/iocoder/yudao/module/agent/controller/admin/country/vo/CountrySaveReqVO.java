package cn.iocoder.yudao.module.agent.controller.admin.country.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 代购服务国家新增/修改 Request VO")
@Data
public class CountrySaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16640")
    private Long id;

    @Schema(description = "国家编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "国家编码不能为空")
    private String code;

    @Schema(description = "图标地址", example = "https://www.iocoder.cn")
    private String flagUrl;

    @Schema(description = "中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "中文名称不能为空")
    private String nameZh;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "英文名称不能为空")
    private String nameEn;

    @Schema(description = "法语名称")
    private String nameFr;

    @Schema(description = "德语名称")
    private String nameDe;

    @Schema(description = "西班牙语名称")
    private String nameEs;

    @Schema(description = "阿拉伯语名称")
    private String nameAr;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "热门", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "热门不能为空")
    private Boolean hot;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "开启状态不能为空")
    private Integer status;

}