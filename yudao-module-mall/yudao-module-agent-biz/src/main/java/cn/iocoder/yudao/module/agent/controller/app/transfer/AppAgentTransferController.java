package cn.iocoder.yudao.module.agent.controller.app.transfer;

import cn.iocoder.yudao.module.agent.convert.transfer.AgentTransferConvert;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

import cn.iocoder.yudao.module.agent.controller.app.transfer.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.transfer.AgentTransferDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.transferitem.AgentTransferItemDO;
import cn.iocoder.yudao.module.agent.service.transfer.AgentTransferService;

@Tag(name = "用户 APP - 代购转运单")
@RestController
@RequestMapping("/agent/transfer")
@Validated
public class AppAgentTransferController {

    @Resource
    private AgentTransferService transferService;

    @PostMapping("/create")
    @Operation(summary = "创建代购转运单")
    public CommonResult<Long> createTransfer(@Valid @RequestBody AppAgentTransferSaveReqVO createReqVO) {
        return success(transferService.createTransferApp(getLoginUserId(),createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购转运单")
    public CommonResult<Boolean> updateTransfer(@Valid @RequestBody AppAgentTransferSaveReqVO updateReqVO) {
        transferService.updateTransferApp(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购转运单")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteTransfer(@RequestParam("id") Long id) {
        transferService.deleteTransfer(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购转运单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppAgentTransferRespVO> getTransfer(@RequestParam("id") Long id) {
        AgentTransferDO transfer = transferService.getTransfer(id);
        List<AgentTransferItemDO> items = transferService.getTransferItemListByTransferId(id);

        List<AppAgentTransferRespVO.Item> respVOItems = AgentTransferConvert.INSTANCE.convertList(items);
        AppAgentTransferRespVO respVO = BeanUtils.toBean(transfer, AppAgentTransferRespVO.class);
        respVO.setItems(respVOItems);
        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购转运单分页")
    public CommonResult<PageResult<AppAgentTransferRespVO>> getTransferPage(@Valid AppAgentTransferPageReqVO pageReqVO) {
        PageResult<AgentTransferDO> pageResult = transferService.getTransferPageApp(getLoginUserId(), pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppAgentTransferRespVO.class));
    }


    // ==================== 子表（代购转运单明细） ====================

    @GetMapping("/transfer-item/list-by-transfer-id")
    @Operation(summary = "获得代购转运单明细列表")
    @Parameter(name = "transferId", description = "转运单编号")
    public CommonResult<List<AgentTransferItemDO>> getTransferItemListByTransferId(@RequestParam("transferId") Long transferId) {
        return success(transferService.getTransferItemListByTransferId(transferId));
    }

}