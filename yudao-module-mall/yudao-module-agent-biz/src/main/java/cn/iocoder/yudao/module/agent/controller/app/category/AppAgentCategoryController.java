package cn.iocoder.yudao.module.agent.controller.app.category;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLang;

import cn.iocoder.yudao.module.agent.controller.app.category.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.category.AgentCategoryDO;
import cn.iocoder.yudao.module.agent.service.category.AgentCategoryService;

@Tag(name = "用户 APP - 代购商品分类")
@RestController
@RequestMapping("/agent/category")
@Validated
public class AppAgentCategoryController {

    @Resource
    private AgentCategoryService categoryService;

    @GetMapping("/get")
    @Operation(summary = "获得代购商品分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<AppAgentCategoryRespVO> getCategory(@RequestParam("id") Long id) {
        AgentCategoryDO category = categoryService.getCategory(id);
        return success(BeanUtils.toBean(category, AppAgentCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得代购商品分类列表")
    @PermitAll
    public CommonResult<List<AppAgentCategoryRespVO>> getCategoryList() {

        List<AppAgentCategoryRespVO> list = categoryService.getCategoryList( getCurrentLang() );
        return success(list);
    }



}