package cn.iocoder.yudao.module.agent.controller.admin.warehouse;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.admin.warehouse.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.warehouse.AgentWarehouseDO;
import cn.iocoder.yudao.module.agent.service.warehouse.AgentWarehouseService;

@Tag(name = "管理后台 - 代购平台仓库")
@RestController
@RequestMapping("/agent/warehouse")
@Validated
public class AgentWarehouseController {

    @Resource
    private AgentWarehouseService warehouseService;

    @PostMapping("/create")
    @Operation(summary = "创建代购平台仓库")
    @PreAuthorize("@ss.hasPermission('agent:warehouse:create')")
    public CommonResult<Long> createWarehouse(@Valid @RequestBody AgentWarehouseSaveReqVO createReqVO) {
        return success(warehouseService.createWarehouse(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购平台仓库")
    @PreAuthorize("@ss.hasPermission('agent:warehouse:update')")
    public CommonResult<Boolean> updateWarehouse(@Valid @RequestBody AgentWarehouseSaveReqVO updateReqVO) {
        warehouseService.updateWarehouse(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购平台仓库")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:warehouse:delete')")
    public CommonResult<Boolean> deleteWarehouse(@RequestParam("id") Long id) {
        warehouseService.deleteWarehouse(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购平台仓库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:warehouse:query')")
    public CommonResult<AgentWarehouseRespVO> getWarehouse(@RequestParam("id") Long id) {
        AgentWarehouseDO warehouse = warehouseService.getWarehouse(id);
        return success(BeanUtils.toBean(warehouse, AgentWarehouseRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购平台仓库分页")
    @PreAuthorize("@ss.hasPermission('agent:warehouse:query')")
    public CommonResult<PageResult<AgentWarehouseRespVO>> getWarehousePage(@Valid AgentWarehousePageReqVO pageReqVO) {
        PageResult<AgentWarehouseDO> pageResult = warehouseService.getWarehousePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgentWarehouseRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购平台仓库 Excel")
    @PreAuthorize("@ss.hasPermission('agent:warehouse:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWarehouseExcel(@Valid AgentWarehousePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentWarehouseDO> list = warehouseService.getWarehousePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购平台仓库.xls", "数据", AgentWarehouseRespVO.class,
                        BeanUtils.toBean(list, AgentWarehouseRespVO.class));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获取代购仓库精简信息列表", description = "主要用于前端的下拉选项")
    public CommonResult<List<AgentWarehouseSimpleRespVO>> getSimpleWarehouseList() {
        List<AgentWarehouseDO> list = warehouseService.getWarehouseListByStatus(CommonStatusEnum.ENABLE.getStatus());
        return success(BeanUtils.toBean(list,AgentWarehouseSimpleRespVO.class));
    }

}