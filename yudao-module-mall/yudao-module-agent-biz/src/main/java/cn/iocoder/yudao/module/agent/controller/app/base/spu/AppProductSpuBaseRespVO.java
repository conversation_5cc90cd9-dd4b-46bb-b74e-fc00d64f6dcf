package cn.iocoder.yudao.module.agent.controller.app.base.spu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 商品 SPU 基础 Response VO
 *
 * <AUTHOR>
 */
@Data
public class AppProductSpuBaseRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "商品 SPU 名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String name;

    @Schema(description = "商品主图地址", example = "https://www.iocoder.cn/xx.png")
    private String picUrl;

    @Schema(description = "商品分类编号", example = "1")
    private Long categoryId;

    @Schema(description = "商品价格,单位使用：分", example = "199")
    private Integer price;

    @Schema(description = "店铺名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "胖东来旗舰店")
    private String shopName;

    @Schema(description = "来源", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String source;

    @Schema(description = "运费,代购商品的国内运费", requiredMode = Schema.RequiredMode.REQUIRED, example = "600")
    private Integer freight;



}
