package cn.iocoder.yudao.module.agent.framework.parcel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.Duration;

/**
 * @program: ruoyi-vue-pro
 * @description: 包裹订单的配置项
 * @author: DingXiao
 * @create: 2025-05-21 11:36
 **/
@ConfigurationProperties(prefix = "yudao.agent.parcel")
@Data
@Validated
public class AgentParcelProperties {

    private static final String PAY_APP_KEY_DEFAULT = "parcel";

    /**
     * 支付应用标识
     *
     * 在 pay 模块的 [支付管理 -> 应用信息] 里添加
     */
    @NotEmpty(message = "Pay 应用标识不能为空")
    private String payAppKey = PAY_APP_KEY_DEFAULT;

    /**
     * 支付超时时间
     */
    @NotNull(message = "支付超时时间不能为空")
    private Duration payExpireTime;

    /**
     * 收货超时时间
     */
    @NotNull(message = "收货超时时间不能为空")
    private Duration receiveExpireTime;

    /**
     * 评论超时时间
     */
    @NotNull(message = "评论超时时间不能为空")
    private Duration commentExpireTime;
}
