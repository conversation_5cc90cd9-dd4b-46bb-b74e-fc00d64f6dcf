package cn.iocoder.yudao.module.agent.controller.admin.serve.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 代购服务项目分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AgentServePageReqVO extends PageParam {

    @Schema(description = "服务编码")
    private String code;

    @Schema(description = "类型;保险，免费服务，收费服务", example = "2")
    private Integer type;

    @Schema(description = "中文名称")
    private String nameZh;

    @Schema(description = "开启状态", example = "2")
    private Integer status;

}