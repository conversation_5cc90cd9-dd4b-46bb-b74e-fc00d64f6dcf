package cn.iocoder.yudao.module.agent.service.shipping;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsZone.LogisticsZoneDO;
import cn.iocoder.yudao.module.agent.enums.LogisticsRestrictionTypeEnum;
import cn.iocoder.yudao.module.agent.service.logisticsZone.LogisticsZoneService;
import cn.iocoder.yudao.module.agent.util.ShippingFeeCalculator;
import cn.iocoder.yudao.module.agent.util.AreaMatchingUtil;
import cn.iocoder.yudao.module.agent.util.SizeRestrictionUtil;
import cn.iocoder.yudao.module.agent.util.TieredPriceCalculator;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductPriceDO;
import cn.iocoder.yudao.module.agent.service.logisticsProduct.LogisticsProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 运费计算服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShippingCalculationServiceImpl implements ShippingCalculationService {

    @Resource
    private LogisticsZoneService logisticsZoneService;

    @Resource
    private LogisticsProductService logisticsProductService;

    @Override
    public ShippingCalculationResult calculateShipping(String countryCode, String postalCode,
                                                     Long productId, BigDecimal weight,
                                                     BigDecimal length, BigDecimal width, BigDecimal height) {

        // 1. 检查尺寸限制
        SizeRestrictionUtil.SizeCheckResult sizeCheckResult = checkSizeRestriction(countryCode, productId, length, width, height);
        if (!sizeCheckResult.isPassed()) {
            return new ShippingCalculationResult(false, BigDecimal.ZERO, BigDecimal.ZERO,
                                               BigDecimal.ZERO, "SIZE_RESTRICTION",
                                               sizeCheckResult.getErrorMessage(),
                                               null, sizeCheckResult);
        }

        // 2. 检查配送限制
        ShippingRestrictionResult restrictionResult = checkShippingRestriction(countryCode, postalCode, productId);

        if (!restrictionResult.isCanShip()) {
            return new ShippingCalculationResult(false, BigDecimal.ZERO, BigDecimal.ZERO,
                                               BigDecimal.ZERO, restrictionResult.getRestrictionType(),
                                               restrictionResult.getRestrictionMessage(),
                                               restrictionResult.getMatchedZone(), sizeCheckResult);
        }

        // 3. 计算基础运费和各项费用
        ShippingFeeResult feeResult = calculateShippingFee(countryCode, productId, weight, length, width, height);

        // 4. 计算偏远地区费
        BigDecimal remoteFee = BigDecimal.ZERO;
        LogisticsZoneDO matchedZone = restrictionResult.getMatchedZone();

        if (matchedZone != null && LogisticsRestrictionTypeEnum.REMOTE_FEE.getCode().equals(matchedZone.getRestrictionType())) {
            remoteFee = ShippingFeeCalculator.calculateRemoteFee(matchedZone.getFeeFormula(), weight);
        }

        // 5. 计算总费用
        BigDecimal totalFee = feeResult.getBaseFee()
                .add(feeResult.getRegistrationFee())
                .add(feeResult.getOperationFee())
                .add(feeResult.getServiceFee())
                .add(feeResult.getCustomsFee())
                .add(remoteFee);

        return new ShippingCalculationResult(true, feeResult.getBaseFee(), remoteFee, totalFee,
                                           restrictionResult.getRestrictionType(),
                                           "正常配送", matchedZone, sizeCheckResult);
    }

    @Override
    public ShippingRestrictionResult checkShippingRestriction(String countryCode, String postalCode, Long productId) {

        // 获取所有候选分区
        List<LogisticsZoneDO> zones = logisticsZoneService.getZoneListByCountryAndProduct(countryCode, productId);

        // 使用智能匹配找到最佳分区 (这里简化处理，实际应该传入完整地址信息)
        LogisticsZoneDO matchedZone = AreaMatchingUtil.findBestMatch(zones, postalCode, null, null, null);

        if (matchedZone == null) {
            // 没有找到匹配的分区，默认允许配送
            return new ShippingRestrictionResult(true, LogisticsRestrictionTypeEnum.NORMAL.getCode(),
                                               "正常配送区域", null);
        }

        String restrictionType = matchedZone.getRestrictionType();
        
        if (LogisticsRestrictionTypeEnum.FORBIDDEN.getCode().equals(restrictionType)) {
            // 禁止配送
            String message = StrUtil.isNotBlank(matchedZone.getRemark()) ? 
                           matchedZone.getRemark() : "该地区禁止配送";
            return new ShippingRestrictionResult(false, restrictionType, message, matchedZone);
        }
        
        if (LogisticsRestrictionTypeEnum.REMOTE_FEE.getCode().equals(restrictionType)) {
            // 偏远地区，需要额外费用
            String message = StrUtil.isNotBlank(matchedZone.getRemark()) ? 
                           matchedZone.getRemark() : "偏远地区，需要额外费用";
            return new ShippingRestrictionResult(true, restrictionType, message, matchedZone);
        }

        // 正常配送
        return new ShippingRestrictionResult(true, LogisticsRestrictionTypeEnum.NORMAL.getCode(),
                                           "正常配送区域", matchedZone);
    }

    @Override
    public ShippingRestrictionResult checkShippingRestrictionWithAddress(String countryCode, String postalCode,
                                                                        String stateProvince, String city, String district,
                                                                        Long productId) {

        // 获取所有候选分区
        List<LogisticsZoneDO> zones = logisticsZoneService.getZoneListByCountryAndProduct(countryCode, productId);

        // 使用智能匹配找到最佳分区
        LogisticsZoneDO matchedZone = AreaMatchingUtil.findBestMatch(zones, postalCode, stateProvince, city, district);

        if (matchedZone == null) {
            // 没有找到匹配的分区，默认允许配送
            return new ShippingRestrictionResult(true, LogisticsRestrictionTypeEnum.NORMAL.getCode(),
                                               "正常配送区域", null);
        }

        String restrictionType = matchedZone.getRestrictionType();

        if (LogisticsRestrictionTypeEnum.FORBIDDEN.getCode().equals(restrictionType)) {
            // 禁止配送
            String message = StrUtil.isNotBlank(matchedZone.getRemark()) ?
                           matchedZone.getRemark() : "该地区禁止配送";
            return new ShippingRestrictionResult(false, restrictionType, message, matchedZone);
        }

        if (LogisticsRestrictionTypeEnum.REMOTE_FEE.getCode().equals(restrictionType)) {
            // 偏远地区，需要额外费用
            String message = StrUtil.isNotBlank(matchedZone.getRemark()) ?
                           matchedZone.getRemark() : "偏远地区，需要额外费用";
            return new ShippingRestrictionResult(true, restrictionType, message, matchedZone);
        }

        // 正常配送
        return new ShippingRestrictionResult(true, LogisticsRestrictionTypeEnum.NORMAL.getCode(),
                                           "正常配送区域", matchedZone);
    }

    /**
     * 检查尺寸限制
     *
     * @param countryCode 国家编码
     * @param productId 物流产品ID
     * @param length 长度(cm)
     * @param width 宽度(cm)
     * @param height 高度(cm)
     * @return 尺寸检查结果
     */
    private SizeRestrictionUtil.SizeCheckResult checkSizeRestriction(String countryCode, Long productId,
                                                                   BigDecimal length, BigDecimal width, BigDecimal height) {
        try {
            // 获取该国家的物流产品价格信息（包含尺寸限制）
            LogisticsProductPriceDO productPrice = logisticsProductService.getLogisticsProductPriceByCountry(productId, countryCode);
            if (productPrice == null) {
                return new SizeRestrictionUtil.SizeCheckResult(false, "该国家暂不支持此物流产品", "COUNTRY_NOT_SUPPORTED");
            }

            // 检查尺寸限制
            return SizeRestrictionUtil.checkSize(length, width, height, productPrice.getSizeRestrictions());
        } catch (Exception e) {
            log.error("检查尺寸限制失败: countryCode={}, productId={}, length={}, width={}, height={}",
                     countryCode, productId, length, width, height, e);
            return new SizeRestrictionUtil.SizeCheckResult(false, "尺寸检查失败", "SIZE_CHECK_ERROR");
        }
    }

    /**
     * 运费计算结果
     */
    public static class ShippingFeeResult {
        private BigDecimal baseFee = BigDecimal.ZERO;
        private BigDecimal registrationFee = BigDecimal.ZERO;
        private BigDecimal operationFee = BigDecimal.ZERO;
        private BigDecimal serviceFee = BigDecimal.ZERO;
        private BigDecimal customsFee = BigDecimal.ZERO;
        private boolean useTieredRegistrationFee = false;

        // Getters and Setters
        public BigDecimal getBaseFee() { return baseFee; }
        public void setBaseFee(BigDecimal baseFee) { this.baseFee = baseFee; }
        public BigDecimal getRegistrationFee() { return registrationFee; }
        public void setRegistrationFee(BigDecimal registrationFee) { this.registrationFee = registrationFee; }
        public BigDecimal getOperationFee() { return operationFee; }
        public void setOperationFee(BigDecimal operationFee) { this.operationFee = operationFee; }
        public BigDecimal getServiceFee() { return serviceFee; }
        public void setServiceFee(BigDecimal serviceFee) { this.serviceFee = serviceFee; }
        public BigDecimal getCustomsFee() { return customsFee; }
        public void setCustomsFee(BigDecimal customsFee) { this.customsFee = customsFee; }
        public boolean isUseTieredRegistrationFee() { return useTieredRegistrationFee; }
        public void setUseTieredRegistrationFee(boolean useTieredRegistrationFee) { this.useTieredRegistrationFee = useTieredRegistrationFee; }
    }

    /**
     * 计算运费和各项费用
     */
    private ShippingFeeResult calculateShippingFee(String countryCode, Long productId, BigDecimal weight,
                                                  BigDecimal length, BigDecimal width, BigDecimal height) {
        ShippingFeeResult result = new ShippingFeeResult();

        try {
            // 获取价格规则
            LogisticsProductPriceDO productPrice = logisticsProductService.getLogisticsProductPriceByCountry(productId, countryCode);
            if (productPrice == null) {
                log.warn("未找到产品价格规则: productId={}, countryCode={}", productId, countryCode);
                return result;
            }

            // 转换重量为克
            Integer weightGrams = weight.multiply(new BigDecimal(1000)).intValue();

            // 根据价格类型计算基础运费
            if ("TIERED".equals(productPrice.getPriceType()) &&
                cn.hutool.core.util.StrUtil.isNotBlank(productPrice.getTieredPrices())) {

                // 使用阶梯价格计算
                TieredPriceCalculator.TieredPriceResult tieredResult =
                    TieredPriceCalculator.calculateTieredPrice(
                        productPrice.getTieredPrices(),
                        weightGrams,
                        productPrice.getRegistrationFee()
                    );

                result.setBaseFee(tieredResult.getBaseFee());
                result.setRegistrationFee(tieredResult.getRegistrationFee());
                result.setUseTieredRegistrationFee(tieredResult.isUseTieredRegistrationFee());

            } else {
                // 使用传统的首重续重计算
                result.setBaseFee(calculateIncrementalFee(productPrice, weightGrams));
                // 使用价格表中的挂号费
                if (productPrice.getRegistrationFee() != null) {
                    result.setRegistrationFee(new BigDecimal(productPrice.getRegistrationFee()));
                }
            }

            // 设置其他费用
            if (productPrice.getOperationFee() != null) {
                result.setOperationFee(new BigDecimal(productPrice.getOperationFee()));
            }
            if (productPrice.getServiceFee() != null) {
                result.setServiceFee(new BigDecimal(productPrice.getServiceFee()));
            }
            if (productPrice.getCustomsFee() != null) {
                result.setCustomsFee(new BigDecimal(productPrice.getCustomsFee()));
            }

            log.debug("运费计算完成: productId={}, countryCode={}, weight={}kg, baseFee={}, registrationFee={}, useTieredRegistrationFee={}",
                     productId, countryCode, weight, result.getBaseFee(), result.getRegistrationFee(), result.isUseTieredRegistrationFee());

        } catch (Exception e) {
            log.error("运费计算失败: productId={}, countryCode={}, weight={}", productId, countryCode, weight, e);
        }

        return result;
    }

    /**
     * 计算递增式运费（首重+续重）
     */
    private BigDecimal calculateIncrementalFee(LogisticsProductPriceDO productPrice, Integer weightGrams) {
        if (productPrice.getFirstUnit() == null || productPrice.getFirstPrice() == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalFee = new BigDecimal(productPrice.getFirstPrice());

        // 计算续重费用
        if (weightGrams > productPrice.getFirstUnit() &&
            productPrice.getAdditionalUnit() != null &&
            productPrice.getAdditionalPrice() != null) {

            int additionalWeight = weightGrams - productPrice.getFirstUnit();
            int additionalUnits = (int) Math.ceil((double) additionalWeight / productPrice.getAdditionalUnit());
            BigDecimal additionalFee = new BigDecimal(productPrice.getAdditionalPrice()).multiply(new BigDecimal(additionalUnits));
            totalFee = totalFee.add(additionalFee);
        }

        return totalFee;
    }
}
