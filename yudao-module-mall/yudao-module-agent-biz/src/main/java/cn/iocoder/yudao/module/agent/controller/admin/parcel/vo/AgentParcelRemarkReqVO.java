package cn.iocoder.yudao.module.agent.controller.admin.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @program: ruoyi-vue-pro
 * @description: 管理后台 - 包裹订单备注 Request VO
 * @author: DingXiao
 * @create: 2025-05-31 14:59
 **/
@Schema(description = "管理后台 - 包裹订单备注 Request VO")
@Data
public class AgentParcelRemarkReqVO {

    @Schema(description = "包裹订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单编号不能为空")
    private Long id;

    @Schema(description = "平台备注", example = "你猜一下")
    @NotEmpty(message = "订单备注不能为空")
    private String remark;
}
