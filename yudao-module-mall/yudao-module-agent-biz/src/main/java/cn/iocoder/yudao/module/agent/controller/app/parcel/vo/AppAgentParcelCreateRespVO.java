package cn.iocoder.yudao.module.agent.controller.app.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 APP - 代购包裹新增 Request VO")
@Data
public class AppAgentParcelCreateRespVO {

    @Schema(description = "包裹订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "支付订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long payOrderId;

}