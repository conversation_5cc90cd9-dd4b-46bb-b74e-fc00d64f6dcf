package cn.iocoder.yudao.module.agent.controller.app.serve.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 代购服务项目 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppAgentServeSimpleRespVO {

    @Schema(description = "服务编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4394")
    @ExcelProperty("服务编号")
    private Long id;

    @Schema(description = "服务编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("服务编码")
    private String code;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "类型", converter = DictConvert.class)
    @DictFormat("agent_serve_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer type;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "是否免费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "是否免费", converter = DictConvert.class)
    @DictFormat("infra_boolean_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Boolean free;

    @Schema(description = "收费金额，单位使用：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "4086")
    @ExcelProperty("收费金额，单位使用：分")
    private Integer price;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;


}