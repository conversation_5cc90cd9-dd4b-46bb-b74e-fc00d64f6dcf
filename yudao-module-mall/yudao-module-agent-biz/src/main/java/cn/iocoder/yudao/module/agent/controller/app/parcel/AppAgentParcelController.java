package cn.iocoder.yudao.module.agent.controller.app.parcel;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.agent.controller.app.parcel.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelItemDO;
import cn.iocoder.yudao.module.agent.service.parcel.AgentParcelQueryService;
import cn.iocoder.yudao.module.agent.service.parcel.AgentParcelService;
import cn.iocoder.yudao.module.agent.service.parcel.AgentParcelUpdateService;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayOrderNotifyReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 代购包裹")
@RestController
@RequestMapping("/agent/parcel")
@Validated
public class AppAgentParcelController {

    @Resource
    private AgentParcelService parcelService;

    @Resource
    private AgentParcelUpdateService parcelUpdateService;

    @Resource
    private AgentParcelQueryService parcelQueryService;

    @GetMapping("/settlement")
    @Operation(summary = "获得包裹结算信息")
    public CommonResult<AppAgentParcelSettlementRespVO> settlementOrder(@Valid AppAgentParcelSettlementReqVO settlementReqVO) {
        return success(parcelUpdateService.settlementOrder(getLoginUserId(), settlementReqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "创建代购包裹")
    public CommonResult<AppAgentParcelCreateRespVO> createParcel(@Valid @RequestBody AppAgentParcelCreateReqVO createReqVO) {
        AgentParcelDO  parcel = parcelUpdateService.createParcel(getLoginUserId(), createReqVO);
        return success(new AppAgentParcelCreateRespVO().setId(parcel.getId()).setPayOrderId(parcel.getPayOrderId()));
    }

    @PostMapping("/update-paid")
    @Operation(summary = "更新订单为已支付") // 由 pay-module 支付服务，进行回调，可见 PayNotifyJob
    @PermitAll  //ding 允许pay服务调用?
    public CommonResult<Boolean> updateOrderPaid(@RequestBody PayOrderNotifyReqDTO notifyReqDTO) {
        parcelUpdateService.updateOrderPaid(Long.valueOf(notifyReqDTO.getMerchantOrderId()),
                notifyReqDTO.getPayOrderId());
        return success(true);
    }

    //@PutMapping("/update")
    //@Operation(summary = "更新代购包裹")
    //public CommonResult<Boolean> updateParcel(@Valid @RequestBody AppAgentParcelSaveReqVO updateReqVO) {
    //    parcelService.updateParcel(updateReqVO);
    //    return success(true);
    //}

    @DeleteMapping("/cancel")
    @Operation(summary = "取消包裹订单")
    @Parameter(name = "id", description = "包裹订单编号")
    public CommonResult<Boolean> cancelParcel(@RequestParam("id") Long id) {
        parcelUpdateService.cancelParcelByMember(getLoginUserId(), id);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购包裹")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteParcel(@RequestParam("id") Long id) {
        parcelUpdateService.deleteParcel(getLoginUserId(),id);
        return success(true);
    }

    @GetMapping("/get-detail")
    @Operation(summary = "获得代购包裹")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppAgentParcelRespVO> getParcel(@RequestParam("id") Long id) {

        AppAgentParcelRespVO parcel =parcelQueryService.getParcelDetail(id);
        return success(BeanUtils.toBean(parcel, AppAgentParcelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购包裹分页")
    public CommonResult<PageResult<AppAgentParcelRespVO>> getParcelPage(@Valid AppAgentParcelPageReqVO pageReqVO) {

        return success(parcelService.getParcelPageApp(getLoginUserId() ,pageReqVO));
    }



    // ==================== 子表（代购包裹明细） ====================

    @GetMapping("/parcel-item/list-by-parcel-id")
    @Operation(summary = "获得代购包裹明细列表")
    @Parameter(name = "parcelId", description = "订单编号")
    public CommonResult<List<AgentParcelItemDO>> getParcelItemListByParcelId(@RequestParam("parcelId") Long parcelId) {
        return success(parcelService.getParcelItemListByParcelId(parcelId));
    }

}