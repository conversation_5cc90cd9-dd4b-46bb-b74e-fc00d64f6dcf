package cn.iocoder.yudao.module.agent.service.transfer;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.number.CurrencyUtils;
import cn.iocoder.yudao.module.agent.controller.app.transfer.vo.AppAgentTransferPageReqVO;
import cn.iocoder.yudao.module.agent.controller.app.transfer.vo.AppAgentTransferSaveReqVO;
import cn.iocoder.yudao.module.agent.convert.transfer.AgentTransferConvert;
import cn.iocoder.yudao.module.agent.dal.redis.no.AgentNoRedisDAO;
import cn.iocoder.yudao.module.agent.enums.transfer.AgentTransferStatusEnum;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import cn.iocoder.yudao.module.agent.controller.admin.transfer.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.transfer.AgentTransferDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.transferitem.AgentTransferItemDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.agent.dal.mysql.transfer.AgentTransferMapper;
import cn.iocoder.yudao.module.agent.dal.mysql.transferitem.AgentTransferItemMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getTerminal;
import static cn.iocoder.yudao.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代购转运单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AgentTransferServiceImpl implements AgentTransferService {

    @Resource
    private AgentTransferMapper transferMapper;
    @Resource
    private AgentTransferItemMapper transferItemMapper;
    @Resource
    private AgentNoRedisDAO agentNoRedisDAO;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTransfer(AgentTransferSaveReqVO createReqVO) {
        // 插入
        AgentTransferDO transfer = BeanUtils.toBean(createReqVO, AgentTransferDO.class);
        transferMapper.insert(transfer);

        // 插入子表
        createTransferItemList(transfer.getId(), createReqVO.getTransferItems());
        // 返回
        return transfer.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransfer(AgentTransferSaveReqVO updateReqVO) {
        // 校验存在
        validateTransferExists(updateReqVO.getId());
        // 更新
        AgentTransferDO updateObj = BeanUtils.toBean(updateReqVO, AgentTransferDO.class);
        transferMapper.updateById(updateObj);

        // 更新子表
        updateTransferItemList(updateReqVO.getId(), updateReqVO.getTransferItems());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTransfer(Long id) {
        // 校验存在
        validateTransferExists(id);
        // 删除
        transferMapper.deleteById(id);

        // 删除子表
        deleteTransferItemByTransferId(id);
    }

    private void validateTransferExists(Long id) {
        if (transferMapper.selectById(id) == null) {
            throw exception(TRANSFER_NOT_EXISTS);
        }
    }

    @Override
    public AgentTransferDO getTransfer(Long id) {
        return transferMapper.selectById(id);
    }

    @Override
    public PageResult<AgentTransferDO> getTransferPage(AgentTransferPageReqVO pageReqVO) {
        return transferMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（代购转运单明细） ====================

    @Override
    public List<AgentTransferItemDO> getTransferItemListByTransferId(Long transferId) {
        return transferItemMapper.selectListByTransferId(transferId);
    }

    private void createTransferItemList(Long transferId, List<AgentTransferItemDO> list) {
        list.forEach(o -> o.setTransferId(transferId));
        transferItemMapper.insertBatch(list);
    }

    private void updateTransferItemList(Long transferId, List<AgentTransferItemDO> list) {
        deleteTransferItemByTransferId(transferId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createTransferItemList(transferId, list);
    }

    private void deleteTransferItemByTransferId(Long transferId) {
        transferItemMapper.deleteByTransferId(transferId);
    }


    //-------------------------------APP-------------------------------


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTransferApp(Long userId,AppAgentTransferSaveReqVO createReqVO) {
        // 插入
        AgentTransferDO transfer = BeanUtils.toBean(createReqVO, AgentTransferDO.class);
        transfer.setUserId(userId);
        transfer.setNo(agentNoRedisDAO.generate(AgentNoRedisDAO.AGENT_TRANSFER_NO_PREFIX));
        transfer.setStatus(AgentTransferStatusEnum.UN_IN_STOCK.getStatus()); //默认未入库状态
        transfer.setUserIp(getClientIP());
        transfer.setTerminal(getTerminal());
        //数量合计
        transfer.setItemCount(CollectionUtils.getSumValue(createReqVO.getItems(), AppAgentTransferSaveReqVO.Item::getCount, Integer::sum));
        //金额合计
        transfer.setTotalPrice(CollectionUtils.getSumValue(
                createReqVO.getItems(),
                item -> item.getPrice() * item.getCount(),
                Integer::sum
        ));
        //重量合计
        transfer.setWeight(CollectionUtils.getSumValue(
                createReqVO.getItems(),
                item -> item.getWeight(),
                BigDecimal::add
        ));

        transferMapper.insert(transfer);
        // 插入子表
        List<AgentTransferItemDO> transferItems = AgentTransferConvert.INSTANCE.convertList(transfer,createReqVO);
        transferItemMapper.insertBatch(transferItems);
        //createTransferItemList(transfer.getId(), transferItems);
        // 返回
        return transfer.getId();
    }

    @Override
    public void updateTransferApp(AppAgentTransferSaveReqVO updateReqVO) {
        // 校验存在
        validateTransferExists(updateReqVO.getId());
        // 更新
        AgentTransferDO updateObj = BeanUtils.toBean(updateReqVO, AgentTransferDO.class);
        transferMapper.updateById(updateObj);

        // 更新子表
        List<AgentTransferItemDO> transferItems = BeanUtils.toBean(updateReqVO.getItems(), AgentTransferItemDO.class);
        updateTransferItemList(updateReqVO.getId(), transferItems);
    }

    @Override
    public PageResult<AgentTransferDO> getTransferPageApp(Long userId, AppAgentTransferPageReqVO pageReqVO) {
        return transferMapper.selectPageApp(pageReqVO,userId);
    }
}