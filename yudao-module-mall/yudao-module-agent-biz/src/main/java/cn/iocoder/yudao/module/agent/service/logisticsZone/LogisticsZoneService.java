package cn.iocoder.yudao.module.agent.service.logisticsZone;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.agent.controller.admin.logisticsZone.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsZone.LogisticsZoneDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 代购物流国家分区 Service 接口
 *
 * <AUTHOR>
 */
public interface LogisticsZoneService {

    /**
     * 创建代购物流国家分区
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLogisticsZone(@Valid LogisticsZoneSaveReqVO createReqVO);

    /**
     * 更新代购物流国家分区
     *
     * @param updateReqVO 更新信息
     */
    void updateLogisticsZone(@Valid LogisticsZoneSaveReqVO updateReqVO);

    /**
     * 删除代购物流国家分区
     *
     * @param id 编号
     */
    void deleteLogisticsZone(Long id);

    /**
     * 获得代购物流国家分区
     *
     * @param id 编号
     * @return 代购物流国家分区
     */
    LogisticsZoneDO getLogisticsZone(Long id);

    /**
     * 获得代购物流国家分区分页
     *
     * @param pageReqVO 分页查询
     * @return 代购物流国家分区分页
     */
    PageResult<LogisticsZoneDO> getLogisticsZonePage(LogisticsZonePageReqVO pageReqVO);

    /**
     * 根据邮编查找匹配的物流分区
     *
     * @param countryCode 国家编码
     * @param productId 产品ID
     * @param postalCode 邮编
     * @return 匹配的分区，如果没有匹配则返回null
     */
    LogisticsZoneDO findZoneByPostalCode(String countryCode, Long productId, String postalCode);

    /**
     * 获取指定国家和产品的所有分区列表
     *
     * @param countryCode 国家编码
     * @param productId 产品ID
     * @return 分区列表
     */
    List<LogisticsZoneDO> getZoneListByCountryAndProduct(String countryCode, Long productId);

    /**
     * 导入代购物流国家分区
     *
     * @param productId 产品编号
     * @param importZones 导入的分区列表
     * @param isUpdateSupport 是否支持更新，默认为 false
     * @return 导入结果
     */
    LogisticsZoneImportRespVO importLogisticsZoneList(Long productId, List<LogisticsZoneImportExcelVO> importZones, boolean isUpdateSupport);

}