package cn.iocoder.yudao.module.agent.controller.app.stock.vo;

import cn.iocoder.yudao.module.agent.controller.app.base.property.AppProductPropertyValueDetailRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "用户 APP - 代购仓库 Response VO")
@Data
public class AppAgentStockRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "5362")
    private Long id;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30558")
    private Long userId;

    @Schema(description = "spu编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10218")
    private Long spuId;

    @Schema(description = "商品 SPU 名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String spuName;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31305")
    private Long skuId;

    @Schema(description = "商品图片", example = "https://www.iocoder.cn")
    private String picUrl;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "11391")
    private Integer count;

    @Schema(description = "商品重量")
    private Integer weight;

    @Schema(description = "商品体积")
    private Double volume;

    @Schema(description = "预包装重量")
    private Integer prePackageWeight;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;

    @Schema(description = "是否可合并", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean canCombine;

    @Schema(description = "入库日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime inTime;

    @Schema(description = "到期日期")
    private LocalDateTime expiredTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "属性列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<AppProductPropertyValueDetailRespVO> properties;

}