package cn.iocoder.yudao.module.agent.service.warehouse;

import cn.iocoder.yudao.module.agent.dal.redis.RedisKeyConstants;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.agent.controller.admin.warehouse.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.warehouse.AgentWarehouseDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.agent.dal.mysql.warehouse.AgentWarehouseMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代购平台仓库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AgentWarehouseServiceImpl implements AgentWarehouseService {

    @Resource
    private AgentWarehouseMapper warehouseMapper;

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.AGENT_WAREHOUSE_LIST, allEntries = true) // allEntries 清空此缓存区所有缓存
    public Long createWarehouse(AgentWarehouseSaveReqVO createReqVO) {
        // 插入
        AgentWarehouseDO warehouse = BeanUtils.toBean(createReqVO, AgentWarehouseDO.class);
        warehouseMapper.insert(warehouse);
        // 返回
        return warehouse.getId();
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.AGENT_WAREHOUSE_LIST, allEntries = true) // allEntries 清空此缓存区所有缓存
    public void updateWarehouse(AgentWarehouseSaveReqVO updateReqVO) {
        // 校验存在
        validateWarehouseExists(updateReqVO.getId());
        // 更新
        AgentWarehouseDO updateObj = BeanUtils.toBean(updateReqVO, AgentWarehouseDO.class);
        warehouseMapper.updateById(updateObj);
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.AGENT_WAREHOUSE_LIST, allEntries = true) // allEntries 清空此缓存区所有缓存
    public void deleteWarehouse(Long id) {
        // 校验存在
        validateWarehouseExists(id);
        // 删除
        warehouseMapper.deleteById(id);
    }

    private void validateWarehouseExists(Long id) {
        if (warehouseMapper.selectById(id) == null) {
            throw exception(WAREHOUSE_NOT_EXISTS);
        }
    }

    @Override
    public AgentWarehouseDO getWarehouse(Long id) {
        return warehouseMapper.selectById(id);
    }

    @Override
    public PageResult<AgentWarehouseDO> getWarehousePage(AgentWarehousePageReqVO pageReqVO) {
        return warehouseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AgentWarehouseDO> getWarehouseListByStatus(Integer status) {
        return warehouseMapper.selectListByStatus(status);
    }

    //----------------------------------APP-----------------------------------------
    @Override
    @Cacheable(cacheNames = RedisKeyConstants.AGENT_WAREHOUSE_LIST+"#432000",key = "'default'") //120小时缓存
    public List<AgentWarehouseDO> getWarehouseList() {
        return warehouseMapper.selectList();
    }


}