package cn.iocoder.yudao.module.agent.service.deliveryexpress;

import cn.iocoder.yudao.module.agent.dal.redis.RedisKeyConstants;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.agent.controller.admin.deliveryexpress.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.deliveryexpress.AgentDeliveryExpressDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.agent.dal.mysql.deliveryexpress.AgentDeliveryExpressMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代购快递公司 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AgentDeliveryExpressServiceImpl implements AgentDeliveryExpressService {

    @Resource
    private AgentDeliveryExpressMapper agentDeliveryExpressMapper;

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.AGENT_DELIVERY_EXPRESS_LIST, allEntries = true) // allEntries 清空此缓存区所有缓存
    public Long createDeliveryExpress(AgentDeliveryExpressSaveReqVO createReqVO) {
        // 插入
        AgentDeliveryExpressDO deliveryExpress = BeanUtils.toBean(createReqVO, AgentDeliveryExpressDO.class);
        agentDeliveryExpressMapper.insert(deliveryExpress);
        // 返回
        return deliveryExpress.getId();
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.AGENT_DELIVERY_EXPRESS_LIST, allEntries = true) // allEntries 清空此缓存区所有缓存
    public void updateDeliveryExpress(AgentDeliveryExpressSaveReqVO updateReqVO) {
        // 校验存在
        validateDeliveryExpressExists(updateReqVO.getId());
        // 更新
        AgentDeliveryExpressDO updateObj = BeanUtils.toBean(updateReqVO, AgentDeliveryExpressDO.class);
        agentDeliveryExpressMapper.updateById(updateObj);
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.AGENT_DELIVERY_EXPRESS_LIST, allEntries = true) // allEntries 清空此缓存区所有缓存
    public void deleteDeliveryExpress(Long id) {
        // 校验存在
        validateDeliveryExpressExists(id);
        // 删除
        agentDeliveryExpressMapper.deleteById(id);
    }

    private void validateDeliveryExpressExists(Long id) {
        if (agentDeliveryExpressMapper.selectById(id) == null) {
            throw exception(DELIVERY_EXPRESS_NOT_EXISTS);
        }
    }

    @Override
    public AgentDeliveryExpressDO getDeliveryExpress(Long id) {
        return agentDeliveryExpressMapper.selectById(id);
    }

    @Override
    public PageResult<AgentDeliveryExpressDO> getDeliveryExpressPage(AgentDeliveryExpressPageReqVO pageReqVO) {
        return agentDeliveryExpressMapper.selectPage(pageReqVO);
    }

    //--------------------------APP-----------------------------------------


    @Override
    @Cacheable(cacheNames = RedisKeyConstants.AGENT_DELIVERY_EXPRESS_LIST +"#432000",key = "'default'") //120小时缓存
    public List<AgentDeliveryExpressDO> getDeliveryExpressList() {
        return agentDeliveryExpressMapper.selectList();
    }
}