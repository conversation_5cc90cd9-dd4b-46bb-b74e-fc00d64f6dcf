package cn.iocoder.yudao.module.agent.controller.admin.deliveryexpress;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.admin.deliveryexpress.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.deliveryexpress.AgentDeliveryExpressDO;
import cn.iocoder.yudao.module.agent.service.deliveryexpress.AgentDeliveryExpressService;

@Tag(name = "管理后台 - 代购快递公司")
@RestController
@RequestMapping("/agent/delivery-express")
@Validated
public class AgentDeliveryExpressController {

    @Resource
    private AgentDeliveryExpressService deliveryExpressService;

    @PostMapping("/create")
    @Operation(summary = "创建代购快递公司")
    @PreAuthorize("@ss.hasPermission('agent:delivery-express:create')")
    public CommonResult<Long> createDeliveryExpress(@Valid @RequestBody AgentDeliveryExpressSaveReqVO createReqVO) {
        return success(deliveryExpressService.createDeliveryExpress(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购快递公司")
    @PreAuthorize("@ss.hasPermission('agent:delivery-express:update')")
    public CommonResult<Boolean> updateDeliveryExpress(@Valid @RequestBody AgentDeliveryExpressSaveReqVO updateReqVO) {
        deliveryExpressService.updateDeliveryExpress(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购快递公司")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:delivery-express:delete')")
    public CommonResult<Boolean> deleteDeliveryExpress(@RequestParam("id") Long id) {
        deliveryExpressService.deleteDeliveryExpress(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购快递公司")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:delivery-express:query')")
    public CommonResult<AgentDeliveryExpressRespVO> getDeliveryExpress(@RequestParam("id") Long id) {
        AgentDeliveryExpressDO deliveryExpress = deliveryExpressService.getDeliveryExpress(id);
        return success(BeanUtils.toBean(deliveryExpress, AgentDeliveryExpressRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购快递公司分页")
    @PreAuthorize("@ss.hasPermission('agent:delivery-express:query')")
    public CommonResult<PageResult<AgentDeliveryExpressRespVO>> getDeliveryExpressPage(@Valid AgentDeliveryExpressPageReqVO pageReqVO) {
        PageResult<AgentDeliveryExpressDO> pageResult = deliveryExpressService.getDeliveryExpressPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgentDeliveryExpressRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购快递公司 Excel")
    @PreAuthorize("@ss.hasPermission('agent:delivery-express:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDeliveryExpressExcel(@Valid AgentDeliveryExpressPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentDeliveryExpressDO> list = deliveryExpressService.getDeliveryExpressPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购快递公司.xls", "数据", AgentDeliveryExpressRespVO.class,
                        BeanUtils.toBean(list, AgentDeliveryExpressRespVO.class));
    }

}