package cn.iocoder.yudao.module.agent.controller.app.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "用户 APP - 代购商品分类 Response VO")
@Data
public class AppAgentCategoryRespVO {

    private List<AppAgentCategoryRespVO> children;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11550")
    private Long id;

    @Schema(description = "分类图", example = "https://www.iocoder.cn")
    private String picUrl;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String remark;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "热门")
    private Boolean hot;


}