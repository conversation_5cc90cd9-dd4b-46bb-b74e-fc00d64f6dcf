package cn.iocoder.yudao.module.agent.controller.admin.aftersale.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 代购售后订单新增/修改 Request VO")
@Data
public class AfterSaleSaveReqVO {

    @Schema(description = "售后编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "32498")
    private Long id;

    @Schema(description = "售后单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "售后单号不能为空")
    private String no;

    @Schema(description = "售后类型", example = "1")
    private Integer type;

    @Schema(description = "售后状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "售后状态不能为空")
    private Integer status;

    @Schema(description = "售后方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "售后方式不能为空")
    private Integer way;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15098")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "申请原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不对")
    @NotEmpty(message = "申请原因不能为空")
    private String applyReason;

    @Schema(description = "补充描述", example = "随便")
    private String applyDescription;

    @Schema(description = "补充凭证图片")
    private String applyPicUrls;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "27505")
    @NotNull(message = "订单编号不能为空")
    private Long orderId;

    @Schema(description = "订单流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单流水号不能为空")
    private String orderNo;

    @Schema(description = "订单项编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16838")
    @NotNull(message = "订单项编号不能为空")
    private Long orderItemId;

    @Schema(description = "商品 SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15562")
    @NotNull(message = "商品 SPU 编号不能为空")
    private Long spuId;

    @Schema(description = "商品 SPU 名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "商品 SPU 名称不能为空")
    private String spuName;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "29530")
    @NotNull(message = "商品 SKU 编号不能为空")
    private Long skuId;

    @Schema(description = "商品属性数组，JSON 格式")
    private String properties;

    @Schema(description = "商品图片", example = "https://www.iocoder.cn")
    private String picUrl;

    @Schema(description = "购买数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "30771")
    @NotNull(message = "购买数量不能为空")
    private Integer count;

    @Schema(description = "审批时间")
    private LocalDateTime auditTime;

    @Schema(description = "审批人", example = "12251")
    private Long auditUserId;

    @Schema(description = "审批备注", example = "不香")
    private String auditReason;

    @Schema(description = "退款金额，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "32370")
    @NotNull(message = "退款金额，单位：分不能为空")
    private Integer refundPrice;

    @Schema(description = "支付退款编号", example = "1364")
    private Long payRefundId;

    @Schema(description = "退款时间")
    private LocalDateTime refundTime;

    @Schema(description = "退货物流公司编号", example = "31559")
    private Long logisticsId;

    @Schema(description = "退货物流单号")
    private String logisticsNo;

    @Schema(description = "退货时间")
    private LocalDateTime deliveryTime;

    @Schema(description = "收货时间")
    private LocalDateTime receiveTime;

    @Schema(description = "收货备注", example = "不喜欢")
    private String receiveReason;

}