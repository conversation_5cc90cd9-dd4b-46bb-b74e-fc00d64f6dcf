package cn.iocoder.yudao.module.agent.controller.app.warehouse;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.app.warehouse.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.warehouse.AgentWarehouseDO;
import cn.iocoder.yudao.module.agent.service.warehouse.AgentWarehouseService;

@Tag(name = "用户 APP - 代购平台仓库")
@RestController
@RequestMapping("/agent/warehouse")
@Validated
public class AppAgentWarehouseController {

    @Resource
    private AgentWarehouseService warehouseService;


    @GetMapping("/get")
    @Operation(summary = "获得代购平台仓库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppAgentWarehouseRespVO> getWarehouse(@RequestParam("id") Long id) {
        AgentWarehouseDO warehouse = warehouseService.getWarehouse(id);
        return success(BeanUtils.toBean(warehouse, AppAgentWarehouseRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得代购平台仓库列表")
    @PermitAll
    public CommonResult<List<AppAgentWarehouseRespVO>> getWarehouseList() {
        List<AgentWarehouseDO> listDO = warehouseService.getWarehouseList();
        return success(BeanUtils.toBean(listDO, AppAgentWarehouseRespVO.class));
    }



}