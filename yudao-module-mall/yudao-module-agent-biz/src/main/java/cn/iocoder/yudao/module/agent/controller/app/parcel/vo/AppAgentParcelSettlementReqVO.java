package cn.iocoder.yudao.module.agent.controller.app.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 用户App 包裹结算 Request VO
 * @author: DingXiao
 * @create: 2025-04-30 14:38
 **/
@Data
@Valid
public class AppAgentParcelSettlementReqVO {

    @Schema(description = "商品项数组", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotEmpty(message = "商品不能为空")
    private List<Item> items;

    @Schema(description = "优惠劵编号", example = "1024")
    private Long couponId;

    @Schema(description = "是否使用积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否使用积分不能为空")
    private Boolean pointStatus;

    @Schema(description = "收件地址编号", example = "1")
    private Long addressId;

    @Schema(description = "物流方案编号", example = "1")
    private Long transportPlanId;

    @Schema(description = "物流方案费用编号", example = "1")
    private Long transportPlanFeeId;

    @Schema(description = "保险服务选项IDs", example = "1,2")
    private List<Long> insuranceServices;

    @Schema(description = "免费服务选项IDs", example = "1,2")
    private List<Long> freeServices;

    @Schema(description = "增值服务选项IDs", example = "1,2")
    private List<Long> chargeServices;


    @Data
    @Schema(description = "用户 App - 包裹商品项")
    public static class Item {

        //@Schema(description = "商品 SKU 编号", example = "2048")
        //@NotNull(message = "商品 SKU 编号不能为空")
        //private Long skuId;

        @Schema(description = "购买数量", example = "1")
        @Min(value = 1, message = "购买数量最小值为 {value}")
        private Integer count;

        @Schema(description = "库存项的编号", example = "1024")
        private Long stockId;

        @Schema(description = "库存数量", example = "1")
        private Integer stockCount;

    }
}
