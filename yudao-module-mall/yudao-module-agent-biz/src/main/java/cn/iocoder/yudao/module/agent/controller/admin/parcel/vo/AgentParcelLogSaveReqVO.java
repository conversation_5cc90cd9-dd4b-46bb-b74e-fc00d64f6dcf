package cn.iocoder.yudao.module.agent.controller.admin.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 代购包裹订单日志新增/修改 Request VO")
@Data
public class AgentParcelLogSaveReqVO {

    @Schema(description = "日志主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19988")
    private Long id;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "8317")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用户类型不能为空")
    private Integer userType;

    @Schema(description = "包裹单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "8951")
    @NotNull(message = "包裹单号不能为空")
    private Long parcelId;

    @Schema(description = "操作前状态", example = "2")
    private Integer beforeStatus;

    @Schema(description = "操作后状态", example = "2")
    private Integer afterStatus;

    @Schema(description = "操作类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "操作类型不能为空")
    private Integer operateType;

    @Schema(description = "操作内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "操作内容不能为空")
    private String content;

}