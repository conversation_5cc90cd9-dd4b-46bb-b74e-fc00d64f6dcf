package cn.iocoder.yudao.module.agent.framework.aftersale.core.annotations;

import cn.iocoder.yudao.module.agent.enums.aftersale.AfterSaleOperateTypeEnum;
import cn.iocoder.yudao.module.agent.framework.aftersale.core.aop.AgentAfterSaleLogAspect;

import java.lang.annotation.*;

/**
 * 售后日志的注解
 *
 * 写在方法上时，会自动记录售后日志
 *
 * <AUTHOR>
 * @since 2023/6/8 17:04
 * @see AgentAfterSaleLogAspect
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AgentAfterSaleLog {

    /**
     * 操作类型
     */
    AfterSaleOperateTypeEnum operateType();

}
