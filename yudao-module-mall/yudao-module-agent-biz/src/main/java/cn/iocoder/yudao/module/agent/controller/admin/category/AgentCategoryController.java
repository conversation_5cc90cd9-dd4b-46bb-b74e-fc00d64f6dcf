package cn.iocoder.yudao.module.agent.controller.admin.category;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.admin.category.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.category.AgentCategoryDO;
import cn.iocoder.yudao.module.agent.service.category.AgentCategoryService;

@Tag(name = "管理后台 - 代购商品分类")
@RestController
@RequestMapping("/agent/category")
@Validated
public class AgentCategoryController {

    @Resource
    private AgentCategoryService categoryService;

    @PostMapping("/create")
    @Operation(summary = "创建代购商品分类")
    @PreAuthorize("@ss.hasPermission('agent:category:create')")
    public CommonResult<Long> createCategory(@Valid @RequestBody AgentCategorySaveReqVO createReqVO) {
        return success(categoryService.createCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购商品分类")
    @PreAuthorize("@ss.hasPermission('agent:category:update')")
    public CommonResult<Boolean> updateCategory(@Valid @RequestBody AgentCategorySaveReqVO updateReqVO) {
        categoryService.updateCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购商品分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:category:delete')")
    public CommonResult<Boolean> deleteCategory(@RequestParam("id") Long id) {
        categoryService.deleteCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购商品分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:category:query')")
    public CommonResult<AgentCategoryRespVO> getCategory(@RequestParam("id") Long id) {
        AgentCategoryDO category = categoryService.getCategory(id);
        return success(BeanUtils.toBean(category, AgentCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得代购商品分类列表")
    @PreAuthorize("@ss.hasPermission('agent:category:query')")
    public CommonResult<List<AgentCategoryRespVO>> getCategoryList(@Valid AgentCategoryListReqVO listReqVO) {
        List<AgentCategoryDO> list = categoryService.getCategoryList(listReqVO);
        return success(BeanUtils.toBean(list, AgentCategoryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购商品分类 Excel")
    @PreAuthorize("@ss.hasPermission('agent:category:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCategoryExcel(@Valid AgentCategoryListReqVO listReqVO,
              HttpServletResponse response) throws IOException {
        List<AgentCategoryDO> list = categoryService.getCategoryList(listReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "代购商品分类.xls", "数据", AgentCategoryRespVO.class,
                        BeanUtils.toBean(list, AgentCategoryRespVO.class));
    }

}