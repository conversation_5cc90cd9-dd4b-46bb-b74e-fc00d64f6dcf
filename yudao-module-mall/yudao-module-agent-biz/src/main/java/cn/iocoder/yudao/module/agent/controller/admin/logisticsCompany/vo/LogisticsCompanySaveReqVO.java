package cn.iocoder.yudao.module.agent.controller.admin.logisticsCompany.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 代购物流公司新增/修改 Request VO")
@Data
public class LogisticsCompanySaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18318")
    private Long id;

    @Schema(description = "物流公司编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物流公司编码不能为空")
    private String code;

    @Schema(description = "物流公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "物流公司名称不能为空")
    private String name;

    @Schema(description = "物流公司 logo", example = "https://www.iocoder.cn")
    private String iconUrl;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}