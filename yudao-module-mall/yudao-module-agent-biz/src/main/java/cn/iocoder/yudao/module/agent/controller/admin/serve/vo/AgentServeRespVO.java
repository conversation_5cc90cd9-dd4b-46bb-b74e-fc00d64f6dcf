package cn.iocoder.yudao.module.agent.controller.admin.serve.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 代购服务项目 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AgentServeRespVO {

    @Schema(description = "服务编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4394")
    @ExcelProperty("服务编号")
    private Long id;

    @Schema(description = "服务编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("服务编码")
    private String code;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "类型", converter = DictConvert.class)
    @DictFormat("agent_serve_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer type;

    @Schema(description = "中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("中文名称")
    private String nameZh;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("英文名称")
    private String nameEn;

    @Schema(description = "法语名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("法语名称")
    private String nameFr;

    @Schema(description = "德语名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("德语名称")
    private String nameDe;

    @Schema(description = "西班牙语名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("西班牙语名称")
    private String nameEs;

    @Schema(description = "阿拉伯语名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("阿拉伯语名称")
    private String nameAr;

    @Schema(description = "中文描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("中文描述")
    private String descriptionZh;

    @Schema(description = "英文描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("英文描述")
    private String descriptionEn;

    @Schema(description = "法语描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("法语描述")
    private String descriptionFr;

    @Schema(description = "德语描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("德语描述")
    private String descriptionDe;

    @Schema(description = "西班牙语描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("西班牙语描述")
    private String descriptionEs;

    @Schema(description = "阿拉伯语描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("阿拉伯语描述")
    private String descriptionAr;

    @Schema(description = "是否免费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "是否免费", converter = DictConvert.class)
    @DictFormat("infra_boolean_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Boolean free;

    @Schema(description = "收费金额，单位使用：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "4086")
    @ExcelProperty("收费金额，单位使用：分")
    private Integer price;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "开启状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}