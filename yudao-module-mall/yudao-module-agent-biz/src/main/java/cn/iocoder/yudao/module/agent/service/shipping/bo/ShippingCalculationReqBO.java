package cn.iocoder.yudao.module.agent.service.shipping.bo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * 物流运费计算请求
 *
 */

@Data
public class ShippingCalculationReqBO {

    @Schema(description = "目标国家编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "US")
    @NotBlank(message = "目标国家编码不能为空")
    private String countryCode;

    @Schema(description = "重量(g)", requiredMode = Schema.RequiredMode.REQUIRED, example = "250")
    @NotNull(message = "重量不能为空")
    @Positive(message = "重量必须大于0")
    private Integer weight;

    @Schema(description = "商品分类ID列表", example = "[1, 2, 3]")
    private List<Long> categoryIds;

    @Schema(description = "长度(cm)", example = "30")
    private BigDecimal length;

    @Schema(description = "宽度(cm)", example = "20")
    private BigDecimal width;

    @Schema(description = "高度(cm)", example = "5")
    private BigDecimal height;

    @Schema(description = "邮编", example = "10001")
    private String postCode;

    @Schema(description = "州/省", example = "New York")
    private String stateProvince;

    @Schema(description = "城市", example = "New York")
    private String city;


    /**
     * 物流产品价格表编号
     */
    private Long priceId;
}
