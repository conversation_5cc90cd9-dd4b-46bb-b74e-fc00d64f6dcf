package cn.iocoder.yudao.module.agent.controller.app.deliveryexpress;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.app.deliveryexpress.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.deliveryexpress.AgentDeliveryExpressDO;
import cn.iocoder.yudao.module.agent.service.deliveryexpress.AgentDeliveryExpressService;

@Tag(name = "用户 APP - 代购快递公司")
@RestController
@RequestMapping("/agent/delivery-express")
@Validated
public class AppAgentDeliveryExpressController {

    @Resource
    private AgentDeliveryExpressService deliveryExpressService;



    @GetMapping("/get")
    @Operation(summary = "获得代购快递公司")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppAgentDeliveryExpressRespVO> getDeliveryExpress(@RequestParam("id") Long id) {
        AgentDeliveryExpressDO deliveryExpress = deliveryExpressService.getDeliveryExpress(id);
        return success(BeanUtils.toBean(deliveryExpress, AppAgentDeliveryExpressRespVO.class));
    }


    @GetMapping("/list")
    @Operation(summary = "获得代购快递公司列表")
    @PermitAll
    public CommonResult<List<AppAgentDeliveryExpressRespVO>> getDeliveryExpressList() {
        List<AgentDeliveryExpressDO> listDO = deliveryExpressService.getDeliveryExpressList();
        return success(BeanUtils.toBean(listDO, AppAgentDeliveryExpressRespVO.class));
    }



}