package cn.iocoder.yudao.module.agent.controller.app.stock.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "用户 APP - 代购仓库新增/修改 Request VO")
@Data
public class AppAgentStockSaveReqVO {

    @Schema(description = "品牌编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "5362")
    private Long id;

    @Schema(description = "仓库编号", example = "1024")
    private Long warehouseId;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30558")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30558")
    private Long categoryId;

    @Schema(description = "spu编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10218")
    @NotNull(message = "spu编号不能为空")
    private Long spuId;

    @Schema(description = "商品 SPU 名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "商品 SPU 名称不能为空")
    private String spuName;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31305")
    @NotNull(message = "商品 SKU 编号不能为空")
    private Long skuId;

    @Schema(description = "商品属性数组，JSON 格式")
    private String properties;

    @Schema(description = "商品图片", example = "https://www.iocoder.cn")
    private String picUrl;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "11391")
    @NotNull(message = "库存数量不能为空")
    private Integer count;

    @Schema(description = "商品重量")
    private Integer weight;

    @Schema(description = "商品体积")
    private BigDecimal volume;

    @Schema(description = "入库质检图片")
    private String inspectPicUrls;

    @Schema(description = "相关文件地址", example = "https://www.iocoder.cn")
    private String fileUrl;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "预包装重量")
    private Integer prePackageWeight;

    @Schema(description = "是否可合并", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean canCombine;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "入库日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "入库日期不能为空")
    private LocalDateTime inTime;

    @Schema(description = "到期日期")
    private LocalDateTime expiredTime;

}