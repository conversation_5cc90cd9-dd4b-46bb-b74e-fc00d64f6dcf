package cn.iocoder.yudao.module.agent.service.deliveryexpress;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.agent.controller.admin.deliveryexpress.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.deliveryexpress.AgentDeliveryExpressDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 代购快递公司 Service 接口
 *
 * <AUTHOR>
 */
public interface AgentDeliveryExpressService {

    /**
     * 创建代购快递公司
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeliveryExpress(@Valid AgentDeliveryExpressSaveReqVO createReqVO);

    /**
     * 更新代购快递公司
     *
     * @param updateReqVO 更新信息
     */
    void updateDeliveryExpress(@Valid AgentDeliveryExpressSaveReqVO updateReqVO);

    /**
     * 删除代购快递公司
     *
     * @param id 编号
     */
    void deleteDeliveryExpress(Long id);

    /**
     * 获得代购快递公司
     *
     * @param id 编号
     * @return 代购快递公司
     */
    AgentDeliveryExpressDO getDeliveryExpress(Long id);

    /**
     * 获得代购快递公司分页
     *
     * @param pageReqVO 分页查询
     * @return 代购快递公司分页
     */
    PageResult<AgentDeliveryExpressDO> getDeliveryExpressPage(AgentDeliveryExpressPageReqVO pageReqVO);

    //---------------------APP--------------------------

    /**
     * 获得代购快递公司分页
     *
     * @param pageReqVO 分页查询
     * @return 代购快递公司分页
     */
    List<AgentDeliveryExpressDO> getDeliveryExpressList();
}