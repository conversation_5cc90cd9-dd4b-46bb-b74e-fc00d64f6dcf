package cn.iocoder.yudao.module.agent.framework.parcel.core.utils;

import cn.iocoder.yudao.module.agent.framework.parcel.core.aop.AgentParcelLogAspect;

import java.util.Map;

/**
 * @program: ruoyi-vue-pro
 * @description: 包括订单的操作日志 Utils
 * @author: Ding<PERSON><PERSON><PERSON>
 * @create: 2025-05-21 11:15
 **/
public class AgentParcelLogUtils {

    public static void setOrderInfo(Long id, Integer beforeStatus, Integer afterStatus) {
        AgentParcelLogAspect.setOrderInfo(id, beforeStatus, afterStatus, null);
    }

    public static void setOrderInfo(Long id, Integer beforeStatus, Integer afterStatus,
                                    Map<String, Object> exts) {
        AgentParcelLogAspect.setOrderInfo(id, beforeStatus, afterStatus, exts);
    }

    public static void setUserInfo(Long userId, Integer userType) {
        AgentParcelLogAspect.setUserInfo(userId, userType);
    }
}
