package cn.iocoder.yudao.module.agent.controller.app.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: ruoyi-vue-pro
 * @description: 物流查询轨迹 Resp DTO
 * @author: DingXiao
 * @create: 2025-05-31 20:09
 **/
@Data
public class AppAgentParcelTrackRespDTO {

    @Schema(description = "发生时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime time;

    @Schema(description = "快递状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "已签收")
    private String content;
}
