package cn.iocoder.yudao.module.agent.service.parcellog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelLogPageReqVO;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelLogSaveReqVO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcellog.AgentParcelLogDO;
import cn.iocoder.yudao.module.agent.service.parcel.bo.AgentParcelLogCreateReqBO;
import org.springframework.scheduling.annotation.Async;

import javax.validation.Valid;
import java.util.List;

/**
 * 代购包裹订单日志 Service 接口
 *
 * <AUTHOR>
 */
public interface AgentParcelLogService {

    /**
     * 创建代购包裹订单日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createParcelLog(@Valid AgentParcelLogSaveReqVO createReqVO);

    /**
     * 更新代购包裹订单日志
     *
     * @param updateReqVO 更新信息
     */
    void updateParcelLog(@Valid AgentParcelLogSaveReqVO updateReqVO);

    /**
     * 删除代购包裹订单日志
     *
     * @param id 编号
     */
    void deleteParcelLog(Long id);

    /**
     * 获得代购包裹订单日志
     *
     * @param id 编号
     * @return 代购包裹订单日志
     */
    AgentParcelLogDO getParcelLog(Long id);

    /**
     * 获得代购包裹订单日志分页
     *
     * @param pageReqVO 分页查询
     * @return 代购包裹订单日志分页
     */
    PageResult<AgentParcelLogDO> getParcelLogPage(AgentParcelLogPageReqVO pageReqVO);


    /**
     * 创建代购包裹订单日志
     *
     * @param createBO 创建信息
     */
    @Async
    void createParcelLog(AgentParcelLogCreateReqBO createBO);


    /**
     * 根据编号查询代购包裹订单日志
     *
     * @param parcelId 订单编号
     * @return 代购包裹订单日志
     */
    List<AgentParcelLogDO> getParcelLogListByParcelId(Long parcelId);

    /**
     * 根据编号查询代购包裹订单日志
     *
     * @param id 订单编号
     * @return 代购包裹订单日志
     */
    List<AgentParcelLogDO> getOrderLogListByParcelId(Long id);
}