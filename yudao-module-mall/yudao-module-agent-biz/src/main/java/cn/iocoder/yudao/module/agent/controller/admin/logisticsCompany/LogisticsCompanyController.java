package cn.iocoder.yudao.module.agent.controller.admin.logisticsCompany;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.agent.convert.logisticsCompany.LogisticsCompanyConvert;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.admin.logisticsCompany.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsCompany.LogisticsCompanyDO;
import cn.iocoder.yudao.module.agent.service.logisticsCompany.LogisticsCompanyService;

@Tag(name = "管理后台 - 代购物流公司")
@RestController
@RequestMapping("/agent/logistics-company")
@Validated
public class LogisticsCompanyController {

    @Resource
    private LogisticsCompanyService logisticsCompanyService;

    @PostMapping("/create")
    @Operation(summary = "创建代购物流公司")
    @PreAuthorize("@ss.hasPermission('agent:logistics-company:create')")
    public CommonResult<Long> createLogisticsCompany(@Valid @RequestBody LogisticsCompanySaveReqVO createReqVO) {
        return success(logisticsCompanyService.createLogisticsCompany(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购物流公司")
    @PreAuthorize("@ss.hasPermission('agent:logistics-company:update')")
    public CommonResult<Boolean> updateLogisticsCompany(@Valid @RequestBody LogisticsCompanySaveReqVO updateReqVO) {
        logisticsCompanyService.updateLogisticsCompany(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购物流公司")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:logistics-company:delete')")
    public CommonResult<Boolean> deleteLogisticsCompany(@RequestParam("id") Long id) {
        logisticsCompanyService.deleteLogisticsCompany(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购物流公司")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:logistics-company:query')")
    public CommonResult<LogisticsCompanyRespVO> getLogisticsCompany(@RequestParam("id") Long id) {
        LogisticsCompanyDO logisticsCompany = logisticsCompanyService.getLogisticsCompany(id);
        return success(BeanUtils.toBean(logisticsCompany, LogisticsCompanyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购物流公司分页")
    @PreAuthorize("@ss.hasPermission('agent:logistics-company:query')")
    public CommonResult<PageResult<LogisticsCompanyRespVO>> getLogisticsCompanyPage(@Valid LogisticsCompanyPageReqVO pageReqVO) {
        PageResult<LogisticsCompanyDO> pageResult = logisticsCompanyService.getLogisticsCompanyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LogisticsCompanyRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购物流公司 Excel")
    @PreAuthorize("@ss.hasPermission('agent:logistics-company:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLogisticsCompanyExcel(@Valid LogisticsCompanyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LogisticsCompanyDO> list = logisticsCompanyService.getLogisticsCompanyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购物流公司.xls", "数据", LogisticsCompanyRespVO.class,
                        BeanUtils.toBean(list, LogisticsCompanyRespVO.class));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获取品牌精简信息列表", description = "主要用于前端的下拉选项")
    public CommonResult<List<LogisticsCompanySimpleRespVO>> getSimpleCompanyList() {
        // 获取品牌列表，只要开启状态的
        List<LogisticsCompanyDO> list = logisticsCompanyService.getLogisticsCompanyListByStatus(CommonStatusEnum.ENABLE.getStatus());
        // 排序后，返回给前端
        return success(LogisticsCompanyConvert.INSTANCE.convertList(list));
    }

}