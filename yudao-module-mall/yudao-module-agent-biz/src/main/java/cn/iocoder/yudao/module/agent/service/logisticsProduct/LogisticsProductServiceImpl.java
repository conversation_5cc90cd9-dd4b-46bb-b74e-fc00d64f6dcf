package cn.iocoder.yudao.module.agent.service.logisticsProduct;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductPriceDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import cn.iocoder.yudao.module.agent.dal.mysql.logisticsProduct.LogisticsProductMapper;
import cn.iocoder.yudao.module.agent.dal.mysql.logisticsProduct.LogisticsProductPriceMapper;
import cn.iocoder.yudao.module.agent.enums.logistics.AgentLogisticsPriceTypeEnum;
import lombok.extern.slf4j.Slf4j;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代购物流公司产品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LogisticsProductServiceImpl implements LogisticsProductService {

    @Resource
    private LogisticsProductMapper logisticsProductMapper;
    @Resource
    private LogisticsProductPriceMapper logisticsProductPriceMapper;

    @Override
    public Long createLogisticsProduct(LogisticsProductSaveReqVO createReqVO) {
        // 插入
        LogisticsProductDO logisticsProduct = BeanUtils.toBean(createReqVO, LogisticsProductDO.class);
        logisticsProductMapper.insert(logisticsProduct);
        // 返回
        return logisticsProduct.getId();
    }

    @Override
    public void updateLogisticsProduct(LogisticsProductSaveReqVO updateReqVO) {
        // 校验存在
        validateLogisticsProductExists(updateReqVO.getId());
        // 更新
        LogisticsProductDO updateObj = BeanUtils.toBean(updateReqVO, LogisticsProductDO.class);
        logisticsProductMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLogisticsProduct(Long id) {
        // 校验存在
        validateLogisticsProductExists(id);
        // 删除
        logisticsProductMapper.deleteById(id);

        // 删除子表
        deleteLogisticsProductPriceByProductId(id);
    }

    private void validateLogisticsProductExists(Long id) {
        if (logisticsProductMapper.selectById(id) == null) {
            throw exception(LOGISTICS_PRODUCT_NOT_EXISTS);
        }
    }

    @Override
    public LogisticsProductDO getLogisticsProduct(Long id) {
        return logisticsProductMapper.selectById(id);
    }

    @Override
    public PageResult<LogisticsProductDO> getLogisticsProductPage(LogisticsProductPageReqVO pageReqVO) {
        return logisticsProductMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（代购物流产品价格规则） ====================

    @Override
    public PageResult<LogisticsProductPriceDO> getLogisticsProductPricePage(PageParam pageReqVO, Long productId) {
        return logisticsProductPriceMapper.selectPage(pageReqVO, productId);
    }

    @Override
    public Long createLogisticsProductPrice(LogisticsProductPriceDO logisticsProductPrice) {
        logisticsProductPriceMapper.insert(logisticsProductPrice);
        return logisticsProductPrice.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LogisticsProductPriceBatchCreateRespVO batchCreateLogisticsProductPrice(LogisticsProductPriceBatchCreateReqVO batchCreateReqVO) {
        // 校验产品是否存在
        LogisticsProductDO product = logisticsProductMapper.selectById(batchCreateReqVO.getProductId());
        if (product == null) {
            throw exception(LOGISTICS_PRODUCT_NOT_EXISTS);
        }

        // 校验国家编码列表不能为空
        if (CollUtil.isEmpty(batchCreateReqVO.getCountryCodes())) {
            throw new IllegalArgumentException("国家编码列表不能为空");
        }

        // 校验基础数据
        validateBatchCreateRequest(batchCreateReqVO);

        LogisticsProductPriceBatchCreateRespVO.LogisticsProductPriceBatchCreateRespVOBuilder respBuilder =
            LogisticsProductPriceBatchCreateRespVO.builder();

        int successCount = 0;
        int failureCount = 0;
        List<Long> successIds = new ArrayList<>();
        List<Map<String, Object>> failureData = new ArrayList<>();

        // 为每个国家创建价格规则
        for (String countryCode : batchCreateReqVO.getCountryCodes()) {
            try {
                // 检查是否已存在相同的价格规则
                LogisticsProductPriceDO existingPrice = findExistingPrice(
                    batchCreateReqVO.getProductId(),
                    countryCode,
                    batchCreateReqVO.getZoneCode()
                );

                if (existingPrice != null) {
                    throw new IllegalArgumentException("国家 " + countryCode + " 的价格规则已存在");
                }

                // 创建新的价格规则
                LogisticsProductPriceDO newPrice = createPriceFromBatchRequest(batchCreateReqVO, countryCode);
                logisticsProductPriceMapper.insert(newPrice);

                successCount++;
                successIds.add(newPrice.getId());

            } catch (Exception e) {
                failureCount++;
                Map<String, Object> failureItem = new HashMap<>();
                failureItem.put("countryCode", countryCode);
                failureItem.put("reason", e.getMessage());
                failureData.add(failureItem);
            }
        }

        return respBuilder
                .successCount(successCount)
                .failureCount(failureCount)
                .successIds(successIds)
                .failureData(failureData)
                .build();
    }

    @Override
    public void updateLogisticsProductPrice(LogisticsProductPriceDO logisticsProductPrice) {
        // 校验存在
        validateLogisticsProductPriceExists(logisticsProductPrice.getId());

        // 更新 - 现在时间字段使用IGNORED策略，会强制更新null值
        logisticsProductPrice.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        logisticsProductPriceMapper.updateById(logisticsProductPrice);

        log.info("更新价格规则: ID={}, 生效时间={}, 失效时间={}",
                logisticsProductPrice.getId(),
                logisticsProductPrice.getEffectiveTime(),
                logisticsProductPrice.getExpireTime());
    }

    @Override
    public void deleteLogisticsProductPrice(Long id) {
        // 校验存在
        validateLogisticsProductPriceExists(id);
        // 删除
        logisticsProductPriceMapper.deleteById(id);
    }

    @Override
    public LogisticsProductPriceDO getLogisticsProductPrice(Long id) {
        return logisticsProductPriceMapper.selectById(id);
    }

    private void validateLogisticsProductPriceExists(Long id) {
        if (logisticsProductPriceMapper.selectById(id) == null) {
            throw exception(LOGISTICS_PRODUCT_PRICE_NOT_EXISTS);
        }
    }

    private void deleteLogisticsProductPriceByProductId(Long productId) {
        logisticsProductPriceMapper.deleteByProductId(productId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LogisticsProductPriceImportRespVO importLogisticsProductPriceList(Long productId, List<LogisticsProductPriceImportExcelVO> importPrices, boolean isUpdateSupport) {
        if (CollUtil.isEmpty(importPrices)) {
            throw exception(LOGISTICS_PRODUCT_PRICE_IMPORT_LIST_IS_EMPTY);
        }

        // 检查产品是否存在
        LogisticsProductDO product = logisticsProductMapper.selectById(productId);
        if (product == null) {
            throw exception(LOGISTICS_PRODUCT_NOT_EXISTS);
        }

        LogisticsProductPriceImportRespVO.LogisticsProductPriceImportRespVOBuilder respBuilder = LogisticsProductPriceImportRespVO.builder();
        int createSuccessCount = 0;
        int updateSuccessCount = 0;
        int failureCount = 0;
        List<Map<String, Object>> failureData = new ArrayList<>();

        for (int i = 0; i < importPrices.size(); i++) {
            LogisticsProductPriceImportExcelVO importPrice = importPrices.get(i);
            try {
                // 参数校验
                validateImportPrice(importPrice, i + 2); // Excel 行号从第2行开始

                // 检查是否已存在相同的价格规则（产品ID + 国家编码 + 分区编码）
                LogisticsProductPriceDO existingPrice = findExistingPrice(productId,
                    importPrice.getCountryCode(), importPrice.getZoneCode());

                if (existingPrice != null) {
                    if (isUpdateSupport) {
                        // 更新现有记录
                        updateExistingPrice(existingPrice, importPrice, productId);
                        updateSuccessCount++;
                    } else {
                        throw new IllegalArgumentException("价格规则已存在，如需更新请选择更新模式");
                    }
                } else {
                    // 创建新记录
                    createNewPrice(importPrice, productId);
                    createSuccessCount++;
                }

            } catch (Exception e) {
                failureCount++;
                Map<String, Object> failureRow = new HashMap<>();
                failureRow.put("row", i + 2);
                failureRow.put("data", importPrice);
                failureRow.put("reason", e.getMessage());
                failureData.add(failureRow);
            }
        }

        return respBuilder
                .createSuccessCount(createSuccessCount)
                .updateSuccessCount(updateSuccessCount)
                .failureCount(failureCount)
                .failureData(failureData)
                .build();
    }

    /**
     * 校验导入的价格规则数据
     */
    private void validateImportPrice(LogisticsProductPriceImportExcelVO importPrice, int rowNum) {
        if (StrUtil.isBlank(importPrice.getCountryCode())) {
            throw new IllegalArgumentException("第" + rowNum + "行：国家编码不能为空");
        }
        if (StrUtil.isBlank(importPrice.getChargeType())) {
            throw new IllegalArgumentException("第" + rowNum + "行：计费方式不能为空");
        }
        if (StrUtil.isBlank(importPrice.getPriceType())) {
            throw new IllegalArgumentException("第" + rowNum + "行：价格类型不能为空");
        }
        if (importPrice.getFirstUnit() == null || importPrice.getFirstUnit() <= 0) {
            throw new IllegalArgumentException("第" + rowNum + "行：首重/首件数量必须大于0");
        }
        if (importPrice.getFirstPrice() == null || importPrice.getFirstPrice() <= 0) {
            throw new IllegalArgumentException("第" + rowNum + "行：首重/首件价格必须大于0");
        }

        // 校验计费方式
        if (!Arrays.asList("WEIGHT", "VOLUME", "PIECE").contains(importPrice.getChargeType())) {
            throw new IllegalArgumentException("第" + rowNum + "行：计费方式只能是WEIGHT、VOLUME、PIECE之一");
        }

        // 校验价格类型
        if (!AgentLogisticsPriceTypeEnum.isValidCode(importPrice.getPriceType())) {
            throw new IllegalArgumentException("第" + rowNum + "行：价格类型只能是" +
                AgentLogisticsPriceTypeEnum.INCREMENTAL.getCode() + "、" +
                AgentLogisticsPriceTypeEnum.TIERED.getCode() + "、" +
                AgentLogisticsPriceTypeEnum.TIERED_INCREMENTAL.getCode() + "之一");
        }

        // 如果是递增计费，续重单位和价格不能为空
        if (AgentLogisticsPriceTypeEnum.INCREMENTAL.getCode().equals(importPrice.getPriceType())) {
            if (importPrice.getAdditionalUnit() == null || importPrice.getAdditionalUnit() <= 0) {
                throw new IllegalArgumentException("第" + rowNum + "行：递增计费模式下续重/续件单位必须大于0");
            }
            if (importPrice.getAdditionalPrice() == null || importPrice.getAdditionalPrice() <= 0) {
                throw new IllegalArgumentException("第" + rowNum + "行：递增计费模式下续重/续件价格必须大于0");
            }
        }

        // 如果是阶梯计费，阶梯价格配置不能为空且格式必须正确
        if (AgentLogisticsPriceTypeEnum.TIERED.getCode().equals(importPrice.getPriceType())) {
            if (StrUtil.isBlank(importPrice.getTieredPrices())) {
                throw new IllegalArgumentException("第" + rowNum + "行：阶梯计费模式下阶梯价格配置不能为空");
            }
            if (!cn.iocoder.yudao.module.agent.util.TieredPriceCalculator.isValidTieredPricesConfig(importPrice.getTieredPrices())) {
                throw new IllegalArgumentException("第" + rowNum + "行：阶梯价格配置格式错误");
            }
        }

        // 如果是阶梯递增计费，阶梯递增价格配置不能为空
        if (AgentLogisticsPriceTypeEnum.TIERED_INCREMENTAL.getCode().equals(importPrice.getPriceType())) {
            if (StrUtil.isBlank(importPrice.getTieredIncrementalPrices())) {
                throw new IllegalArgumentException("第" + rowNum + "行：阶梯递增计费模式下阶梯递增价格配置不能为空");
            }
        }

        // 校验JSON格式
        if (StrUtil.isNotBlank(importPrice.getTieredPrices())) {
            try {
                JSONUtil.parseArray(importPrice.getTieredPrices());
            } catch (Exception e) {
                throw new IllegalArgumentException("第" + rowNum + "行：阶梯价格配置JSON格式错误");
            }
        }
    }

    /**
     * 查找已存在的价格规则
     */
    private LogisticsProductPriceDO findExistingPrice(Long productId, String countryCode, String zoneCode) {
        return logisticsProductPriceMapper.selectOne(new LambdaQueryWrapperX<LogisticsProductPriceDO>()
                .eq(LogisticsProductPriceDO::getProductId, productId)
                .eq(LogisticsProductPriceDO::getCountryCode, countryCode)
                .eqIfPresent(LogisticsProductPriceDO::getZoneCode, zoneCode));
    }

    /**
     * 更新已存在的价格规则
     */
    private void updateExistingPrice(LogisticsProductPriceDO existingPrice, LogisticsProductPriceImportExcelVO importPrice, Long productId) {
        // 手动复制属性，避免覆盖关键字段
        existingPrice.setCountryCode(importPrice.getCountryCode());
        existingPrice.setZoneCode(importPrice.getZoneCode());
        existingPrice.setTransitTime(importPrice.getTransitTime());
        existingPrice.setChargeType(importPrice.getChargeType());
        existingPrice.setPriceType(importPrice.getPriceType());
        existingPrice.setFirstUnit(importPrice.getFirstUnit());
        existingPrice.setFirstPrice(importPrice.getFirstPrice());
        existingPrice.setAdditionalUnit(importPrice.getAdditionalUnit());
        existingPrice.setAdditionalPrice(importPrice.getAdditionalPrice());
        existingPrice.setMinWeight(importPrice.getMinWeight());
        existingPrice.setMaxWeight(importPrice.getMaxWeight());
        existingPrice.setTieredPrices(importPrice.getTieredPrices());
        existingPrice.setFuelFeeRate(importPrice.getFuelFeeRate());
        existingPrice.setRegistrationFee(importPrice.getRegistrationFee());
        existingPrice.setOperationFee(importPrice.getOperationFee());
        existingPrice.setServiceFee(importPrice.getServiceFee());
        existingPrice.setCustomsFee(importPrice.getCustomsFee());
        existingPrice.setPrepayTariff(importPrice.getPrepayTariff());
        existingPrice.setTariffRate(importPrice.getTariffRate());
        existingPrice.setEffectiveTime(importPrice.getEffectiveTime());
        existingPrice.setExpireTime(importPrice.getExpireTime());
        // 保持原有的productId、id、createTime、creator等字段不变
        existingPrice.setProductId(productId);
        logisticsProductPriceMapper.updateById(existingPrice);
    }

    /**
     * 创建新的价格规则
     */
    private void createNewPrice(LogisticsProductPriceImportExcelVO importPrice, Long productId) {
        LogisticsProductPriceDO newPrice = new LogisticsProductPriceDO();
        // 手动设置属性
        newPrice.setProductId(productId);
        newPrice.setCountryCode(importPrice.getCountryCode());
        newPrice.setZoneCode(importPrice.getZoneCode());
        newPrice.setTransitTime(importPrice.getTransitTime());
        newPrice.setChargeType(importPrice.getChargeType());
        newPrice.setPriceType(importPrice.getPriceType());
        newPrice.setFirstUnit(importPrice.getFirstUnit());
        newPrice.setFirstPrice(importPrice.getFirstPrice());
        newPrice.setAdditionalUnit(importPrice.getAdditionalUnit());
        newPrice.setAdditionalPrice(importPrice.getAdditionalPrice());
        newPrice.setMinWeight(importPrice.getMinWeight());
        newPrice.setMaxWeight(importPrice.getMaxWeight());
        newPrice.setTieredPrices(importPrice.getTieredPrices());
        newPrice.setFuelFeeRate(importPrice.getFuelFeeRate());
        newPrice.setRegistrationFee(importPrice.getRegistrationFee());
        newPrice.setOperationFee(importPrice.getOperationFee());
        newPrice.setServiceFee(importPrice.getServiceFee());
        newPrice.setCustomsFee(importPrice.getCustomsFee());
        newPrice.setPrepayTariff(importPrice.getPrepayTariff());
        newPrice.setTariffRate(importPrice.getTariffRate());
        newPrice.setEffectiveTime(importPrice.getEffectiveTime());
        newPrice.setExpireTime(importPrice.getExpireTime());
        // 设置默认值
        newPrice.setStatus(1); // 默认启用
        newPrice.setSort(0); // 默认排序
        logisticsProductPriceMapper.insert(newPrice);
    }

    /**
     * 校验批量创建请求数据
     */
    private void validateBatchCreateRequest(LogisticsProductPriceBatchCreateReqVO batchCreateReqVO) {
        if (StrUtil.isBlank(batchCreateReqVO.getChargeType())) {
            throw new IllegalArgumentException("计费方式不能为空");
        }
        if (StrUtil.isBlank(batchCreateReqVO.getPriceType())) {
            throw new IllegalArgumentException("价格类型不能为空");
        }
        if (batchCreateReqVO.getFirstUnit() == null || batchCreateReqVO.getFirstUnit() <= 0) {
            throw new IllegalArgumentException("首重/首件数量必须大于0");
        }
        if (batchCreateReqVO.getFirstPrice() == null || batchCreateReqVO.getFirstPrice() <= 0) {
            throw new IllegalArgumentException("首重/首件价格必须大于0");
        }

        // 校验计费方式
        if (!Arrays.asList("WEIGHT", "VOLUME", "PIECE").contains(batchCreateReqVO.getChargeType())) {
            throw new IllegalArgumentException("计费方式只能是WEIGHT、VOLUME、PIECE之一");
        }

        // 校验价格类型
        if (!Arrays.asList(AgentLogisticsPriceTypeEnum.TIERED.getCode(),
                AgentLogisticsPriceTypeEnum.INCREMENTAL.getCode(),
                AgentLogisticsPriceTypeEnum.TIERED_INCREMENTAL.getCode()).contains(batchCreateReqVO.getPriceType())) {
            throw new IllegalArgumentException("价格类型只能是TIERED、INCREMENTAL、TIERED_INCREMENTAL之一");
        }

        // 如果是递增计费，续重单位和价格不能为空
        if ("INCREMENTAL".equals(batchCreateReqVO.getPriceType())) {
            if (batchCreateReqVO.getAdditionalUnit() == null || batchCreateReqVO.getAdditionalUnit() <= 0) {
                throw new IllegalArgumentException("递增计费模式下续重/续件单位必须大于0");
            }
            if (batchCreateReqVO.getAdditionalPrice() == null || batchCreateReqVO.getAdditionalPrice() <= 0) {
                throw new IllegalArgumentException("递增计费模式下续重/续件价格必须大于0");
            }
        }

        // 如果是阶梯计费，阶梯价格配置不能为空且格式必须正确
        if ("TIERED".equals(batchCreateReqVO.getPriceType())) {
            if (StrUtil.isBlank(batchCreateReqVO.getTieredPrices())) {
                throw new IllegalArgumentException("阶梯计费模式下阶梯价格配置不能为空");
            }
            if (!cn.iocoder.yudao.module.agent.util.TieredPriceCalculator.isValidTieredPricesConfig(batchCreateReqVO.getTieredPrices())) {
                throw new IllegalArgumentException("阶梯价格配置格式错误");
            }
        }

        // 校验JSON格式
        if (StrUtil.isNotBlank(batchCreateReqVO.getTieredPrices())) {
            try {
                JSONUtil.parseArray(batchCreateReqVO.getTieredPrices());
            } catch (Exception e) {
                throw new IllegalArgumentException("阶梯价格配置JSON格式错误");
            }
        }
    }

    /**
     * 根据批量创建请求创建价格规则对象
     */
    private LogisticsProductPriceDO createPriceFromBatchRequest(LogisticsProductPriceBatchCreateReqVO batchCreateReqVO, String countryCode) {
        LogisticsProductPriceDO newPrice = new LogisticsProductPriceDO();

        // 设置基础信息
        newPrice.setProductId(batchCreateReqVO.getProductId());
        newPrice.setCountryCode(countryCode);
        newPrice.setZoneCode(batchCreateReqVO.getZoneCode());
        newPrice.setTransitTime(batchCreateReqVO.getTransitTime());
        newPrice.setTimelinessInfo(batchCreateReqVO.getTimelinessInfo());

        // 设置计费信息
        newPrice.setChargeType(batchCreateReqVO.getChargeType());
        newPrice.setPriceType(batchCreateReqVO.getPriceType());
        newPrice.setFirstUnit(batchCreateReqVO.getFirstUnit());
        newPrice.setFirstPrice(batchCreateReqVO.getFirstPrice());
        newPrice.setAdditionalUnit(batchCreateReqVO.getAdditionalUnit());
        newPrice.setAdditionalPrice(batchCreateReqVO.getAdditionalPrice());

        // 设置重量限制
        newPrice.setMinWeight(batchCreateReqVO.getMinWeight());
        newPrice.setMaxWeight(batchCreateReqVO.getMaxWeight());
        newPrice.setTieredPrices(batchCreateReqVO.getTieredPrices());

        // 设置费用信息
        newPrice.setFuelFeeRate(batchCreateReqVO.getFuelFeeRate());
        newPrice.setRegistrationFee(batchCreateReqVO.getRegistrationFee());
        newPrice.setOperationFee(batchCreateReqVO.getOperationFee());
        newPrice.setServiceFee(batchCreateReqVO.getServiceFee());
        newPrice.setCustomsFee(batchCreateReqVO.getCustomsFee());
        newPrice.setPrepayTariff(batchCreateReqVO.getPrepayTariff());
        newPrice.setTariffRate(batchCreateReqVO.getTariffRate());

        // 设置时间信息
        newPrice.setEffectiveTime(batchCreateReqVO.getEffectiveTime());
        newPrice.setExpireTime(batchCreateReqVO.getExpireTime());

        // 设置排序和状态
        newPrice.setSort(batchCreateReqVO.getSort() != null ? batchCreateReqVO.getSort() : 0);
        newPrice.setStatus(batchCreateReqVO.getStatus() != null ? batchCreateReqVO.getStatus() : 1);

        return newPrice;
    }

    @Override
    public LogisticsProductPriceDO getLogisticsProductPriceByCountry(Long productId, String countryCode) {
        // 首先查找有分区编码的价格规则
        LogisticsProductPriceDO priceWithZone = logisticsProductPriceMapper.selectOne(
            new LambdaQueryWrapper<LogisticsProductPriceDO>()
                .eq(LogisticsProductPriceDO::getProductId, productId)
                .eq(LogisticsProductPriceDO::getCountryCode, countryCode)
                .isNotNull(LogisticsProductPriceDO::getZoneCode)
                .ne(LogisticsProductPriceDO::getZoneCode, "")
                .eq(LogisticsProductPriceDO::getStatus, 1)
                .orderByDesc(LogisticsProductPriceDO::getCreateTime)
                .last("LIMIT 1")
        );

        if (priceWithZone != null) {
            return priceWithZone;
        }

        // 如果没有找到有分区的，查找无分区的价格规则
        return logisticsProductPriceMapper.selectOne(
            new LambdaQueryWrapper<LogisticsProductPriceDO>()
                .eq(LogisticsProductPriceDO::getProductId, productId)
                .eq(LogisticsProductPriceDO::getCountryCode, countryCode)
                .and(wrapper -> wrapper.isNull(LogisticsProductPriceDO::getZoneCode)
                                      .or()
                                      .eq(LogisticsProductPriceDO::getZoneCode, ""))
                .eq(LogisticsProductPriceDO::getStatus, 1)
                .orderByDesc(LogisticsProductPriceDO::getCreateTime)
                .last("LIMIT 1")
        );
    }

    @Override
    public List<LogisticsProductDO> getLogisticsProductListByIds(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return  logisticsProductMapper.selectLogisticsProductListByIds(ids);
    }
}