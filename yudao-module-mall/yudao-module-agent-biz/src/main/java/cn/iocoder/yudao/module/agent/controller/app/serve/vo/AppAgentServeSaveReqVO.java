package cn.iocoder.yudao.module.agent.controller.app.serve.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "用户 APP - 代购服务项目新增/修改 Request VO")
@Data
public class AppAgentServeSaveReqVO {

    @Schema(description = "服务编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4394")
    private Long id;

    @Schema(description = "服务编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "服务编码不能为空")
    private String code;

    @Schema(description = "服务图标")
    private String icon;

    @Schema(description = "类型;保险，免费服务，收费服务", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "类型;保险，免费服务，收费服务不能为空")
    private Integer type;

    @Schema(description = "中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "中文名称不能为空")
    private String nameZh;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "英文名称不能为空")
    private String nameEn;

    @Schema(description = "法语名称")
    private String nameFr;

    @Schema(description = "德语名称")
    private String nameDe;

    @Schema(description = "西班牙语名称")
    private String nameEs;

    @Schema(description = "阿拉伯语名称")
    private String nameAr;

    @Schema(description = "中文描述")
    private String descriptionZh;

    @Schema(description = "英文描述")
    private String descriptionEn;

    @Schema(description = "法语描述")
    private String descriptionFr;

    @Schema(description = "德语描述")
    private String descriptionDe;

    @Schema(description = "西班牙语描述")
    private String descriptionEs;

    @Schema(description = "阿拉伯语描述")
    private String descriptionAr;

    @Schema(description = "是否免费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否免费不能为空")
    private Boolean free;

    @Schema(description = "收费金额，单位使用：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "4086")
    @NotNull(message = "收费金额，单位使用：分不能为空")
    private Integer price;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "开启状态不能为空")
    private Integer status;

}