package cn.iocoder.yudao.module.agent.controller.admin.parcel;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.*;
import cn.iocoder.yudao.module.agent.convert.parcel.AgentParcelConvert;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelItemDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcellog.AgentParcelLogDO;
import cn.iocoder.yudao.module.agent.framework.delivery.core.client.dto.ExpressTrackRespDTO;
import cn.iocoder.yudao.module.agent.service.logisticsProduct.LogisticsProductService;
import cn.iocoder.yudao.module.agent.service.parcel.AgentParcelQueryService;
import cn.iocoder.yudao.module.agent.service.parcel.AgentParcelService;
import cn.iocoder.yudao.module.agent.service.parcel.AgentParcelUpdateService;
import cn.iocoder.yudao.module.agent.service.parcellog.AgentParcelLogService;
import cn.iocoder.yudao.module.agent.service.serve.AgentServeService;
import cn.iocoder.yudao.module.agent.service.serve.bo.AgentServerBO;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.member.api.user.dto.MemberUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.*;

@Tag(name = "管理后台 - 代购包裹")
@RestController
@RequestMapping("/agent/parcel")
@Validated
public class AgentParcelController {

    @Resource
    private AgentParcelService parcelService;

    @Resource
    private AgentParcelQueryService  parcelQueryService;
    @Resource
    private AgentParcelUpdateService parcelUpdateService;

    @Resource
    private AgentParcelLogService parcelLogService;

    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private LogisticsProductService logisticsProductService;
    @Resource
    private AgentServeService  serveService;

    @PostMapping("/create")
    @Operation(summary = "创建代购包裹")
    @PreAuthorize("@ss.hasPermission('agent:parcel:create')")
    public CommonResult<Long> createParcel(@Valid @RequestBody AgentParcelSaveReqVO createReqVO) {
        return success(parcelService.createParcel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购包裹")
    @PreAuthorize("@ss.hasPermission('agent:parcel:update')")
    public CommonResult<Boolean> updateParcel(@Valid @RequestBody AgentParcelSaveReqVO updateReqVO) {
        parcelService.updateParcel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购包裹")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:parcel:delete')")
    public CommonResult<Boolean> deleteParcel(@RequestParam("id") Long id) {
        parcelService.deleteParcel(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购包裹")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:parcel:query')")
    public CommonResult<AgentParcelRespVO> getParcel(@RequestParam("id") Long id) {
        AgentParcelDO parcel = parcelService.getParcel(id);
        return success(BeanUtils.toBean(parcel, AgentParcelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购包裹分页")
    @PreAuthorize("@ss.hasPermission('agent:parcel:query')")
    public CommonResult<PageResult<AgentParcelPageItemRespVO>> getParcelPage(@Valid AgentParcelPageReqVO pageReqVO) {
        PageResult<AgentParcelDO> pageResult = parcelQueryService.getParcelPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        // 查询包裹明细项
        List<AgentParcelItemDO> parcelItems = parcelQueryService.getParcelItemListByParcelId(
                convertSet(pageResult.getList(), AgentParcelDO::getId));

        // 查询用户信息
        Set<Long> userIds = CollUtil.unionDistinct(convertList(pageResult.getList(), AgentParcelDO::getUserId),
                convertList(pageResult.getList(), AgentParcelDO::getBrokerageUserId, Objects::nonNull));
        Map<Long, MemberUserRespDTO> userMap = memberUserApi.getUserMap(userIds);

        //运输方案
        Set<Long> planIds = convertSet(pageResult.getList(), AgentParcelDO::getTransportPlanId);
//        List<AgentTransportPlanDO> transportPlans = transportPlanService.getTransportPlanListByIds(planIds);
//        Map<Long, AgentTransportPlanDO> planMap = convertMap(transportPlans, AgentTransportPlanDO::getId);
        List<LogisticsProductDO> logisticsProducts = logisticsProductService.getLogisticsProductListByIds(planIds);
        Map<Long, LogisticsProductDO> planMap = convertMap(logisticsProducts, LogisticsProductDO::getId);

        //服务项
        Set<Long> allServiceIds = pageResult.getList().stream()
                .flatMap(agentParcel -> Stream.of(
                        agentParcel.getInsuranceServices(),
                        agentParcel.getFreeServices(),
                        agentParcel.getChargeServices()
                ))
                .filter(Objects::nonNull) // 过滤 null 的情况
                .flatMap(List::stream)     // 展开所有 list 为一个流
                .collect(Collectors.toSet());
        List<AgentServerBO> servers = serveService.selectListByIds(allServiceIds, "zh");
        Map<Long, AgentServerBO> serverMap = convertMap(servers, AgentServerBO::getId);


        return success(AgentParcelConvert.INSTANCE.convertPage(pageResult,parcelItems,userMap,planMap,serverMap));
    }

    @GetMapping("/get-detail")
    @Operation(summary = "获得包裹订单详情")
    @Parameter(name = "id", description = "包裹订单编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('agent:parcel:query')")
    public CommonResult<AgentParcelDetailRespVO> getOrderDetail(@RequestParam("id") Long id) {
        // 查询订单
        AgentParcelDO parcel = parcelQueryService.getParcel(id);
        if (parcel == null) {
            return success(null);
        }
        // 查询订单项
        List<AgentParcelItemDO> parcelItems = parcelQueryService.getParcelItemListByOrderId(id);

        //运输方案
//        AgentTransportPlanDO transportPlan = transportPlanService.getTransportPlan(parcel.getTransportPlanId());
        LogisticsProductDO transportPlan = logisticsProductService.getLogisticsProduct(parcel.getTransportPlanId());

        //服务项
        Set<Long> serverIds = CollUtil.unionDistinct(parcel.getInsuranceServices(), parcel.getFreeServices(), parcel.getChargeServices());
        List<AgentServerBO> servers = serveService.selectListByIds(serverIds, "zh");
        Map<Long, AgentServerBO> serverMap = convertMap(servers, AgentServerBO::getId);

        // 拼接数据
        MemberUserRespDTO user = memberUserApi.getUser(parcel.getUserId());
        MemberUserRespDTO brokerageUser = parcel.getBrokerageUserId() != null ?
                memberUserApi.getUser(parcel.getBrokerageUserId()) : null;
        List<AgentParcelLogDO> orderLogs = parcelLogService.getOrderLogListByParcelId(id);


        return success(AgentParcelConvert.INSTANCE.convert(parcel, parcelItems, orderLogs, user, brokerageUser,transportPlan,serverMap));
    }

    @PutMapping("/delivery")
    @Operation(summary = "包裹订单发货")
    @PreAuthorize("@ss.hasPermission('agent:parcel:update')")
    public CommonResult<Boolean> deliveryParcel(@RequestBody AgentParcelTransportReqVO deliveryReqVO) {
        parcelUpdateService.deliveryParcel(deliveryReqVO);
        return success(true);
    }

    @PutMapping("/update-remark")
    @Operation(summary = "包裹订单备注")
    @PreAuthorize("@ss.hasPermission('agent:parcel:update')")
    public CommonResult<Boolean> updateParcelRemark(@RequestBody AgentParcelRemarkReqVO reqVO) {
        parcelUpdateService.updateParcelRemark(reqVO);
        return success(true);
    }

    @PutMapping("/update-price")
    @Operation(summary = "订单调价")
    @PreAuthorize("@ss.hasPermission('agent:parcel:update')")
    public CommonResult<Boolean> updateOrderPrice(@RequestBody AgentParcelUpdatePriceReqVO reqVO) {
        parcelUpdateService.updateParcelPrice(reqVO);
        return success(true);
    }

    @PutMapping("/update-address")
    @Operation(summary = "修改包裹订单收货地址")
    @PreAuthorize("@ss.hasPermission('agent:parcel:update')")
    public CommonResult<Boolean> updateOrderAddress(@RequestBody AgentParcelUpdateAddressReqVO reqVO) {
        parcelUpdateService.updateOrderAddress(reqVO);
        return success(true);
    }

    @GetMapping("/get-track-list")
    @Operation(summary = "获得包裹交易订单的物流轨迹")
    @Parameter(name = "id", description = "包裹订单编号")
    @PreAuthorize("@ss.hasPermission('agent:parcel:query')")
    public CommonResult<List<?>> getParcelTrackList(@RequestParam("id") Long id) {

        List<ExpressTrackRespDTO>  tracks =  parcelQueryService.getParcelTrackList(id);

        return success(AgentParcelConvert.INSTANCE.convertList02(tracks));
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出代购包裹 Excel")
    @PreAuthorize("@ss.hasPermission('agent:parcel:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportParcelExcel(@Valid AgentParcelPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentParcelDO> list = parcelService.getParcelPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购包裹.xls", "数据", AgentParcelRespVO.class,
                        BeanUtils.toBean(list, AgentParcelRespVO.class));
    }

    // ==================== 子表（代购包裹明细） ====================

    @GetMapping("/parcel-item/list-by-parcel-id")
    @Operation(summary = "获得代购包裹明细列表")
    @Parameter(name = "parcelId", description = "订单编号")
    @PreAuthorize("@ss.hasPermission('agent:parcel:query')")
    public CommonResult<List<AgentParcelItemDO>> getParcelItemListByParcelId(@RequestParam("parcelId") Long parcelId) {
        return success(parcelService.getParcelItemListByParcelId(parcelId));
    }

}