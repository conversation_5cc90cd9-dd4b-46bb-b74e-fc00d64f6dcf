package cn.iocoder.yudao.module.agent.controller.app.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 APP - 代购包裹新增 Request VO")
@Data
public class AppAgentParcelCreateReqVO extends AppAgentParcelSettlementReqVO {

    @Schema(description = "用户备注", example = "这个是我的订单哟")
    private String userRemark;


    @Schema(description = "海关申报内容", example = "衣服")
    private String declareContent;

    @Schema(description = "海关申报价值", example = "5000")
    private Integer declareValue;

    @Schema(description = "清关代码", example = "123")
    private String clearanceCode;


    @Schema(description = "币别", example = "USD")
    public String currency;

}