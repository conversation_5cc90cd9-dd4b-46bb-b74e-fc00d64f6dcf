package cn.iocoder.yudao.module.agent.controller.admin.country.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 代购服务国家分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CountryPageReqVO extends PageParam {

    @Schema(description = "国家编码")
    private String code;

    @Schema(description = "中文名称")
    private String nameZh;

    @Schema(description = "英文名称")
    private String nameEn;

    @Schema(description = "法语名称")
    private String nameFr;

    @Schema(description = "德语名称")
    private String nameDe;

    @Schema(description = "西班牙语名称")
    private String nameEs;

    @Schema(description = "阿拉伯语名称")
    private String nameAr;

    @Schema(description = "开启状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}