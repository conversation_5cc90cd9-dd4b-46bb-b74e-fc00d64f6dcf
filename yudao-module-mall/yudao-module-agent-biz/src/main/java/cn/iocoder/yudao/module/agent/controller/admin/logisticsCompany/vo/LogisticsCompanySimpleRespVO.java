package cn.iocoder.yudao.module.agent.controller.admin.logisticsCompany.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 代购物流公司精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsCompanySimpleRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "物流公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "苹果")
    private String name;
}
