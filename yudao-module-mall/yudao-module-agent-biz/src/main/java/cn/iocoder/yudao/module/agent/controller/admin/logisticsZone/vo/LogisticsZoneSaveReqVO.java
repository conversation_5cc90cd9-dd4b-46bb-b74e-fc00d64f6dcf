package cn.iocoder.yudao.module.agent.controller.admin.logisticsZone.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 代购物流国家分区新增/修改 Request VO")
@Data
public class LogisticsZoneSaveReqVO {

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30055")
    private Long id;

    @Schema(description = "国家编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "国家编码不能为空")
    private String countryCode;

    @Schema(description = "产品编号", example = "1601")
    private Long productId;

    @Schema(description = "分区编码")
    private String zoneCode;

    @Schema(description = "分区名称", example = "禁止配送区域")
    private String zoneName;

    @Schema(description = "一级行政区划", example = "California")
    private String stateProvince;

    @Schema(description = "二级行政区划", example = "Los Angeles")
    private String city;

    @Schema(description = "三级行政区划", example = "Beverly Hills")
    private String district;

    @Schema(description = "特殊区域类型", example = "ISLAND")
    private String specialAreaType;

    @Schema(description = "完整区域描述", example = "California Los Angeles Beverly Hills")
    private String fullAreaName;

    @Schema(description = "邮编配置", example = "[\"99500-99999\"]")
    private String postalCodes;

    @Schema(description = "限制类型", example = "FORBIDDEN")
    private String restrictionType;

    @Schema(description = "附加费公式", example = "3*weight_kg,min:48")
    private String feeFormula;

    @Schema(description = "备注说明", example = "阿拉斯加州禁止配送")
    private String remark;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "开启状态不能为空")
    private Integer status;

}