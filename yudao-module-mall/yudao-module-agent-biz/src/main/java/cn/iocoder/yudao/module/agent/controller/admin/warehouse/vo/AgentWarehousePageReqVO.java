package cn.iocoder.yudao.module.agent.controller.admin.warehouse.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 代购平台仓库分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AgentWarehousePageReqVO extends PageParam {

    @Schema(description = "仓库编码")
    private String code;

    @Schema(description = "仓库名称", example = "张三")
    private String name;

    @Schema(description = "开启状态", example = "2")
    private Integer status;

}