package cn.iocoder.yudao.module.agent.service.logisticsProduct;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductPriceDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 代购物流公司产品 Service 接口
 *
 * <AUTHOR>
 */
public interface LogisticsProductService {

    /**
     * 创建代购物流公司产品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLogisticsProduct(@Valid LogisticsProductSaveReqVO createReqVO);

    /**
     * 更新代购物流公司产品
     *
     * @param updateReqVO 更新信息
     */
    void updateLogisticsProduct(@Valid LogisticsProductSaveReqVO updateReqVO);

    /**
     * 删除代购物流公司产品
     *
     * @param id 编号
     */
    void deleteLogisticsProduct(Long id);

    /**
     * 获得代购物流公司产品
     *
     * @param id 编号
     * @return 代购物流公司产品
     */
    LogisticsProductDO getLogisticsProduct(Long id);

    /**
     * 获得代购物流公司产品分页
     *
     * @param pageReqVO 分页查询
     * @return 代购物流公司产品分页
     */
    PageResult<LogisticsProductDO> getLogisticsProductPage(LogisticsProductPageReqVO pageReqVO);

    // ==================== 子表（代购物流产品价格规则） ====================

    /**
     * 获得代购物流产品价格规则分页
     *
     * @param pageReqVO 分页查询
     * @param productId 产品编号
     * @return 代购物流产品价格规则分页
     */
    PageResult<LogisticsProductPriceDO> getLogisticsProductPricePage(PageParam pageReqVO, Long productId);

    /**
     * 创建代购物流产品价格规则
     *
     * @param logisticsProductPrice 创建信息
     * @return 编号
     */
    Long createLogisticsProductPrice(@Valid LogisticsProductPriceDO logisticsProductPrice);

    /**
     * 批量创建代购物流产品价格规则（多国家）
     *
     * @param batchCreateReqVO 批量创建信息
     * @return 批量创建结果
     */
    LogisticsProductPriceBatchCreateRespVO batchCreateLogisticsProductPrice(@Valid LogisticsProductPriceBatchCreateReqVO batchCreateReqVO);

    /**
     * 更新代购物流产品价格规则
     *
     * @param logisticsProductPrice 更新信息
     */
    void updateLogisticsProductPrice(@Valid LogisticsProductPriceDO logisticsProductPrice);

    /**
     * 删除代购物流产品价格规则
     *
     * @param id 编号
     */
    void deleteLogisticsProductPrice(Long id);

	/**
	 * 获得代购物流产品价格规则
	 *
	 * @param id 编号
     * @return 代购物流产品价格规则
	 */
    LogisticsProductPriceDO getLogisticsProductPrice(Long id);

    /**
     * 导入代购物流产品价格规则
     *
     * @param productId 产品编号
     * @param importPrices 导入的价格规则列表
     * @param isUpdateSupport 是否支持更新，默认为 false
     * @return 导入结果
     */
    LogisticsProductPriceImportRespVO importLogisticsProductPriceList(Long productId, List<LogisticsProductPriceImportExcelVO> importPrices, boolean isUpdateSupport);

    /**
     * 根据产品ID和国家编码查询价格规则
     *
     * @param productId 产品ID
     * @param countryCode 国家编码
     * @return 价格规则
     */
    LogisticsProductPriceDO getLogisticsProductPriceByCountry(Long productId, String countryCode);


    /**
     * 根据编号数组查询
     *
     * @param ids 编号数组
     * @return 代购物流产品列表
     */
    List<LogisticsProductDO> getLogisticsProductListByIds(Set<Long> ids);
}