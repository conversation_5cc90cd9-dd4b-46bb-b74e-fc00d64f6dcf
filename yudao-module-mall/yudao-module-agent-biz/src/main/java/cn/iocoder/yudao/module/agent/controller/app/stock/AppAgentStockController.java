package cn.iocoder.yudao.module.agent.controller.app.stock;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

import cn.iocoder.yudao.module.agent.controller.app.stock.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.stock.AgentStockDO;
import cn.iocoder.yudao.module.agent.service.stock.AgentStockService;

@Tag(name = "用户 APP - 代购库存")
@RestController
@RequestMapping("/agent/stock")
@Validated
public class AppAgentStockController {

    @Resource
    private AgentStockService stockService;

//    @PostMapping("/create")
//    @Operation(summary = "创建代购库存")
//    public CommonResult<Long> createStock(@Valid @RequestBody AppAgentStockSaveReqVO createReqVO) {
//        return success(stockService.createStock(createReqVO));
//    }

//    @PutMapping("/update")
//    @Operation(summary = "更新代购库存")
//    public CommonResult<Boolean> updateStock(@Valid @RequestBody AppAgentStockSaveReqVO updateReqVO) {
//        stockService.updateStock(updateReqVO);
//        return success(true);
//    }

//    @DeleteMapping("/delete")
//    @Operation(summary = "删除代购库存")
//    @Parameter(name = "id", description = "编号", required = true)
//    public CommonResult<Boolean> deleteStock(@RequestParam("id") Long id) {
//        stockService.deleteStock(id);
//        return success(true);
//    }

    @GetMapping("/get")
    @Operation(summary = "获得代购库存")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppAgentStockRespVO> getStock(@RequestParam("id") Long id) {
        AgentStockDO stock = stockService.getStock(id);
        return success(BeanUtils.toBean(stock, AppAgentStockRespVO.class));
    }

    @GetMapping("/get-detail")
    @Operation(summary = "获得代购库存")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppAgentStockDetailRespVO> getStockDetail(@RequestParam("id") Long id) {
        AgentStockDO stock = stockService.getStock(getLoginUserId(), id);
        return success(BeanUtils.toBean(stock, AppAgentStockDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购库存分页")
    public CommonResult<PageResult<AppAgentStockRespVO>> getStockPage(@Valid AppAgentStockPageReqVO pageReqVO) {
        PageResult<AgentStockDO> pageResult = stockService.getStockPageApp(getLoginUserId(), pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppAgentStockRespVO.class));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出代购库存 Excel")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportStockExcel(@Valid AppAgentStockPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<AgentStockDO> list = stockService.getStockPageApp(getLoginUserId(), pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "代购库存.xls", "数据", AppAgentStockRespVO.class,
//                        BeanUtils.toBean(list, AppAgentStockRespVO.class));
//    }

}