package cn.iocoder.yudao.module.agent.service.logisticsCompany;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.agent.controller.admin.logisticsCompany.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsCompany.LogisticsCompanyDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 代购物流公司 Service 接口
 *
 * <AUTHOR>
 */
public interface LogisticsCompanyService {

    /**
     * 创建代购物流公司
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLogisticsCompany(@Valid LogisticsCompanySaveReqVO createReqVO);

    /**
     * 更新代购物流公司
     *
     * @param updateReqVO 更新信息
     */
    void updateLogisticsCompany(@Valid LogisticsCompanySaveReqVO updateReqVO);

    /**
     * 删除代购物流公司
     *
     * @param id 编号
     */
    void deleteLogisticsCompany(Long id);

    /**
     * 获得代购物流公司
     *
     * @param id 编号
     * @return 代购物流公司
     */
    LogisticsCompanyDO getLogisticsCompany(Long id);

    /**
     * 获得代购物流公司分页
     *
     * @param pageReqVO 分页查询
     * @return 代购物流公司分页
     */
    PageResult<LogisticsCompanyDO> getLogisticsCompanyPage(LogisticsCompanyPageReqVO pageReqVO);

    /**
     * 获取指定状态的物流公司列表
     *
     * @param status 状态
     * @return  返回物流公司列表
     */
    List<LogisticsCompanyDO> getLogisticsCompanyListByStatus(Integer status);
}