package cn.iocoder.yudao.module.agent.controller.admin.parcel.vo;

import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelItemDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@Schema(description = "管理后台 - 代购包裹新增/修改 Request VO")
@Data
public class AgentParcelSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "28896")
    private Long id;

    @Schema(description = "流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "流水号不能为空")
    private String no;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3180")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "包裹状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "包裹状态不能为空")
    private Integer status;

    @Schema(description = "包裹商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "14008")
    @NotNull(message = "包裹商品数量不能为空")
    private Integer productCount;

    @Schema(description = "取消类型", example = "1")
    private Integer cancelType;

    @Schema(description = "平台备注", example = "你猜")
    private String remark;

    @Schema(description = "是否评价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "是否评价不能为空")
    private Boolean commentStatus;

    @Schema(description = "推广人编号", example = "6443")
    private Long brokerageUserId;

    @Schema(description = "支付订单编号", example = "1746")
    private Long payOrderId;

    @Schema(description = "支付订单编号集合")
    private String payOrderIds;

    @Schema(description = "是否支付", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "是否支付不能为空")
    private Boolean payStatus;

    @Schema(description = "订单支付时间")
    private LocalDateTime payTime;

    @Schema(description = "支付成功的支付渠道")
    private String payChannelCode;

    @Schema(description = "包裹完成时间")
    private LocalDateTime finishTime;

    @Schema(description = "包裹取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "订单原价", requiredMode = Schema.RequiredMode.REQUIRED, example = "12778")
    @NotNull(message = "订单原价不能为空")
    private Integer totalPrice;

    @Schema(description = "订单优惠", requiredMode = Schema.RequiredMode.REQUIRED, example = "442")
    @NotNull(message = "订单优惠不能为空")
    private Integer discountPrice;

    @Schema(description = "运费金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "17163")
    @NotNull(message = "运费金额不能为空")
    private Integer deliveryPrice;

    @Schema(description = "保险金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "11327")
    @NotNull(message = "保险金额不能为空")
    private Integer insurancePrice;

    @Schema(description = "服务金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "8890")
    @NotNull(message = "服务金额不能为空")
    private Integer servicePrice;

    @Schema(description = "订单调价", requiredMode = Schema.RequiredMode.REQUIRED, example = "24474")
    @NotNull(message = "订单调价不能为空")
    private Integer adjustPrice;

    @Schema(description = "应付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "13447")
    @NotNull(message = "应付金额不能为空")
    private Integer payPrice;

    @Schema(description = "商品重量")
    private BigDecimal weight;

    @Schema(description = "商品体积")
    private BigDecimal volume;

    @Schema(description = "长")
    private BigDecimal length;

    @Schema(description = "宽")
    private BigDecimal width;

    @Schema(description = "高")
    private BigDecimal height;

    @Schema(description = "包装重量")
    private BigDecimal packingWeight;

    @Schema(description = "配送类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "配送类型不能为空")
    private Integer deliveryType;

    @Schema(description = "物流方案编号", example = "27258")
    private Long transportPlanId;

    @Schema(description = "物流公司编号", example = "14569")
    private Long transportCompanyId;

    @Schema(description = "物流单号")
    private String transportNo;

    @Schema(description = "物流跟踪")
    private String transportTrack;

    @Schema(description = "海关申报内容")
    private String declareContent;

    @Schema(description = "海关申报价值")
    private Integer declareValue;

    @Schema(description = "清关代码")
    private String clearanceCode;

    @Schema(description = "保险服务数组")
    private String insuranceServices;

    @Schema(description = "免费服务数组")
    private String freeServices;

    @Schema(description = "收费服务数组")
    private String chargeServices;

    @Schema(description = "发货时间")
    private LocalDateTime deliveryTime;

    @Schema(description = "收货时间")
    private LocalDateTime receiveTime;

    @Schema(description = "收件人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "收件人名称不能为空")
    private String receiverName;

    @Schema(description = "收件人手机", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "收件人手机不能为空")
    private String receiverMobile;

    @Schema(description = "收件人地区编号", example = "13235")
    private Integer receiverAreaId;

    @Schema(description = "收件人详细地址")
    private String receiverDetailAddress;

    @Schema(description = "收件人手机", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "收件人手机不能为空")
    private String receiverPhoneCode;

    @Schema(description = "邮编", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "邮编不能为空")
    private String receiverPostCode;

    @Schema(description = "国家编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "国家编码不能为空")
    private String receiverCountryCode;

    @Schema(description = "售后状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "售后状态不能为空")
    private Integer refundStatus;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "28591")
    @NotNull(message = "退款金额不能为空")
    private Integer refundPrice;

    @Schema(description = "优惠劵编号", example = "1861")
    private Long couponId;

    @Schema(description = "优惠劵减免金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "21069")
    @NotNull(message = "优惠劵减免金额不能为空")
    private Integer couponPrice;

    @Schema(description = "使用的积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "使用的积分不能为空")
    private Integer usePoint;

    @Schema(description = "积分抵扣的金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "16912")
    @NotNull(message = "积分抵扣的金额不能为空")
    private Integer pointPrice;

    @Schema(description = "赠送的积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "赠送的积分不能为空")
    private Integer givePoint;

    @Schema(description = "VIP 减免金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "20539")
    @NotNull(message = "VIP 减免金额不能为空")
    private Integer vipPrice;

    @Schema(description = "赠送的优惠劵")
    private String giveCouponTemplateCounts;

    @Schema(description = "赠送的优惠劵编号")
    private String giveCouponIds;

    @Schema(description = "代购包裹明细列表")
    private List<AgentParcelItemDO> parcelItems;

}