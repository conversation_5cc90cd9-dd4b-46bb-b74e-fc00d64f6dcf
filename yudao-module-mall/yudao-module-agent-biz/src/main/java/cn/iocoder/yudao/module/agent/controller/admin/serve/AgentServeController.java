package cn.iocoder.yudao.module.agent.controller.admin.serve;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.admin.serve.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.serve.AgentServeDO;
import cn.iocoder.yudao.module.agent.service.serve.AgentServeService;

@Tag(name = "管理后台 - 代购服务项目")
@RestController
@RequestMapping("/agent/serve")
@Validated
public class AgentServeController {

    @Resource
    private AgentServeService serveService;

    @PostMapping("/create")
    @Operation(summary = "创建代购服务项目")
    @PreAuthorize("@ss.hasPermission('agent:serve:create')")
    public CommonResult<Long> createServe(@Valid @RequestBody AgentServeSaveReqVO createReqVO) {
        return success(serveService.createServe(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购服务项目")
    @PreAuthorize("@ss.hasPermission('agent:serve:update')")
    public CommonResult<Boolean> updateServe(@Valid @RequestBody AgentServeSaveReqVO updateReqVO) {
        serveService.updateServe(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购服务项目")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:serve:delete')")
    public CommonResult<Boolean> deleteServe(@RequestParam("id") Long id) {
        serveService.deleteServe(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购服务项目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:serve:query')")
    public CommonResult<AgentServeRespVO> getServe(@RequestParam("id") Long id) {
        AgentServeDO serve = serveService.getServe(id);
        return success(BeanUtils.toBean(serve, AgentServeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购服务项目分页")
    @PreAuthorize("@ss.hasPermission('agent:serve:query')")
    public CommonResult<PageResult<AgentServeRespVO>> getServePage(@Valid AgentServePageReqVO pageReqVO) {
        PageResult<AgentServeDO> pageResult = serveService.getServePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgentServeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购服务项目 Excel")
    @PreAuthorize("@ss.hasPermission('agent:serve:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportServeExcel(@Valid AgentServePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentServeDO> list = serveService.getServePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购服务项目.xls", "数据", AgentServeRespVO.class,
                        BeanUtils.toBean(list, AgentServeRespVO.class));
    }

}