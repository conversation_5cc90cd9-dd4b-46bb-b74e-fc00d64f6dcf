package cn.iocoder.yudao.module.agent.controller.app.serve;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.agent.controller.app.serve.vo.AppAgentServeRespVO;
import cn.iocoder.yudao.module.agent.dal.dataobject.serve.AgentServeDO;
import cn.iocoder.yudao.module.agent.service.serve.AgentServeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLanguage;

@Tag(name = "用户 APP - 代购服务项目")
@RestController
@RequestMapping("/agent/serve")
@Validated
public class AppAgentServeController {

    @Resource
    private AgentServeService serveService;



    @GetMapping("/get")
    @Operation(summary = "获得代购服务项目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppAgentServeRespVO> getServe(@RequestParam("id") Long id) {
        AgentServeDO serve = serveService.getServe(id);
        return success(BeanUtils.toBean(serve, AppAgentServeRespVO.class));
    }

    //@GetMapping("/page")
    //@Operation(summary = "获得代购服务项目分页")
    //public CommonResult<PageResult<AppAgentServeRespVO>> getServePage(@Valid AppAgentServePageReqVO pageReqVO) {
    //    PageResult<AgentServeDO> pageResult = serveService.getServePage(pageReqVO);
    //    return success(BeanUtils.toBean(pageResult, AppAgentServeRespVO.class));
    //}

    @GetMapping("/simple-list")
    @Operation(summary = "获得代购服务项目简单列表")
    public CommonResult<List<AppAgentServeRespVO>> getServeSimpleList(Integer type) {
        // 直接调用服务层方法，已包含多语言处理
        List<AppAgentServeRespVO> list = serveService.getServeList(type, getCurrentLanguage().getLanguage());
        return success(list);
    }



}