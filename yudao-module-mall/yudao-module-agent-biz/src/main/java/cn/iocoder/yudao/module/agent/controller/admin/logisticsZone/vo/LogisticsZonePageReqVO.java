package cn.iocoder.yudao.module.agent.controller.admin.logisticsZone.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 代购物流国家分区分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LogisticsZonePageReqVO extends PageParam {

    @Schema(description = "国家编码")
    private String countryCode;

    @Schema(description = "产品编号", example = "1601")
    private Long productId;

    @Schema(description = "分区编码")
    private String zoneCode;

    @Schema(description = "分区名称", example = "禁止配送区域")
    private String zoneName;

    @Schema(description = "一级行政区划", example = "California")
    private String stateProvince;

    @Schema(description = "二级行政区划", example = "Los Angeles")
    private String city;

    @Schema(description = "特殊区域类型", example = "ISLAND")
    private String specialAreaType;

    @Schema(description = "完整区域描述", example = "California Los Angeles")
    private String fullAreaName;

    @Schema(description = "限制类型", example = "FORBIDDEN")
    private String restrictionType;

    @Schema(description = "开启状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}