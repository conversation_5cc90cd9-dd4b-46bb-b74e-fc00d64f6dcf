package cn.iocoder.yudao.module.agent.controller.admin.parcel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 代购包裹 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AgentParcelRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "28896")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("流水号")
    private String no;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3180")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "包裹状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "包裹状态", converter = DictConvert.class)
    @DictFormat("agent_parcel_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "平台备注", example = "你猜")
    @ExcelProperty("平台备注")
    private String remark;

    @Schema(description = "是否评价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("是否评价")
    private Boolean commentStatus;

    @Schema(description = "支付订单编号", example = "1746")
    @ExcelProperty("支付订单编号")
    private Long payOrderId;

    @Schema(description = "是否支付", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("是否支付")
    private Boolean payStatus;

    @Schema(description = "支付成功的支付渠道")
    @ExcelProperty("支付成功的支付渠道")
    private String payChannelCode;

    @Schema(description = "包裹完成时间")
    @ExcelProperty("包裹完成时间")
    private LocalDateTime finishTime;

    @Schema(description = "包裹取消时间")
    @ExcelProperty("包裹取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "订单原价", requiredMode = Schema.RequiredMode.REQUIRED, example = "12778")
    @ExcelProperty("订单原价")
    private Integer totalPrice;

    @Schema(description = "订单优惠", requiredMode = Schema.RequiredMode.REQUIRED, example = "442")
    @ExcelProperty("订单优惠")
    private Integer discountPrice;

    @Schema(description = "运费金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "17163")
    @ExcelProperty("运费金额")
    private Integer deliveryPrice;

    @Schema(description = "保险金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "11327")
    @ExcelProperty("保险金额")
    private Integer insurancePrice;

    @Schema(description = "服务金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "8890")
    @ExcelProperty("服务金额")
    private Integer servicePrice;

    @Schema(description = "订单调价", requiredMode = Schema.RequiredMode.REQUIRED, example = "24474")
    @ExcelProperty("订单调价")
    private Integer adjustPrice;

    @Schema(description = "应付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "13447")
    @ExcelProperty("应付金额")
    private Integer payPrice;

    @Schema(description = "商品重量")
    @ExcelProperty("商品重量")
    private BigDecimal weight;

    @Schema(description = "商品体积")
    @ExcelProperty("商品体积")
    private BigDecimal volume;

    @Schema(description = "包装重量")
    @ExcelProperty("包装重量")
    private BigDecimal packingWeight;

    @Schema(description = "配送类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("配送类型")
    private Integer deliveryType;

    @Schema(description = "物流方案编号", example = "27258")
    @ExcelProperty("物流方案编号")
    private Long transportPlanId;

    @Schema(description = "物流公司编号", example = "14569")
    @ExcelProperty("物流公司编号")
    private Long transportCompanyId;

    @Schema(description = "物流单号")
    @ExcelProperty("物流单号")
    private String transportNo;

    @Schema(description = "海关申报价值")
    @ExcelProperty("海关申报价值")
    private Integer declareValue;

    @Schema(description = "发货时间")
    @ExcelProperty("发货时间")
    private LocalDateTime deliveryTime;

    @Schema(description = "收货时间")
    @ExcelProperty("收货时间")
    private LocalDateTime receiveTime;

    @Schema(description = "收件人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("收件人名称")
    private String receiverName;

    @Schema(description = "售后状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("售后状态")
    private Integer refundStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}