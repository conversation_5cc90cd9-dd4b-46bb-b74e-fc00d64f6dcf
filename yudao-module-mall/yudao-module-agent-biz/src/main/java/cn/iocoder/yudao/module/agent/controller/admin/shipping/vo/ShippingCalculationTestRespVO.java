package cn.iocoder.yudao.module.agent.controller.admin.shipping.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 运费计算测试 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 运费计算测试 Response VO")
@Data
public class ShippingCalculationTestRespVO {

    @Schema(description = "是否可配送", example = "true")
    private Boolean canShip;

    @Schema(description = "基础运费(分)", example = "1500")
    private BigDecimal baseFee;

    @Schema(description = "挂号费(分)", example = "500")
    private BigDecimal registrationFee;

    @Schema(description = "操作费(分)", example = "200")
    private BigDecimal operationFee;

    @Schema(description = "服务费(分)", example = "300")
    private BigDecimal serviceFee;

    @Schema(description = "清关费(分)", example = "0")
    private BigDecimal customsFee;

    @Schema(description = "偏远地区费(分)", example = "800")
    private BigDecimal remoteFee;

    @Schema(description = "总费用(分)", example = "3300")
    private BigDecimal totalFee;

    @Schema(description = "限制类型", example = "NORMAL")
    private String restrictionType;

    @Schema(description = "配送说明", example = "正常配送")
    private String deliveryNote;

    @Schema(description = "是否使用阶梯配置中的挂号费", example = "true")
    private Boolean useTieredRegistrationFee;

    @Schema(description = "匹配的分区信息")
    private ZoneInfo matchedZone;

    @Schema(description = "尺寸检查结果")
    private SizeCheckInfo sizeCheck;

    @Schema(description = "分区信息")
    @Data
    public static class ZoneInfo {
        @Schema(description = "分区编码", example = "REMOTE")
        private String zoneCode;

        @Schema(description = "分区名称", example = "偏远地区")
        private String zoneName;

        @Schema(description = "限制类型", example = "REMOTE_FEE")
        private String restrictionType;

        @Schema(description = "费用公式", example = "8*weight_kg,min:50")
        private String feeFormula;

        @Schema(description = "备注", example = "夏威夷偏远费")
        private String remark;
    }

    @Schema(description = "尺寸检查信息")
    @Data
    public static class SizeCheckInfo {
        @Schema(description = "是否通过尺寸检查", example = "true")
        private Boolean passed;

        @Schema(description = "检查结果说明", example = "尺寸符合要求")
        private String message;

        @Schema(description = "最大长度限制(cm)", example = "120")
        private BigDecimal maxLength;

        @Schema(description = "最大宽度限制(cm)", example = "60")
        private BigDecimal maxWidth;

        @Schema(description = "最大高度限制(cm)", example = "60")
        private BigDecimal maxHeight;

        @Schema(description = "最大周长限制(cm)", example = "300")
        private BigDecimal maxGirth;
    }

}
