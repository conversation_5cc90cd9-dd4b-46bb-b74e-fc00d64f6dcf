package cn.iocoder.yudao.module.agent.controller.admin.logisticsCompany.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 代购物流公司 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LogisticsCompanyRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18318")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "物流公司编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物流公司编码")
    private String code;

    @Schema(description = "物流公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("物流公司名称")
    private String name;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}