package cn.iocoder.yudao.module.agent.service.warehouse;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.agent.controller.admin.warehouse.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.warehouse.AgentWarehouseDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 代购平台仓库 Service 接口
 *
 * <AUTHOR>
 */
public interface AgentWarehouseService {

    /**
     * 创建代购平台仓库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWarehouse(@Valid AgentWarehouseSaveReqVO createReqVO);

    /**
     * 更新代购平台仓库
     *
     * @param updateReqVO 更新信息
     */
    void updateWarehouse(@Valid AgentWarehouseSaveReqVO updateReqVO);

    /**
     * 删除代购平台仓库
     *
     * @param id 编号
     */
    void deleteWarehouse(Long id);

    /**
     * 获得代购平台仓库
     *
     * @param id 编号
     * @return 代购平台仓库
     */
    AgentWarehouseDO getWarehouse(Long id);

    /**
     * 获得代购平台仓库分页
     *
     * @param pageReqVO 分页查询
     * @return 代购平台仓库分页
     */
    PageResult<AgentWarehouseDO> getWarehousePage(AgentWarehousePageReqVO pageReqVO);

    //------------------------------APP---------------------------------------------------------------------------------

    /**
     * 获得代购平台仓库列表, 用于 Excel 导出
     *
     * @return 代购平台仓库列表
     */
    List<AgentWarehouseDO> getWarehouseList();

    /**
     * 获得代购平台仓库列表 根据状态
     *
     * @return 代购平台仓库列表
     */
    List<AgentWarehouseDO> getWarehouseListByStatus(Integer status);
}