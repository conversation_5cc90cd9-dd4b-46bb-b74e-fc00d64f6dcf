package cn.iocoder.yudao.module.agent.controller.admin.transfer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 代购转运单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AgentTransferRespVO {

    @Schema(description = "转运单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16496")
    @ExcelProperty("转运单编号")
    private Long id;

    @Schema(description = "转运单流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("转运单流水号")
    private String no;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19625")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "发货物流公司编号", example = "32487")
    @ExcelProperty("发货物流公司编号")
    private Long logisticsId;

    @Schema(description = "物流公司单号")
    @ExcelProperty("物流公司单号")
    private String logisticsNo;

    @Schema(description = "转运的商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "17170")
    @ExcelProperty("转运的商品数量")
    private Integer itemCount;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("agent_transfer_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}