package cn.iocoder.yudao.module.agent.controller.admin.shipping.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 运费计算测试 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 运费计算测试 Request VO")
@Data
public class ShippingCalculationTestReqVO {

    @Schema(description = "物流产品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "物流产品ID不能为空")
    private Long productId;

    @Schema(description = "目标国家编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "US")
    @NotBlank(message = "目标国家编码不能为空")
    private String countryCode;

    @Schema(description = "重量(kg)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1.5")
    @NotNull(message = "重量不能为空")
    @Positive(message = "重量必须大于0")
    private BigDecimal weight;

    @Schema(description = "长度(cm)", example = "30")
    private BigDecimal length;

    @Schema(description = "宽度(cm)", example = "20")
    private BigDecimal width;

    @Schema(description = "高度(cm)", example = "10")
    private BigDecimal height;

    @Schema(description = "邮编", example = "10001")
    private String postalCode;

    @Schema(description = "州/省", example = "New York")
    private String stateProvince;

    @Schema(description = "城市", example = "New York")
    private String city;

}
