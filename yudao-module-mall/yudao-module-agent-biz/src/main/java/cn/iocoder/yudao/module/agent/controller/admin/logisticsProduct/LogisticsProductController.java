package cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.math.BigDecimal;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.Parameters;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.module.agent.enums.logistics.AgentLogisticsPriceTypeEnum;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductPriceDO;
import cn.iocoder.yudao.module.agent.service.logisticsProduct.LogisticsProductService;

@Tag(name = "管理后台 - 代购物流公司产品")
@RestController
@RequestMapping("/agent/logistics-product")
@Validated
public class LogisticsProductController {

    @Resource
    private LogisticsProductService logisticsProductService;

    @PostMapping("/create")
    @Operation(summary = "创建代购物流公司产品")
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:create')")
    public CommonResult<Long> createLogisticsProduct(@Valid @RequestBody LogisticsProductSaveReqVO createReqVO) {
        return success(logisticsProductService.createLogisticsProduct(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购物流公司产品")
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:update')")
    public CommonResult<Boolean> updateLogisticsProduct(@Valid @RequestBody LogisticsProductSaveReqVO updateReqVO) {
        logisticsProductService.updateLogisticsProduct(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购物流公司产品")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:delete')")
    public CommonResult<Boolean> deleteLogisticsProduct(@RequestParam("id") Long id) {
        logisticsProductService.deleteLogisticsProduct(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购物流公司产品")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:query')")
    public CommonResult<LogisticsProductRespVO> getLogisticsProduct(@RequestParam("id") Long id) {
        LogisticsProductDO logisticsProduct = logisticsProductService.getLogisticsProduct(id);
        return success(BeanUtils.toBean(logisticsProduct, LogisticsProductRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购物流公司产品分页")
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:query')")
    public CommonResult<PageResult<LogisticsProductRespVO>> getLogisticsProductPage(@Valid LogisticsProductPageReqVO pageReqVO) {
        PageResult<LogisticsProductDO> pageResult = logisticsProductService.getLogisticsProductPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LogisticsProductRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购物流公司产品 Excel")
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLogisticsProductExcel(@Valid LogisticsProductPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LogisticsProductDO> list = logisticsProductService.getLogisticsProductPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购物流公司产品.xls", "数据", LogisticsProductRespVO.class,
                        BeanUtils.toBean(list, LogisticsProductRespVO.class));
    }

    // ==================== 子表（代购物流产品价格规则） ====================

    @GetMapping("/logistics-product-price/page")
    @Operation(summary = "获得代购物流产品价格规则分页")
    @Parameter(name = "productId", description = "产品编号")
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:query')")
    public CommonResult<PageResult<LogisticsProductPriceDO>> getLogisticsProductPricePage(PageParam pageReqVO,
                                                                                        @RequestParam("productId") Long productId) {
        return success(logisticsProductService.getLogisticsProductPricePage(pageReqVO, productId));
    }

    @PostMapping("/logistics-product-price/create")
    @Operation(summary = "创建代购物流产品价格规则")
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:create')")
    public CommonResult<LogisticsProductPriceBatchCreateRespVO> createLogisticsProductPrice(@Valid @RequestBody LogisticsProductPriceBatchCreateReqVO createReqVO) {
        return success(logisticsProductService.batchCreateLogisticsProductPrice(createReqVO));
    }

    @PutMapping("/logistics-product-price/update")
    @Operation(summary = "更新代购物流产品价格规则")
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:update')")
    public CommonResult<Boolean> updateLogisticsProductPrice(@Valid @RequestBody LogisticsProductPriceDO logisticsProductPrice) {
        logisticsProductService.updateLogisticsProductPrice(logisticsProductPrice);
        return success(true);
    }

    @DeleteMapping("/logistics-product-price/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除代购物流产品价格规则")
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:delete')")
    public CommonResult<Boolean> deleteLogisticsProductPrice(@RequestParam("id") Long id) {
        logisticsProductService.deleteLogisticsProductPrice(id);
        return success(true);
    }

	@GetMapping("/logistics-product-price/get")
	@Operation(summary = "获得代购物流产品价格规则")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:query')")
	public CommonResult<LogisticsProductPriceDO> getLogisticsProductPrice(@RequestParam("id") Long id) {
	    return success(logisticsProductService.getLogisticsProductPrice(id));
	}

    @GetMapping("/logistics-product-price/get-import-template")
    @Operation(summary = "获得代购物流产品价格规则导入模板")
    public void getLogisticsProductPriceImportTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<LogisticsProductPriceImportExcelVO> list = Arrays.asList(
                LogisticsProductPriceImportExcelVO.builder()
                        .countryCode("US").zoneCode("").transitTime("12-20天")
                        .chargeType("WEIGHT").priceType(AgentLogisticsPriceTypeEnum.INCREMENTAL.getCode())
                        .firstUnit(500).firstPrice(10500).additionalUnit(200).additionalPrice(2940)
                        .minWeight(100).maxWeight(30000).fuelFeeRate(new BigDecimal("0.15"))
                        .registrationFee(0).operationFee(0).serviceFee(0).customsFee(0)
                        .prepayTariff(false).tariffRate(new BigDecimal("0.00")).build(),
                LogisticsProductPriceImportExcelVO.builder()
                        .countryCode("AU").zoneCode("Zone1").transitTime("15-25天")
                        .chargeType("WEIGHT").priceType(AgentLogisticsPriceTypeEnum.TIERED.getCode())
                        .firstUnit(500).firstPrice(12000).tieredPrices("[{\"tierStart\":0,\"tierEnd\":500,\"unitPrice\":12000},{\"tierStart\":501,\"tierEnd\":1000,\"unitPrice\":8000},{\"tierStart\":1001,\"tierEnd\":null,\"unitPrice\":6000}]")
                        .minWeight(100).maxWeight(20000).fuelFeeRate(new BigDecimal("0.12"))
                        .registrationFee(500).operationFee(200).serviceFee(300).customsFee(0)
                        .prepayTariff(true).tariffRate(new BigDecimal("0.10")).build()
        );
        // 输出
        ExcelUtils.write(response, "代购物流产品价格规则导入模板.xls", "价格规则列表", LogisticsProductPriceImportExcelVO.class, list);
    }

    @PostMapping("/logistics-product-price/import")
    @Operation(summary = "导入代购物流产品价格规则")
    @Parameters({
            @Parameter(name = "productId", description = "产品编号", required = true),
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('agent:logistics-product:import')")
    public CommonResult<LogisticsProductPriceImportRespVO> importLogisticsProductPriceExcel(
            @RequestParam("productId") Long productId,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<LogisticsProductPriceImportExcelVO> list = ExcelUtils.read(file, LogisticsProductPriceImportExcelVO.class);
        return success(logisticsProductService.importLogisticsProductPriceList(productId, list, updateSupport));
    }

}