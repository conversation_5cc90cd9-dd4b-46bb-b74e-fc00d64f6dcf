package cn.iocoder.yudao.module.agent.controller.admin.parcel.vo;

import cn.iocoder.yudao.module.agent.controller.admin.base.member.user.MemberUserRespVO;
import cn.iocoder.yudao.module.agent.controller.admin.base.product.property.ProductPropertyValueDetailRespVO;
import cn.iocoder.yudao.module.agent.controller.admin.base.server.AgentParcelServerVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 管理后台 - 包裹分页项 Response VO
 * @author: DingXiao
 * @create: 2025-05-30 09:39
 **/
@Data
public class AgentParcelPageItemRespVO extends AgentParcelBaseVO{

    @Schema(description = "收件人地区名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "上海 上海市 普陀区")
    private String receiverAreaName;

    @Schema(description = "订单项列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Item> items;

    @Schema(description = "用户信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private MemberUserRespVO user;

    @Schema(description = "推广人信息")
    private MemberUserRespVO brokerageUser;

    @Schema(description = "保险服务")
    private List<Server>  insuranceServerList;

    @Schema(description = "免费服务")
    private List<Server>  freeServerList;

    @Schema(description = "收费服务")
    private List<Server>  chargeServerList;


    @Data
    public static class Item extends AgentParcelItemBaseVO {

        @Schema(description = "属性列表", requiredMode = Schema.RequiredMode.REQUIRED)
        private List<ProductPropertyValueDetailRespVO> properties;

    }

    public static class Server extends AgentParcelServerVO {

    }
}
