package cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@Schema(description = "管理后台 - 代购物流公司产品新增/修改 Request VO")
@Data
public class LogisticsProductSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11367")
    private Long id;

    @Schema(description = "物流公司编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "28989")
    @NotNull(message = "物流公司编号不能为空")
    private Long companyId;

    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "产品编码不能为空")
    private String productCode;

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "中文名称不能为空")
    private String nameZh;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "英文名称不能为空")
    private String nameEn;

    @Schema(description = "法语名称")
    private String nameFr;

    @Schema(description = "德语名称")
    private String nameDe;

    @Schema(description = "西班牙语名称")
    private String nameEs;

    @Schema(description = "阿拉伯语名称")
    private String nameAr;

    @Schema(description = "中文特性描述")
    private String featuresZh;

    @Schema(description = "英文特性描述")
    private String featuresEn;

    @Schema(description = "法语特性描述")
    private String featuresFr;

    @Schema(description = "德语特性描述")
    private String featuresDe;

    @Schema(description = "西班牙语特性描述")
    private String featuresEs;

    @Schema(description = "阿拉伯语特性描述")
    private String featuresAr;

    @Schema(description = "产品图标", example = "https://www.iocoder.cn")
    private String iconUrl;

    @Schema(description = "是否包税", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否包税不能为空")
    private Boolean taxInclude;

    @Schema(description = "是否计算体积重", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否计算体积重不能为空")
    private Boolean needVolumeCal;

    @Schema(description = "体积重计算基数")
    private Integer volumeBase;

    @Schema(description = "最小重量(g)")
    private Integer minWeight;

    @Schema(description = "最大重量(g)")
    private Integer maxWeight;

    @Schema(description = "中文尺寸限制描述")
    private String dimensionRestrictionZh;

    @Schema(description = "英文尺寸限制描述")
    private String dimensionRestrictionEn;

    @Schema(description = "法语尺寸限制描述")
    private String dimensionRestrictionFr;

    @Schema(description = "德语尺寸限制描述")
    private String dimensionRestrictionDe;

    @Schema(description = "西班牙尺寸限制描述")
    private String dimensionRestrictionEs;

    @Schema(description = "阿拉伯尺寸限制描述")
    private String dimensionRestrictionAr;

    @Schema(description = "中文体积重量计费规则描述")
    private String volumeWeightRuleZh;

    @Schema(description = "英文体积重量计费规则描述")
    private String volumeWeightRuleEn;

    @Schema(description = "法语体积重量计费规则描述")
    private String volumeWeightRuleFr;

    @Schema(description = "德语体积重量计费规则描述")
    private String volumeWeightRuleDe;

    @Schema(description = "西班牙体积重量计费规则描述")
    private String volumeWeightRuleEs;

    @Schema(description = "阿拉伯体积重量计费规则描述")
    private String volumeWeightRuleAr;

    @Schema(description = "最小申报价值(分)")
    private BigDecimal minDeclareValue;

    @Schema(description = "最大申报价值(分)")
    private BigDecimal maxDeclareValue;

    @Schema(description = "默认申报类型", example = "1")
    private String defaultDeclareType;

    @Schema(description = "每公斤申报价值(分)")
    private BigDecimal declarePerKg;

    @Schema(description = "申报比例")
    private BigDecimal declareRatio;

    @Schema(description = "免费保险", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "免费保险不能为空")
    private Boolean freeInsure;

    @Schema(description = "IOSS", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "IOSS不能为空")
    private Boolean iossEnabled;

    @Schema(description = "计算公式表达式")
    private String calculationFormula;

    @Schema(description = "分类限制配置")
    private String categoryRestrictions;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}