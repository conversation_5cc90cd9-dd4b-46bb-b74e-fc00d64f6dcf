package cn.iocoder.yudao.module.agent.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 代购商品分类新增/修改 Request VO")
@Data
public class AgentCategorySaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11550")
    private Long id;

    @Schema(description = "父编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31032")
    @NotNull(message = "父编号不能为空")
    private Long parentId;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "编码不能为空")
    private String code;

    @Schema(description = "分类图", example = "https://www.iocoder.cn")
    private String picUrl;

    @Schema(description = "中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "中文名称不能为空")
    private String nameZh;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "英文名称不能为空")
    private String nameEn;

    @Schema(description = "法语名称")
    private String nameFr;

    @Schema(description = "德语名称")
    private String nameDe;

    @Schema(description = "西班牙语名称")
    private String nameEs;

    @Schema(description = "阿拉伯语名称")
    private String nameAr;

    @Schema(description = "中文备注")
    private String remarkZh;

    @Schema(description = "英文备注")
    private String remarkEn;

    @Schema(description = "法语备注")
    private String remarkFr;

    @Schema(description = "德语备注")
    private String remarkDe;

    @Schema(description = "西班牙语备注")
    private String remarkEs;

    @Schema(description = "阿拉伯语备注")
    private String remarkAr;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "热门")
    private Boolean hot;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

}