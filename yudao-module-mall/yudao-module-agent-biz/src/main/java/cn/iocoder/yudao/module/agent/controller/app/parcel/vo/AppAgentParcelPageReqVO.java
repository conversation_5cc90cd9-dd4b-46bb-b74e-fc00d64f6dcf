package cn.iocoder.yudao.module.agent.controller.app.parcel.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户 APP - 代购包裹分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppAgentParcelPageReqVO extends PageParam {

    @Schema(description = "流水号")
    private String no;

    @Schema(description = "用户编号", example = "3180")
    private Long userId;

    @Schema(description = "包裹状态", example = "1")
    private Integer status;

    @Schema(description = "推广人编号", example = "6443")
    private Long brokerageUserId;

    @Schema(description = "支付订单编号", example = "1746")
    private Long payOrderId;

    @Schema(description = "是否支付", example = "2")
    private Boolean payStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}