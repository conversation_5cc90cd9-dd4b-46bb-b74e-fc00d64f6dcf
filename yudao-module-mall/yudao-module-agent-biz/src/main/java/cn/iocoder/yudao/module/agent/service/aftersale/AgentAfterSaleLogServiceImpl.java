package cn.iocoder.yudao.module.agent.service.aftersale;


import cn.iocoder.yudao.module.agent.convert.aftersale.AfterSaleLogConvert;
import cn.iocoder.yudao.module.agent.dal.dataobject.aftersale.AgentAfterSaleLogDO;
import cn.iocoder.yudao.module.agent.dal.mysql.aftersale.AgentAfterSaleLogMapper;
import cn.iocoder.yudao.module.agent.service.aftersale.bo.AfterSaleLogCreateReqBO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 交易售后日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AgentAfterSaleLogServiceImpl implements AgentAfterSaleLogService {

    @Resource
    private AgentAfterSaleLogMapper agentAfterSaleLogMapper;

    @Override
    public void createAfterSaleLog(AfterSaleLogCreateReqBO createReqBO) {
        AgentAfterSaleLogDO afterSaleLog = AfterSaleLogConvert.INSTANCE.convert(createReqBO);
        agentAfterSaleLogMapper.insert(afterSaleLog);
    }

    @Override
    public List<AgentAfterSaleLogDO> getAfterSaleLogList(Long afterSaleId) {
        return agentAfterSaleLogMapper.selectListByAfterSaleId(afterSaleId);
    }

}
