package cn.iocoder.yudao.module.agent.controller.admin.shipping;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.agent.controller.admin.shipping.vo.ShippingCalculationTestReqVO;
import cn.iocoder.yudao.module.agent.controller.admin.shipping.vo.ShippingCalculationTestRespVO;
import cn.iocoder.yudao.module.agent.controller.admin.shipping.vo.ShippingQuoteReqVO;
import cn.iocoder.yudao.module.agent.controller.admin.shipping.vo.ShippingQuoteRespVO;
import cn.iocoder.yudao.module.agent.service.shipping.ShippingCalculationService;
import cn.iocoder.yudao.module.agent.service.shipping.ShippingCalculationService.ShippingCalculationResult;
import cn.iocoder.yudao.module.agent.service.shipping.ShippingQuoteService;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingCalculationReqBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingQuoteRespBO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductPriceDO;
import cn.iocoder.yudao.module.agent.service.logisticsProduct.LogisticsProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 运费计算控制器
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 运费计算")
@RestController
@RequestMapping("/agent/shipping-calculation")
@Validated
@Slf4j
public class ShippingCalculationController {

    @Resource
    private ShippingCalculationService shippingCalculationService;

    @Resource
    private LogisticsProductService logisticsProductService;

    @Resource
    private ShippingQuoteService shippingQuoteService;

    @PostMapping("/quote")
    @Operation(summary = "运费查询")
    @PreAuthorize("@ss.hasPermission('agent:shipping:quote')")
    public CommonResult<List<ShippingQuoteRespVO>> getShippingQuotes(@Valid @RequestBody ShippingQuoteReqVO reqVO) {
        log.info("=== 管理端运费查询 ===");
        log.info("国家: {}, 重量: {}g, 分类: {}", reqVO.getCountryCode(), reqVO.getWeight(), reqVO.getCategoryIds());

        try {
            // 转换为内部请求对象
            ShippingCalculationReqBO reqBO = BeanUtils.toBean(reqVO, ShippingCalculationReqBO.class);

            // 调用Service层获取BO
            List<ShippingQuoteRespBO> boQuotes = shippingQuoteService.getShippingQuotes(reqBO);

            // 转换为VO（BO转VO）
            List<ShippingQuoteRespVO> voQuotes = convertBOToAdminResponse(boQuotes);

            log.info("管理端运费查询完成: 找到{}个方案", voQuotes.size());
            return success(voQuotes);

        } catch (Exception e) {
            log.error("管理端运费查询失败", e);
            return success(new ArrayList<>());
        }
    }

    @PostMapping("/test")
    @Operation(summary = "运费计算测试")
    @PreAuthorize("@ss.hasPermission('agent:shipping:calculate')")
    public CommonResult<ShippingCalculationTestRespVO> testShippingCalculation(@Valid @RequestBody ShippingCalculationTestReqVO reqVO) {
        log.info("=== 运费计算测试 ===");
        log.info("产品ID: {}, 国家: {}, 重量: {}kg", reqVO.getProductId(), reqVO.getCountryCode(), reqVO.getWeight());

        try {
            // 调用运费计算服务
            ShippingCalculationResult result = shippingCalculationService.calculateShipping(
                    reqVO.getCountryCode(),
                    reqVO.getPostalCode(),
                    reqVO.getProductId(),
                    reqVO.getWeight(),
                    reqVO.getLength() != null ? reqVO.getLength() : BigDecimal.ZERO,
                    reqVO.getWidth() != null ? reqVO.getWidth() : BigDecimal.ZERO,
                    reqVO.getHeight() != null ? reqVO.getHeight() : BigDecimal.ZERO
            );

            // 获取详细的费用信息
            DetailedFeeResult feeResult = getDetailedFeeResult(reqVO);

            // 构建响应
            ShippingCalculationTestRespVO respVO = new ShippingCalculationTestRespVO();
            respVO.setCanShip(result.isCanShip());
            respVO.setBaseFee(result.getBaseFee());
            respVO.setRemoteFee(result.getRemoteFee());
            respVO.setTotalFee(result.getTotalFee());
            respVO.setRestrictionType(result.getRestrictionType());
            respVO.setDeliveryNote(result.getRestrictionMessage());

            // 设置详细费用信息
            if (feeResult != null) {
                respVO.setRegistrationFee(feeResult.getRegistrationFee());
                respVO.setOperationFee(feeResult.getOperationFee());
                respVO.setServiceFee(feeResult.getServiceFee());
                respVO.setCustomsFee(feeResult.getCustomsFee());
                respVO.setUseTieredRegistrationFee(feeResult.isUseTieredRegistrationFee());
            }

            // 设置分区信息
            if (result.getMatchedZone() != null) {
                ShippingCalculationTestRespVO.ZoneInfo zoneInfo = new ShippingCalculationTestRespVO.ZoneInfo();
                zoneInfo.setZoneCode(result.getMatchedZone().getZoneCode());
                zoneInfo.setZoneName(result.getMatchedZone().getZoneName());
                zoneInfo.setRestrictionType(result.getMatchedZone().getRestrictionType());
                zoneInfo.setFeeFormula(result.getMatchedZone().getFeeFormula());
                zoneInfo.setRemark(result.getMatchedZone().getRemark());
                respVO.setMatchedZone(zoneInfo);
            }

            // 设置尺寸检查信息
            if (result.getSizeCheckResult() != null) {
                ShippingCalculationTestRespVO.SizeCheckInfo sizeCheckInfo = new ShippingCalculationTestRespVO.SizeCheckInfo();
                sizeCheckInfo.setPassed(result.getSizeCheckResult().isPassed());
                sizeCheckInfo.setMessage(result.getSizeCheckResult().getErrorMessage());
                // 可以添加更多尺寸限制信息
                respVO.setSizeCheck(sizeCheckInfo);
            }

            log.info("运费计算完成: 可配送={}, 总费用={}分", respVO.getCanShip(), respVO.getTotalFee());
            return success(respVO);

        } catch (Exception e) {
            log.error("运费计算失败", e);
            
            // 返回错误信息
            ShippingCalculationTestRespVO errorResp = new ShippingCalculationTestRespVO();
            errorResp.setCanShip(false);
            errorResp.setDeliveryNote("运费计算失败: " + e.getMessage());
            errorResp.setBaseFee(BigDecimal.ZERO);
            errorResp.setTotalFee(BigDecimal.ZERO);
            
            return success(errorResp);
        }
    }

    /**
     * 详细费用结果类
     */
    public static class DetailedFeeResult {
        private BigDecimal baseFee = BigDecimal.ZERO;
        private BigDecimal registrationFee = BigDecimal.ZERO;
        private BigDecimal operationFee = BigDecimal.ZERO;
        private BigDecimal serviceFee = BigDecimal.ZERO;
        private BigDecimal customsFee = BigDecimal.ZERO;
        private boolean useTieredRegistrationFee = false;

        // Getters and Setters
        public BigDecimal getBaseFee() { return baseFee; }
        public void setBaseFee(BigDecimal baseFee) { this.baseFee = baseFee; }
        public BigDecimal getRegistrationFee() { return registrationFee; }
        public void setRegistrationFee(BigDecimal registrationFee) { this.registrationFee = registrationFee; }
        public BigDecimal getOperationFee() { return operationFee; }
        public void setOperationFee(BigDecimal operationFee) { this.operationFee = operationFee; }
        public BigDecimal getServiceFee() { return serviceFee; }
        public void setServiceFee(BigDecimal serviceFee) { this.serviceFee = serviceFee; }
        public BigDecimal getCustomsFee() { return customsFee; }
        public void setCustomsFee(BigDecimal customsFee) { this.customsFee = customsFee; }
        public boolean isUseTieredRegistrationFee() { return useTieredRegistrationFee; }
        public void setUseTieredRegistrationFee(boolean useTieredRegistrationFee) { this.useTieredRegistrationFee = useTieredRegistrationFee; }
    }

    /**
     * 获取详细的费用结果
     */
    private DetailedFeeResult getDetailedFeeResult(ShippingCalculationTestReqVO reqVO) {
        try {
            // 获取价格规则
            LogisticsProductPriceDO productPrice = logisticsProductService.getLogisticsProductPriceByCountry(
                    reqVO.getProductId(), reqVO.getCountryCode());
            
            if (productPrice == null) {
                return null;
            }

            // 创建费用结果对象
            DetailedFeeResult result = new DetailedFeeResult();
            
            // 转换重量为克
            Integer weightGrams = reqVO.getWeight().multiply(new BigDecimal(1000)).intValue();

            // 根据价格类型计算费用
            if ("TIERED".equals(productPrice.getPriceType()) && 
                cn.hutool.core.util.StrUtil.isNotBlank(productPrice.getTieredPrices())) {
                
                // 使用阶梯价格计算
                cn.iocoder.yudao.module.agent.util.TieredPriceCalculator.TieredPriceResult tieredResult = 
                    cn.iocoder.yudao.module.agent.util.TieredPriceCalculator.calculateTieredPrice(
                        productPrice.getTieredPrices(), 
                        weightGrams, 
                        productPrice.getRegistrationFee()
                    );
                
                result.setBaseFee(tieredResult.getBaseFee());
                result.setRegistrationFee(tieredResult.getRegistrationFee());
                result.setUseTieredRegistrationFee(tieredResult.isUseTieredRegistrationFee());
            } else {
                // 使用传统计算
                if (productPrice.getRegistrationFee() != null) {
                    result.setRegistrationFee(new BigDecimal(productPrice.getRegistrationFee()));
                }
            }

            // 设置其他费用
            if (productPrice.getOperationFee() != null) {
                result.setOperationFee(new BigDecimal(productPrice.getOperationFee()));
            }
            if (productPrice.getServiceFee() != null) {
                result.setServiceFee(new BigDecimal(productPrice.getServiceFee()));
            }
            if (productPrice.getCustomsFee() != null) {
                result.setCustomsFee(new BigDecimal(productPrice.getCustomsFee()));
            }

            return result;

        } catch (Exception e) {
            log.error("获取详细费用信息失败", e);
            return null;
        }
    }

    /**
     * 将BO转换为管理端响应VO
     */
    private List<ShippingQuoteRespVO> convertBOToAdminResponse(List<ShippingQuoteRespBO> boQuotes) {
        List<ShippingQuoteRespVO> voQuotes = new ArrayList<>();

        for (ShippingQuoteRespBO boQuote : boQuotes) {
            ShippingQuoteRespVO voQuote = new ShippingQuoteRespVO();

            // 复制基础字段
            voQuote.setId(boQuote.getProductId() != null ? boQuote.getProductId().toString() : null);
            voQuote.setName(boQuote.getName());
            voQuote.setIconUrl(boQuote.getIconUrl());
            voQuote.setFeatures(boQuote.getFeatures());
            voQuote.setTransitTime(boQuote.getTransitTime());
            voQuote.setTaxInclude(boQuote.getTaxInclude());
            voQuote.setLineTips(boQuote.getLineTips());
            voQuote.setAvailable(boQuote.getAvailable());
            voQuote.setUnavailableReason(boQuote.getUnavailableReason());
            voQuote.setSort(boQuote.getSort());
            voQuote.setMinDeclareValue(boQuote.getMinDeclareValue());
            voQuote.setMaxDeclareValue(boQuote.getMaxDeclareValue());
            voQuote.setDefaultDeclareType(boQuote.getDefaultDeclareType());
            voQuote.setDeclarePerKg(boQuote.getDeclarePerKg());
            voQuote.setDeclareRatio(boQuote.getDeclareRatio() != null ? boQuote.getDeclareRatio().toString() : null);
            voQuote.setIossEnabled(boQuote.getIossEnabled());
            voQuote.setFreeInsure(boQuote.getFreeInsure());

            // 转换费用详情（Integer转String）
            if (boQuote.getFeeDetail() != null) {
                ShippingQuoteRespVO.FeeDetail voFeeDetail = new ShippingQuoteRespVO.FeeDetail();
                ShippingQuoteRespBO.FeeDetail boFeeDetail = boQuote.getFeeDetail();

                voFeeDetail.setWeight(boFeeDetail.getWeight());
                voFeeDetail.setLength(boFeeDetail.getLength());
                voFeeDetail.setWidth(boFeeDetail.getWidth());
                voFeeDetail.setHeight(boFeeDetail.getHeight());
                voFeeDetail.setVolumeWeight(boFeeDetail.getVolumeWeight());
                voFeeDetail.setChargeableWeight(boFeeDetail.getChargeableWeight());
                voFeeDetail.setNeedVolumeCal(boFeeDetail.getNeedVolumeCal());
                voFeeDetail.setVolumeBase(boFeeDetail.getVolumeBase());
                voFeeDetail.setWeightFirst(boFeeDetail.getWeightFirst());
                voFeeDetail.setWeightContinue(boFeeDetail.getWeightContinue());

                // 费用转换：Integer（分）转String（分）
                voFeeDetail.setTotal(boFeeDetail.getTotal() != null ? boFeeDetail.getTotal().toString() : "0");
                voFeeDetail.setFreight(boFeeDetail.getFreight() != null ? boFeeDetail.getFreight().toString() : "0");
                voFeeDetail.setCustomsFee(boFeeDetail.getCustomsFee() != null ? boFeeDetail.getCustomsFee().toString() : "0");
                voFeeDetail.setFuelFee(boFeeDetail.getFuelFee() != null ? boFeeDetail.getFuelFee().toString() : "0");
                voFeeDetail.setAirSurcharge(boFeeDetail.getAirSurcharge() != null ? boFeeDetail.getAirSurcharge().toString() : "0");
                voFeeDetail.setOperationFee(boFeeDetail.getOperationFee() != null ? boFeeDetail.getOperationFee().toString() : "0");
                voFeeDetail.setServiceFee(boFeeDetail.getServiceFee() != null ? boFeeDetail.getServiceFee().toString() : "0");
                voFeeDetail.setFeeFirst(boFeeDetail.getFeeFirst() != null ? boFeeDetail.getFeeFirst().toString() : "0");
                voFeeDetail.setFeeContinue(boFeeDetail.getFeeContinue() != null ? boFeeDetail.getFeeContinue().toString() : "0");
                voFeeDetail.setAdditionalFee(boFeeDetail.getAdditionalFee() != null ? boFeeDetail.getAdditionalFee().toString() : "0");
                voFeeDetail.setDiscount(boFeeDetail.getDiscount() != null ? boFeeDetail.getDiscount().toString() : "0");
                voFeeDetail.setOriginalTotal(boFeeDetail.getOriginalTotal() != null ? boFeeDetail.getOriginalTotal().toString() : null);
                voFeeDetail.setCurrentTotal(boFeeDetail.getCurrentTotal() != null ? boFeeDetail.getCurrentTotal().toString() : voFeeDetail.getTotal());

                voQuote.setFeeDetail(voFeeDetail);
            }

            // 转换限制信息
            if (boQuote.getRestrictions() != null) {
                ShippingQuoteRespVO.Restrictions voRestrictions = new ShippingQuoteRespVO.Restrictions();
                ShippingQuoteRespBO.Restrictions boRestrictions = boQuote.getRestrictions();

                voRestrictions.setMinWeight(boRestrictions.getMinWeight());
                voRestrictions.setMaxWeight(boRestrictions.getMaxWeight());
                voRestrictions.setDimensionRestriction(boRestrictions.getDimensionRestriction());
                voRestrictions.setVolumeWeightRule(boRestrictions.getVolumeWeightRule());
                voRestrictions.setCategoryRestrictions(boRestrictions.getCategoryRestrictions());

                voQuote.setRestrictions(voRestrictions);
            }

            voQuotes.add(voQuote);
        }

        return voQuotes;
    }

}
