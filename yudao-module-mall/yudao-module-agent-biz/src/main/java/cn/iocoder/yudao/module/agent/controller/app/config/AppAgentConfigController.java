package cn.iocoder.yudao.module.agent.controller.app.config;

import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.agent.controller.app.config.vo.AppAgentConfigRespVO;
import cn.iocoder.yudao.module.agent.convert.config.AgentConfigConvert;
import cn.iocoder.yudao.module.agent.dal.dataobject.config.AgentConfigDO;
import cn.iocoder.yudao.module.agent.service.config.AgentConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 代购配置")
@RestController
@RequestMapping("/agent/config")
@RequiredArgsConstructor
@Validated
@Slf4j
public class AppAgentConfigController {

    @Resource
    private AgentConfigService agentConfigService;

    @GetMapping("/get")
    @Operation(summary = "获得代购配置")
    @PermitAll
    public CommonResult<AppAgentConfigRespVO> getAgentConfig() {
        AgentConfigDO config = ObjUtil.defaultIfNull(agentConfigService.getAgentConfig(), new AgentConfigDO());
        return success(AgentConfigConvert.INSTANCE.convert02(config));
    }

}
