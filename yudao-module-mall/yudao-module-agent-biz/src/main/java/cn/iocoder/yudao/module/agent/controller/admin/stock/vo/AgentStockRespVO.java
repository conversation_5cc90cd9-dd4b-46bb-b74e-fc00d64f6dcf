package cn.iocoder.yudao.module.agent.controller.admin.stock.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 代购仓库 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AgentStockRespVO {

    @Schema(description = "品牌编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "5362")
    @ExcelProperty("品牌编号")
    private Long id;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30558")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "spu编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10218")
    @ExcelProperty("spu编号")
    private Long spuId;

    @Schema(description = "商品 SPU 名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("商品 SPU 名称")
    private String spuName;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31305")
    @ExcelProperty("商品 SKU 编号")
    private Long skuId;

    @Schema(description = "商品图片", example = "https://www.iocoder.cn")
    @ExcelProperty("商品图片")
    private String picUrl;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "11391")
    @ExcelProperty("库存数量")
    private Integer count;

    @Schema(description = "商品重量")
    @ExcelProperty("商品重量")
    private Double weight;

    @Schema(description = "商品体积")
    @ExcelProperty("商品体积")
    private Double volume;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "入库日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入库日期")
    private LocalDateTime inTime;

    @Schema(description = "到期日期")
    @ExcelProperty("到期日期")
    private LocalDateTime expiredTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    //位置
    private String location;

}