package cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 代购物流产品价格规则导入 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 代购物流产品价格规则导入 Response VO")
@Data
@Builder
public class LogisticsProductPriceImportRespVO {

    @Schema(description = "创建成功的数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer createSuccessCount;

    @Schema(description = "更新成功的数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer updateSuccessCount;

    @Schema(description = "导入失败的数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer failureCount;

    @Schema(description = "失败数据")
    private List<Map<String, Object>> failureData;

}
