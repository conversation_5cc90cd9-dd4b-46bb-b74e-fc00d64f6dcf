package cn.iocoder.yudao.module.agent.controller.admin.country;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.agent.controller.admin.logisticsCompany.vo.LogisticsCompanySimpleRespVO;
import cn.iocoder.yudao.module.agent.convert.logisticsCompany.LogisticsCompanyConvert;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsCompany.LogisticsCompanyDO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.agent.controller.admin.country.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.country.AgentCountryDO;
import cn.iocoder.yudao.module.agent.service.country.AgentCountryService;

@Tag(name = "管理后台 - 代购服务国家")
@RestController
@RequestMapping("/agent/country")
@Validated
public class AgentCountryController {

    @Resource
    private AgentCountryService agentCountryService;

    @PostMapping("/create")
    @Operation(summary = "创建代购服务国家")
    @PreAuthorize("@ss.hasPermission('agent:country:create')")
    public CommonResult<Long> createCountry(@Valid @RequestBody CountrySaveReqVO createReqVO) {
        return success(agentCountryService.createCountry(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代购服务国家")
    @PreAuthorize("@ss.hasPermission('agent:country:update')")
    public CommonResult<Boolean> updateCountry(@Valid @RequestBody CountrySaveReqVO updateReqVO) {
        agentCountryService.updateCountry(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代购服务国家")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:country:delete')")
    public CommonResult<Boolean> deleteCountry(@RequestParam("id") Long id) {
        agentCountryService.deleteCountry(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代购服务国家")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:country:query')")
    public CommonResult<CountryRespVO> getCountry(@RequestParam("id") Long id) {
        AgentCountryDO country = agentCountryService.getCountry(id);
        return success(BeanUtils.toBean(country, CountryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代购服务国家分页")
    @PreAuthorize("@ss.hasPermission('agent:country:query')")
    public CommonResult<PageResult<CountryRespVO>> getCountryPage(@Valid CountryPageReqVO pageReqVO) {
        PageResult<AgentCountryDO> pageResult = agentCountryService.getCountryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CountryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代购服务国家 Excel")
    @PreAuthorize("@ss.hasPermission('agent:country:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCountryExcel(@Valid CountryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentCountryDO> list = agentCountryService.getCountryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代购服务国家.xls", "数据", CountryRespVO.class,
                        BeanUtils.toBean(list, CountryRespVO.class));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获取国家精简信息列表", description = "主要用于前端的下拉选项")
    public CommonResult<List<CountrySimpleRespVO>> getSimpleCountryList() {
        // 获取品牌列表，只要开启状态的
        List<CountrySimpleRespVO> list = agentCountryService.getCountrySimpleListByStatus(CommonStatusEnum.ENABLE.getStatus());
        // 排序后，返回给前端
        return success(list);
    }

}