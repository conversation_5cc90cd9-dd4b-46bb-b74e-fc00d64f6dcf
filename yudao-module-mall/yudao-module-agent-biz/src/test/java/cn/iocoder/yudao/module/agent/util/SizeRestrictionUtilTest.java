package cn.iocoder.yudao.module.agent.util;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 尺寸限制工具类测试
 *
 * <AUTHOR>
 */
class SizeRestrictionUtilTest {

    @Test
    void testParseSizeRestrictions() {
        // 测试正常的JSON配置
        String json = "{\"maxLength\":120,\"maxWidth\":60,\"maxHeight\":60,\"maxGirth\":300,\"maxSingleSide\":120}";
        SizeRestrictionUtil.SizeRestriction restriction = SizeRestrictionUtil.parseSizeRestrictions(json);
        
        assertNotNull(restriction);
        assertEquals(new BigDecimal("120"), restriction.getMaxLength());
        assertEquals(new BigDecimal("60"), restriction.getMaxWidth());
        assertEquals(new BigDecimal("60"), restriction.getMaxHeight());
        assertEquals(new BigDecimal("300"), restriction.getMaxGirth());
        assertEquals(new BigDecimal("120"), restriction.getMaxSingleSide());
    }

    @Test
    void testParseSizeRestrictions_EmptyJson() {
        // 测试空配置
        SizeRestrictionUtil.SizeRestriction restriction = SizeRestrictionUtil.parseSizeRestrictions("");
        assertNull(restriction);
        
        restriction = SizeRestrictionUtil.parseSizeRestrictions(null);
        assertNull(restriction);
    }

    @Test
    void testCheckSize_Normal() {
        // 测试正常尺寸
        String json = "{\"maxLength\":120,\"maxWidth\":60,\"maxHeight\":60,\"maxGirth\":300}";
        SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSize(
            new BigDecimal("100"), new BigDecimal("50"), new BigDecimal("40"), json);
        
        assertTrue(result.isPassed());
        assertNull(result.getErrorMessage());
    }

    @Test
    void testCheckSize_ExceedLength() {
        // 测试超过长度限制
        String json = "{\"maxLength\":120,\"maxWidth\":60,\"maxHeight\":60}";
        SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSize(
            new BigDecimal("130"), new BigDecimal("50"), new BigDecimal("40"), json);
        
        assertFalse(result.isPassed());
        assertTrue(result.getErrorMessage().contains("长度"));
        assertEquals("EXCEED_MAX_LENGTH", result.getErrorCode());
    }

    @Test
    void testCheckSize_ExceedWidth() {
        // 测试超过宽度限制
        String json = "{\"maxLength\":120,\"maxWidth\":60,\"maxHeight\":60}";
        SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSize(
            new BigDecimal("100"), new BigDecimal("70"), new BigDecimal("40"), json);
        
        assertFalse(result.isPassed());
        assertTrue(result.getErrorMessage().contains("宽度"));
        assertEquals("EXCEED_MAX_WIDTH", result.getErrorCode());
    }

    @Test
    void testCheckSize_ExceedHeight() {
        // 测试超过高度限制
        String json = "{\"maxLength\":120,\"maxWidth\":60,\"maxHeight\":60}";
        SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSize(
            new BigDecimal("100"), new BigDecimal("50"), new BigDecimal("70"), json);
        
        assertFalse(result.isPassed());
        assertTrue(result.getErrorMessage().contains("高度"));
        assertEquals("EXCEED_MAX_HEIGHT", result.getErrorCode());
    }

    @Test
    void testCheckSize_ExceedGirth() {
        // 测试超过周长限制
        String json = "{\"maxGirth\":200}";
        SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSize(
            new BigDecimal("80"), new BigDecimal("70"), new BigDecimal("60"), json);
        
        assertFalse(result.isPassed());
        assertTrue(result.getErrorMessage().contains("周长"));
        assertEquals("EXCEED_MAX_GIRTH", result.getErrorCode());
    }

    @Test
    void testCheckSize_ExceedSingleSide() {
        // 测试超过单边限制
        String json = "{\"maxSingleSide\":100}";
        SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSize(
            new BigDecimal("110"), new BigDecimal("50"), new BigDecimal("40"), json);
        
        assertFalse(result.isPassed());
        assertTrue(result.getErrorMessage().contains("最大边长"));
        assertEquals("EXCEED_MAX_SINGLE_SIDE", result.getErrorCode());
    }

    @Test
    void testCheckSize_ExceedVolume() {
        // 测试超过体积限制
        String json = "{\"maxVolume\":100000}";
        SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSize(
            new BigDecimal("60"), new BigDecimal("50"), new BigDecimal("40"), json);
        
        assertFalse(result.isPassed());
        assertTrue(result.getErrorMessage().contains("体积"));
        assertEquals("EXCEED_MAX_VOLUME", result.getErrorCode());
    }

    @Test
    void testCheckSize_InvalidInput() {
        // 测试无效输入
        String json = "{\"maxLength\":120}";
        
        // 空尺寸
        SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSize(
            null, new BigDecimal("50"), new BigDecimal("40"), json);
        assertFalse(result.isPassed());
        assertEquals("INCOMPLETE_SIZE", result.getErrorCode());
        
        // 负数尺寸
        result = SizeRestrictionUtil.checkSize(
            new BigDecimal("-10"), new BigDecimal("50"), new BigDecimal("40"), json);
        assertFalse(result.isPassed());
        assertEquals("INVALID_SIZE", result.getErrorCode());
    }

    @Test
    void testCheckSize_NoRestriction() {
        // 测试无限制配置
        SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSize(
            new BigDecimal("1000"), new BigDecimal("500"), new BigDecimal("400"), "");
        
        assertTrue(result.isPassed());
        assertNull(result.getErrorMessage());
    }

    @Test
    void testIsValidSizeRestrictions() {
        // 测试配置验证
        assertTrue(SizeRestrictionUtil.isValidSizeRestrictions("{\"maxLength\":120}"));
        assertTrue(SizeRestrictionUtil.isValidSizeRestrictions(""));
        assertTrue(SizeRestrictionUtil.isValidSizeRestrictions(null));
        assertFalse(SizeRestrictionUtil.isValidSizeRestrictions("{invalid json}"));
    }

    @Test
    void testGenerateSizeDescription() {
        // 测试描述生成
        String json = "{\"maxLength\":120,\"maxWidth\":60,\"maxHeight\":60,\"maxGirth\":300,\"maxSingleSide\":120}";
        String description = SizeRestrictionUtil.generateSizeDescription(json);
        
        assertTrue(description.contains("最大尺寸"));
        assertTrue(description.contains("长120"));
        assertTrue(description.contains("宽60"));
        assertTrue(description.contains("高60"));
        assertTrue(description.contains("最大单边: 120"));
        assertTrue(description.contains("最大周长: 300"));
        
        // 测试空配置
        String emptyDescription = SizeRestrictionUtil.generateSizeDescription("");
        assertEquals("无尺寸限制", emptyDescription);
    }

    @Test
    void testEuropeanZoneRestrictions() {
        // 测试欧洲分区的实际限制场景
        
        // 法国0区限制 (假设更严格)
        String franceZone0 = "{\"maxLength\":100,\"maxWidth\":50,\"maxHeight\":50,\"maxGirth\":250}";
        
        // 欧洲1-3区限制
        String europeZone123 = "{\"maxLength\":120,\"maxWidth\":60,\"maxHeight\":60,\"maxGirth\":300}";
        
        // 测试符合法国0区的包裹
        SizeRestrictionUtil.SizeCheckResult result1 = SizeRestrictionUtil.checkSize(
            new BigDecimal("90"), new BigDecimal("40"), new BigDecimal("30"), franceZone0);
        assertTrue(result1.isPassed());
        
        // 测试超过法国0区但符合欧洲1-3区的包裹
        SizeRestrictionUtil.SizeCheckResult result2 = SizeRestrictionUtil.checkSize(
            new BigDecimal("110"), new BigDecimal("55"), new BigDecimal("50"), franceZone0);
        assertFalse(result2.isPassed());
        
        SizeRestrictionUtil.SizeCheckResult result3 = SizeRestrictionUtil.checkSize(
            new BigDecimal("110"), new BigDecimal("55"), new BigDecimal("50"), europeZone123);
        assertTrue(result3.isPassed());
    }
}
