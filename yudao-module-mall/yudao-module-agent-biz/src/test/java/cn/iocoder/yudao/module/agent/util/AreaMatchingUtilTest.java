package cn.iocoder.yudao.module.agent.util;

import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsZone.LogisticsZoneDO;
import cn.iocoder.yudao.module.agent.enums.LogisticsRestrictionTypeEnum;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 区域匹配工具类测试
 *
 * <AUTHOR>
 */
class AreaMatchingUtilTest {

    @Test
    void testFindBestMatch_PostalCodePriority() {
        // 准备测试数据
        List<LogisticsZoneDO> zones = Arrays.asList(
            createZone("US", "California", null, null, null, "[\"90210\"]", "REMOTE_FEE"),
            createZone("US", "California", "Los Angeles", null, null, null, "NORMAL"),
            createZone("US", "California", null, null, null, null, "NORMAL")
        );

        // 邮编匹配应该优先
        LogisticsZoneDO result = AreaMatchingUtil.findBestMatch(zones, "90210", "California", "Los Angeles", null);
        
        assertNotNull(result);
        assertEquals("REMOTE_FEE", result.getRestrictionType());
        assertEquals("California", result.getStateProvince());
    }

    @Test
    void testFindBestMatch_HierarchyMatching() {
        // 准备测试数据
        List<LogisticsZoneDO> zones = Arrays.asList(
            createZone("US", "California", "Los Angeles", "Beverly Hills", null, null, "REMOTE_FEE"),
            createZone("US", "California", "Los Angeles", null, null, null, "NORMAL"),
            createZone("US", "California", null, null, null, null, "NORMAL")
        );

        // 完整层级匹配
        LogisticsZoneDO result1 = AreaMatchingUtil.findBestMatch(zones, null, "California", "Los Angeles", "Beverly Hills");
        assertNotNull(result1);
        assertEquals("REMOTE_FEE", result1.getRestrictionType());
        assertEquals("Beverly Hills", result1.getDistrict());

        // 部分层级匹配
        LogisticsZoneDO result2 = AreaMatchingUtil.findBestMatch(zones, null, "California", "Los Angeles", null);
        assertNotNull(result2);
        assertEquals("NORMAL", result2.getRestrictionType());
        assertEquals("Los Angeles", result2.getCity());
        assertNull(result2.getDistrict());

        // 州级匹配
        LogisticsZoneDO result3 = AreaMatchingUtil.findBestMatch(zones, null, "California", null, null);
        assertNotNull(result3);
        assertEquals("NORMAL", result3.getRestrictionType());
        assertEquals("California", result3.getStateProvince());
        assertNull(result3.getCity());
    }

    @Test
    void testFindBestMatch_SpecialArea() {
        // 准备测试数据
        List<LogisticsZoneDO> zones = Arrays.asList(
            createZone("US", null, null, null, "MILITARY", null, "FORBIDDEN"),
            createZone("US", "Alaska", null, null, null, null, "FORBIDDEN")
        );

        // 特殊区域匹配
        LogisticsZoneDO result = AreaMatchingUtil.findBestMatch(zones, null, "Alaska", null, null);
        assertNotNull(result);
        assertEquals("FORBIDDEN", result.getRestrictionType());
        assertEquals("Alaska", result.getStateProvince());
    }

    @Test
    void testFindBestMatch_NoMatch() {
        // 准备测试数据
        List<LogisticsZoneDO> zones = Arrays.asList(
            createZone("US", "California", null, null, null, null, "NORMAL")
        );

        // 没有匹配的情况
        LogisticsZoneDO result = AreaMatchingUtil.findBestMatch(zones, null, "Texas", null, null);
        assertNull(result);
    }

    @Test
    void testGenerateFullAreaName() {
        // 测试完整区域名称生成
        String result1 = AreaMatchingUtil.generateFullAreaName("California", "Los Angeles", "Beverly Hills", null);
        assertEquals("California Los Angeles Beverly Hills", result1);

        String result2 = AreaMatchingUtil.generateFullAreaName("California", "Los Angeles", null, null);
        assertEquals("California Los Angeles", result2);

        String result3 = AreaMatchingUtil.generateFullAreaName(null, null, null, "MILITARY");
        assertEquals("MILITARY", result3);
    }

    @Test
    void testValidateZoneConfiguration() {
        // 有效配置
        LogisticsZoneDO validZone1 = createZone("US", "California", null, null, null, null, "NORMAL");
        assertTrue(AreaMatchingUtil.validateZoneConfiguration(validZone1));

        LogisticsZoneDO validZone2 = createZone("US", null, null, null, "MILITARY", null, "FORBIDDEN");
        assertTrue(AreaMatchingUtil.validateZoneConfiguration(validZone2));

        // 无效配置
        LogisticsZoneDO invalidZone = new LogisticsZoneDO();
        assertFalse(AreaMatchingUtil.validateZoneConfiguration(invalidZone));

        assertFalse(AreaMatchingUtil.validateZoneConfiguration(null));
    }

    /**
     * 创建测试用的物流分区对象
     */
    private LogisticsZoneDO createZone(String countryCode, String stateProvince, String city, String district,
                                     String specialAreaType, String postalCodes, String restrictionType) {
        LogisticsZoneDO zone = new LogisticsZoneDO();
        zone.setCountryCode(countryCode);
        zone.setStateProvince(stateProvince);
        zone.setCity(city);
        zone.setDistrict(district);
        zone.setSpecialAreaType(specialAreaType);
        zone.setPostalCodes(postalCodes);
        zone.setRestrictionType(restrictionType);
        zone.setFullAreaName(AreaMatchingUtil.generateFullAreaName(stateProvince, city, district, specialAreaType));
        zone.setStatus(1);
        return zone;
    }
}
