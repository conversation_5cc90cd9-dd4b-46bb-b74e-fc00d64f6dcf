# ========== Bestellmodul 1-011-000-000 ==========
trade.order_item.not_found=Bestellposition existiert nicht
trade.order.not_found=Bestellung existiert nicht
trade.order_item.update_after_sale_status_fail=Fehler beim Aktualisieren des After-Sales-Status der Bestellposition, bitte versuchen Sie es erneut
trade.order.update_paid_status_not_unpaid=Fehler beim Aktualisieren des Zahlungsstatus, die Bestellung ist nicht im Status "unbezahlt"
trade.order.update_paid_fail_pay_order_id_error=Fehler beim Aktualisieren des Zahlungsstatus, die Zahlungs-ID stimmt nicht überein
trade.order.update_paid_fail_pay_order_status_not_success=Fehler beim Aktualisieren des Zahlungsstatus, der Zahlungsstatus ist nicht "erfolgreich"
trade.order.update_paid_fail_pay_price_not_match=Fehler beim Aktualisieren des Zahlungsstatus, der Zahlungsbetrag stimmt nicht überein
trade.order.delivery_fail_status_not_undelivered=Fehler beim Versenden der Bestellung, die Bestellung ist nicht im Status "offen zur Lieferung"
trade.order.receive_fail_status_not_delivered=Fehler beim Empfang der Bestellung, die Bestellung ist nicht im Status "geliefert"
trade.order.comment_fail_status_not_completed=Fehler beim Erstellen der Bewertung, die Bestellung ist nicht im Status "abgeschlossen"
trade.order.comment_status_not_false=Fehler beim Erstellen der Bewertung, die Bestellung wurde bereits bewertet
trade.order.delivery_fail_refund_status_not_none=Fehler beim Versenden der Bestellung, die Bestellung wurde bereits (teilweise) erstattet
trade.order.delivery_fail_combination_record_not_success=Fehler beim Versenden der Bestellung, die Gruppenbestellung war nicht erfolgreich
trade.order.delivery_fail_bargain_record_not_success=Fehler beim Versenden der Bestellung, der Preisvorschlag war nicht erfolgreich
trade.order.delivery_fail_delivery_type_not_express=Fehler beim Versenden der Bestellung, der Liefermodus ist nicht "Express"
trade.order.cancel_fail_status_not_unpaid=Fehler beim Stornieren der Bestellung, die Bestellung ist nicht im Status "unbezahlt"
trade.order.update_price_fail_paid=Fehler beim Anpassen des Preises, die Bestellung wurde bereits bezahlt
trade.order.update_price_fail_already=Fehler beim Anpassen des Preises, der Preis wurde bereits geändert
trade.order.update_price_fail_price_error=Fehler beim Anpassen des Preises, der angepasste Preis darf nicht weniger als 0,01 betragen
trade.order.delete_fail_status_not_cancel=Fehler beim Löschen der Bestellung, die Bestellung ist nicht im Status "storniert"
trade.order.receive_fail_delivery_type_not_pick_up=Fehler beim Abholen der Bestellung, die Liefermethode ist nicht "Selbstabholung"
trade.order.update_address_fail_status_not_delivered=Fehler beim Ändern der Lieferadresse, die Bestellung ist nicht im Status "offen zur Lieferung"
trade.order.create_fail_exist_unpaid=Fehler beim Erstellen der Bestellung, es existiert bereits eine unbezahlte Bestellung
trade.order.cancel_paid_fail=Fehler beim Stornieren der Zahlung, die Bestellung ist nicht im Status "{}"

# ========== After-Sales-Modul 1-011-000-100 ==========
trade.after_sale.not_found=After-Sales-Antrag existiert nicht
trade.after_sale.create_fail_refund_price_error=Falscher Rückerstattungsbetrag
trade.after_sale.create_fail_order_status_canceled=Die Bestellung wurde storniert und kann nicht für After-Sales verwendet werden
trade.after_sale.create_fail_order_status_no_paid=Die Bestellung wurde nicht bezahlt und kann nicht für After-Sales verwendet werden
trade.after_sale.create_fail_order_status_no_delivered=Die Bestellung wurde nicht versandt und kann nicht für "Rückgabe und Erstattung" verwendet werden
trade.after_sale.create_fail_order_item_applied=Die Bestellposition wurde bereits für After-Sales beantragt und kann nicht erneut beantragt werden
trade.after_sale.audit_fail_status_not_apply=Genehmigung fehlgeschlagen, der After-Sales-Status ist nicht "in Prüfung"
trade.after_sale.update_status_fail=Fehler bei der Bearbeitung des After-Sales-Antrags, bitte aktualisieren und erneut versuchen
trade.after_sale.delivery_fail_status_not_seller_agree=Fehler beim Rückversand, der After-Sales-Status ist nicht "Warten auf Rücksendung"
trade.after_sale.confirm_fail_status_not_buyer_delivery=Fehler beim Bestätigen des Empfangs, der After-Sales-Status ist nicht "Warten auf Bestätigung"
trade.after_sale.refund_fail_status_not_wait_refund=Rückerstattung fehlgeschlagen, der After-Sales-Status ist nicht "Warten auf Rückerstattung"
trade.after_sale.cancel_fail_status_not_valid=Fehler beim Stornieren des After-Sales-Antrags, der Status ist ungültig
trade.after_sale.create_fail_order_combination_in_progress=Die Gruppenbestellung ist noch im Gange und kann nicht für After-Sales verwendet werden

# ========== Warenkorb-Modul 1-011-002-000 ==========
trade.cart.item_not_found=Warenkorb-Artikel existiert nicht

# ========== Preis-Modul 1-011-003-000 ==========
trade.price.calculate_pay_price_illegal=Fehler bei der Preisberechnung, der Preis muss größer als 0 sein
trade.price.delivery_price_template_not_found=Fehler bei der Berechnung der Versandkosten, keine passende Vorlage gefunden
trade.price.coupon_not_match_normal_order=Gutscheine können nicht für Blitzverkäufe, Gruppenbestellungen oder Preisverhandlungen verwendet werden
trade.price.seckill_total_limit_exceed=Das Produkt im Blitzverkauf überschreitet das Gesamtkauflimit
trade.price.point_total_limit_exceed=Das Punkte-Produkt überschreitet das Gesamtkauflimit
trade.price.delivery_price_type_illegal=Fehler bei der Berechnung der Versandkosten, der Liefermodus ist ungültig
trade.price.coupon_can_not_use=Dieser Gutschein kann nicht verwendet werden, Grund: {}

# ========== Logistikmodul Express 1-011-004-000 ==========
trade.express.not_exists=Das Versandunternehmen existiert nicht
trade.express.code_duplicate=Der Code des Versandunternehmens existiert bereits
trade.express.client_not_provide=Ein Versanddienstleister wie „Kuaidi100“ wird benötigt
trade.express.status_not_enable=Das Versandunternehmen ist nicht aktiviert
trade.express.api_query_error=Fehler bei der Abfrage der Versand-API
trade.express.api_query_failed=Versandabfrage fehlgeschlagen, Grund: {}

# ========== Logistikmodul Vorlage 1-011-005-000 ==========
trade.express_template.name_duplicate=Der Name der Versandvorlage existiert bereits
trade.express_template.not_exists=Die Versandvorlage existiert nicht

# ========== Logistikmodul Selbstabholung 1-011-006-000 ==========
trade.pick_up.store_not_exists=Das Abholgeschäft existiert nicht

# ========== Vertriebspartner-Modul 1-011-007-000 ==========
trade.brokerage.user_not_exists=Vertriebspartner existiert nicht
trade.brokerage.frozen_price_not_enough=Nicht genügend eingefrorene Provision ({})
trade.brokerage.bind_self=Sie können sich nicht selbst binden
trade.brokerage.bind_user_not_enabled=Der gebundene Benutzer ist nicht berechtigt
trade.brokerage.bind_condition_admin=Bindung ist nur im Admin-Panel möglich
trade.brokerage.bind_mode_register=Bindung ist nur bei der Registrierung möglich
trade.brokerage.bind_override=Ein Promoter ist bereits gebunden
trade.brokerage.bind_loop=Ein Untergeordneter kann seinen Vorgesetzten nicht binden
trade.brokerage.level_not_support=Derzeit werden nur Level kleiner oder gleich 2 unterstützt

# ========== Provisionsauszahlungsmodul 1-011-008-000 ==========
trade.brokerage_withdraw.not_exists=Provisionsauszahlungsdatensatz existiert nicht
trade.brokerage_withdraw.status_not_auditing=Der Auszahlungsdatensatz ist nicht in Prüfung
trade.brokerage_withdraw.min_price=Der Auszahlungsbetrag darf nicht unter {} Euro liegen
trade.brokerage_withdraw.balance_not_enough=Sie können bis zu {} Euro auszahlen lassen

# ========== Wunschlisten-Modul 1-011-009-000 ==========
trade.wishlist.not_exists=Der Wunschlisten-Artikel existiert nicht
