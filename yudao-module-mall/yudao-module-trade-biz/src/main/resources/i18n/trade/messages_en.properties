# ========== Order Module 1-011-000-000 ==========
trade.order_item.not_found=Order item does not exist
trade.order.not_found=Order does not exist
trade.order_item.update_after_sale_status_fail=Failed to update after-sale status of the order item, please try again
trade.order.update_paid_status_not_unpaid=Failed to update order payment status, the order is not in "unpaid" status
trade.order.update_paid_fail_pay_order_id_error=Failed to update order payment status, payment order ID does not match
trade.order.update_paid_fail_pay_order_status_not_success=Failed to update order payment status, payment order status is not "successful"
trade.order.update_paid_fail_pay_price_not_match=Failed to update order payment status, payment amount does not match
trade.order.delivery_fail_status_not_undelivered=Failed to deliver the order, the order is not in "to be delivered" status
trade.order.receive_fail_status_not_delivered=Failed to receive the order, the order is not in "to be received" status
trade.order.comment_fail_status_not_completed=Failed to create order item review, the order is not in "completed" status
trade.order.comment_status_not_false=Failed to create order item review, the order has already been reviewed
trade.order.delivery_fail_refund_status_not_none=Failed to deliver the order, the order has been refunded or partially refunded
trade.order.delivery_fail_combination_record_not_success=Failed to deliver the order, group buying was unsuccessful
trade.order.delivery_fail_bargain_record_not_success=Failed to deliver the order, bargaining was unsuccessful
trade.order.delivery_fail_delivery_type_not_express=Failed to deliver the order, delivery type is not express
trade.order.cancel_fail_status_not_unpaid=Failed to cancel the order, the order is not in "to be paid" status
trade.order.update_price_fail_paid=Failed to adjust the payment price, the order has already been paid
trade.order.update_price_fail_already=Failed to adjust the payment price, the price has already been modified
trade.order.update_price_fail_price_error=Failed to adjust the payment price, the adjusted price cannot be less than 0.01
trade.order.delete_fail_status_not_cancel=Failed to delete the order, the order is not in "cancelled" status
trade.order.receive_fail_delivery_type_not_pick_up=Failed to self-pickup the order, the delivery method is not "self-pickup"
trade.order.update_address_fail_status_not_delivered=Failed to update delivery address, the order is not in "to be delivered" status
trade.order.create_fail_exist_unpaid=Failed to create the order, there is an unpaid order
trade.order.cancel_paid_fail=Failed to cancel the order payment, the order is not in "{}" status

# ========== After Sale Module 1-011-000-100 ==========
trade.after_sale.not_found=After-sale order does not exist
trade.after_sale.create_fail_refund_price_error=Refund amount is incorrect
trade.after_sale.create_fail_order_status_canceled=Order has been closed and cannot apply for after-sale
trade.after_sale.create_fail_order_status_no_paid=Order has not been paid and cannot apply for after-sale
trade.after_sale.create_fail_order_status_no_delivered=Order has not been shipped and cannot apply for "refund and return"
trade.after_sale.create_fail_order_item_applied=The order item has already applied for after-sale and cannot apply again
trade.after_sale.audit_fail_status_not_apply=Approval failed, the after-sale status is not "under review"
trade.after_sale.update_status_fail=Failed to process the after-sale order, please refresh and try again
trade.after_sale.delivery_fail_status_not_seller_agree=Failed to return goods, the after-sale status is not "awaiting buyer return"
trade.after_sale.confirm_fail_status_not_buyer_delivery=Failed to confirm receipt, the after-sale status is not "awaiting confirmation"
trade.after_sale.refund_fail_status_not_wait_refund=Refund failed, the after-sale status is not "awaiting refund"
trade.after_sale.cancel_fail_status_not_valid=Failed to cancel the after-sale order, the status is not valid
trade.after_sale.create_fail_order_combination_in_progress=The group order is in progress, cannot apply for after-sale

# ========== Cart Module 1-011-002-000 ==========
trade.cart.item_not_found=Shopping cart item does not exist

# ========== Price Module 1-011-003-000 ==========
trade.price.calculate_pay_price_illegal=Payment price calculation error, the price must be greater than 0
trade.price.delivery_price_template_not_found=Delivery fee calculation error, no matching delivery price template found
trade.price.coupon_not_match_normal_order=Coupons cannot be used for seckill, group buying, or bargaining products
trade.price.seckill_total_limit_exceed=The seckill product exceeds the total purchase limit
trade.price.point_total_limit_exceed=The points product exceeds the total purchase limit
trade.price.delivery_price_type_illegal=Delivery fee calculation error, delivery method does not match
trade.price.coupon_can_not_use=This coupon cannot be used, reason: {}

# ========== Logistics Express Module 1-*********** ==========
trade.express.not_exists=The courier company does not exist
trade.express.code_duplicate=The courier company code already exists
trade.express.client_not_provide=A courier service provider such as "Kuaidi100" is required
trade.express.status_not_enable=The courier company is not enabled
trade.express.api_query_error=Courier query API error
trade.express.api_query_failed=Courier query failed, reason: {}

# ========== Logistics Template Module 1-*********** ==========
trade.express_template.name_duplicate=The delivery template name already exists
trade.express_template.not_exists=The delivery template does not exist

# ========== Logistics PICK_UP Module 1-*********** ==========
trade.pick_up.store_not_exists=Self-pickup store does not exist

# ========== Brokerage User Module 1-*********** ==========
trade.brokerage.user_not_exists=Brokerage user does not exist
trade.brokerage.frozen_price_not_enough=Insufficient frozen commission ({})
trade.brokerage.bind_self=You cannot bind to yourself
trade.brokerage.bind_user_not_enabled=The bound user is not eligible for promotion
trade.brokerage.bind_condition_admin=Binding can only be done in the admin panel
trade.brokerage.bind_mode_register=Binding is only allowed during registration
trade.brokerage.bind_override=A promoter is already bound
trade.brokerage.bind_loop=A subordinate cannot bind to their superior
trade.brokerage.level_not_support=Currently only supports levels less than or equal to 2

# ========== Brokerage Withdrawal Module 1-011-008-000 ==========
trade.brokerage_withdraw.not_exists=Brokerage withdrawal record does not exist
trade.brokerage_withdraw.status_not_auditing=The brokerage withdrawal record is not under review
trade.brokerage_withdraw.min_price=The withdrawal amount cannot be less than {} yuan
trade.brokerage_withdraw.balance_not_enough=You can withdraw up to {} yuan

# ========== Wishlist Module 1-011-009-000 ==========
trade.wishlist.not_exists=The wishlist item does not exist

# ========== Order Module 1-011-010-000 ==========
trade.order_supplement.not_exists = The supplementary order does not exist
trade.order_supplement.order.status.error = The order status is incorrect
trade.order_supplement.paid.can.not.delete = The supplement order already paid, cannot be deleted
