# ========== Module Commande 1-011-000-000 ==========
trade.order_item.not_found=L'article de commande n'existe pas
trade.order.not_found=La commande n'existe pas
trade.order_item.update_after_sale_status_fail=Échec de la mise à jour du statut après-vente de l'article de commande, veuillez réessayer
trade.order.update_paid_status_not_unpaid=Échec de la mise à jour du statut de paiement, la commande n'est pas en statut « impayé »
trade.order.update_paid_fail_pay_order_id_error=Échec de la mise à jour du statut de paiement, l'ID de la commande de paiement ne correspond pas
trade.order.update_paid_fail_pay_order_status_not_success=Échec de la mise à jour du statut de paiement, le statut du paiement n'est pas « réussi »
trade.order.update_paid_fail_pay_price_not_match=Échec de la mise à jour du statut de paiement, le montant payé ne correspond pas
trade.order.delivery_fail_status_not_undelivered=Échec de la livraison, la commande n'est pas en statut « en attente de livraison »
trade.order.receive_fail_status_not_delivered=Échec de la réception, la commande n'est pas en statut « en attente de réception »
trade.order.comment_fail_status_not_completed=Échec de la création de l'avis, la commande n'est pas en statut « terminée »
trade.order.comment_status_not_false=Échec de la création de l'avis, la commande a déjà été évaluée
trade.order.delivery_fail_refund_status_not_none=Échec de la livraison, la commande a été remboursée ou partiellement remboursée
trade.order.delivery_fail_combination_record_not_success=Échec de la livraison, l'achat groupé n'a pas réussi
trade.order.delivery_fail_bargain_record_not_success=Échec de la livraison, la négociation n'a pas réussi
trade.order.delivery_fail_delivery_type_not_express=Échec de la livraison, le mode de livraison n'est pas express
trade.order.cancel_fail_status_not_unpaid=Échec de l'annulation de la commande, la commande n'est pas en statut « en attente de paiement »
trade.order.update_price_fail_paid=Échec de la modification du prix, la commande a déjà été payée
trade.order.update_price_fail_already=Échec de la modification du prix, le prix a déjà été ajusté
trade.order.update_price_fail_price_error=Échec de la modification du prix, le prix ajusté ne peut pas être inférieur à 0,01
trade.order.delete_fail_status_not_cancel=Échec de la suppression de la commande, la commande n'est pas en statut « annulée »
trade.order.receive_fail_delivery_type_not_pick_up=Échec du retrait, le mode de livraison n'est pas « retrait en magasin »
trade.order.update_address_fail_status_not_delivered=Échec de la modification de l'adresse de livraison, la commande n'est pas en statut « en attente de livraison »
trade.order.create_fail_exist_unpaid=Échec de la création de la commande, il existe une commande impayée
trade.order.cancel_paid_fail=Échec de l'annulation du paiement de la commande, la commande n'est pas en statut « {} »

# ========== Module Après-Vente 1-011-000-100 ==========
trade.after_sale.not_found=Le dossier après-vente n'existe pas
trade.after_sale.create_fail_refund_price_error=Le montant du remboursement est incorrect
trade.after_sale.create_fail_order_status_canceled=La commande est annulée et ne peut pas faire l'objet d'un après-vente
trade.after_sale.create_fail_order_status_no_paid=La commande n'a pas été payée et ne peut pas faire l'objet d'un après-vente
trade.after_sale.create_fail_order_status_no_delivered=La commande n'a pas été expédiée et ne peut pas faire l'objet d'un « retour-remboursement »
trade.after_sale.create_fail_order_item_applied=L'article a déjà fait l'objet d'un après-vente et ne peut pas être soumis à nouveau
trade.after_sale.audit_fail_status_not_apply=Échec de l'approbation, le statut après-vente n'est pas « en attente de validation »
trade.after_sale.update_status_fail=Échec du traitement du dossier après-vente, veuillez actualiser et réessayer
trade.after_sale.delivery_fail_status_not_seller_agree=Échec du retour, le statut après-vente n'est pas « en attente du retour de l'acheteur »
trade.after_sale.confirm_fail_status_not_buyer_delivery=Échec de la confirmation de réception, le statut après-vente n'est pas « en attente de confirmation »
trade.after_sale.refund_fail_status_not_wait_refund=Échec du remboursement, le statut après-vente n'est pas « en attente de remboursement »
trade.after_sale.cancel_fail_status_not_valid=Échec de l'annulation du dossier après-vente, le statut n'est pas valide
trade.after_sale.create_fail_order_combination_in_progress=La commande est en cours d'achat groupé et ne peut pas faire l'objet d'un après-vente

# ========== Module Panier 1-011-002-000 ==========
trade.cart.item_not_found=L'article du panier n'existe pas

# ========== Module Prix 1-011-003-000 ==========
trade.price.calculate_pay_price_illegal=Erreur de calcul du prix, le prix doit être supérieur à 0
trade.price.delivery_price_template_not_found=Erreur de calcul des frais de livraison, aucun modèle de frais de livraison correspondant trouvé
trade.price.coupon_not_match_normal_order=Les coupons ne peuvent pas être utilisés pour les produits en vente flash, achats groupés ou négociation
trade.price.seckill_total_limit_exceed=Le produit en vente flash dépasse la limite d'achat totale
trade.price.point_total_limit_exceed=Le produit en points dépasse la limite d'achat totale
trade.price.delivery_price_type_illegal=Erreur de calcul des frais de livraison, le mode de livraison ne correspond pas
trade.price.coupon_can_not_use=Ce coupon ne peut pas être utilisé, raison: {}

# ========== Module Livraison Express 1-011-004-000 ==========
trade.express.not_exists=La société de livraison n'existe pas
trade.express.code_duplicate=Le code de la société de livraison existe déjà
trade.express.client_not_provide=Un fournisseur de services de livraison comme « Kuaidi100 » est requis
trade.express.status_not_enable=La société de livraison n'est pas activée
trade.express.api_query_error=Erreur de l'API de suivi de livraison
trade.express.api_query_failed=Échec du suivi de livraison, raison: {}

# ========== Module Modèle de Livraison 1-011-005-000 ==========
trade.express_template.name_duplicate=Le nom du modèle de frais de livraison existe déjà
trade.express_template.not_exists=Le modèle de frais de livraison n'existe pas

# ========== Module Retrait 1-011-006-000 ==========
trade.pick_up.store_not_exists=Le magasin de retrait n'existe pas

# ========== Module Utilisateur Distribution 1-011-007-000 ==========
trade.brokerage.user_not_exists=L'utilisateur distributeur n'existe pas
trade.brokerage.frozen_price_not_enough=Commission gelée insuffisante ({})
trade.brokerage.bind_self=Vous ne pouvez pas vous lier à vous-même
trade.brokerage.bind_user_not_enabled=L'utilisateur lié n'est pas éligible à la promotion
trade.brokerage.bind_condition_admin=Le lien ne peut être effectué que depuis l'interface administrateur
trade.brokerage.bind_mode_register=Le lien ne peut être effectué qu'à l'inscription
trade.brokerage.bind_override=Un promoteur est déjà lié
trade.brokerage.bind_loop=Un subordonné ne peut pas lier son supérieur
trade.brokerage.level_not_support=Actuellement, seuls les niveaux inférieurs ou égaux à 2 sont pris en charge

# ========== Module Retrait Distribution 1-011-008-000 ==========
trade.brokerage_withdraw.not_exists=Le dossier de retrait de commission n'existe pas
trade.brokerage_withdraw.status_not_auditing=Le dossier de retrait n'est pas en cours de validation
trade.brokerage_withdraw.min_price=Le montant de retrait ne peut pas être inférieur à {} euros
trade.brokerage_withdraw.balance_not_enough=Vous pouvez retirer jusqu'à {} euros

# ========== Module Liste de Souhaits 1-011-009-000 ==========
trade.wishlist.not_exists=L'article de la liste de souhaits n'existe pas
