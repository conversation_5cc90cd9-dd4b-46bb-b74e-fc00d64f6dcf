# ========== Order 模块 1-011-000-000 ==========
trade.order_item.not_found=交易订单项不存在
trade.order.not_found=交易订单不存在
trade.order_item.update_after_sale_status_fail=交易订单项更新售后状态失败，请重试
trade.order.update_paid_status_not_unpaid=交易订单更新支付状态失败，订单不是【未支付】状态
trade.order.update_paid_fail_pay_order_id_error=交易订单更新支付状态失败，支付单编号不匹配
trade.order.update_paid_fail_pay_order_status_not_success=交易订单更新支付状态失败，支付单状态不是【支付成功】状态
trade.order.update_paid_fail_pay_price_not_match=交易订单更新支付状态失败，支付单金额不匹配
trade.order.delivery_fail_status_not_undelivered=交易订单发货失败，订单不是【待发货】状态
trade.order.receive_fail_status_not_delivered=交易订单收货失败，订单不是【待收货】状态
trade.order.comment_fail_status_not_completed=创建交易订单项的评价失败，订单不是【已完成】状态
trade.order.comment_status_not_false=创建交易订单项的评价失败，订单已评价
trade.order.delivery_fail_refund_status_not_none=交易订单发货失败，订单已退款或部分退款
trade.order.delivery_fail_combination_record_not_success=交易订单发货失败，拼团未成功
trade.order.delivery_fail_bargain_record_not_success=交易订单发货失败，砍价未成功
trade.order.delivery_fail_delivery_type_not_express=交易订单发货失败，发货类型不是快递
trade.order.cancel_fail_status_not_unpaid=交易订单取消失败，订单不是【待支付】状态
trade.order.update_price_fail_paid=支付订单调价失败，原因：支付订单已付款，不能调价
trade.order.update_price_fail_already=支付订单调价失败，原因：已经修改过价格
trade.order.update_price_fail_price_error=支付订单调价失败，原因：调整后支付价格不能小于 0.01 元
trade.order.delete_fail_status_not_cancel=交易订单删除失败，订单不是【已取消】状态
trade.order.receive_fail_delivery_type_not_pick_up=交易订单自提失败，收货方式不是【用户自提】
trade.order.update_address_fail_status_not_delivered=交易订单修改收货地址失败，原因：订单不是【待发货】状态
trade.order.create_fail_exist_unpaid=交易订单创建失败，原因：存在未付款订单
trade.order.cancel_paid_fail=交易订单取消支付失败，原因：订单不是【{}】状态

# ========== After Sale 模块 1-011-000-100 ==========
trade.after_sale.not_found=售后单不存在
trade.after_sale.create_fail_refund_price_error=申请退款金额错误
trade.after_sale.create_fail_order_status_canceled=订单已关闭，无法申请售后
trade.after_sale.create_fail_order_status_no_paid=订单未支付，无法申请售后
trade.after_sale.create_fail_order_status_no_delivered=订单未发货，无法申请【退货退款】售后
trade.after_sale.create_fail_order_item_applied=订单项已申请售后，无法重复申请
trade.after_sale.audit_fail_status_not_apply=审批失败，售后状态不处于审批中
trade.after_sale.update_status_fail=操作售后单失败，请刷新后重试
trade.after_sale.delivery_fail_status_not_seller_agree=退货失败，售后单状态不处于【待买家退货】
trade.after_sale.confirm_fail_status_not_buyer_delivery=确认收货失败，售后单状态不处于【待确认收货】
trade.after_sale.refund_fail_status_not_wait_refund=退款失败，售后单状态不是【待退款】
trade.after_sale.cancel_fail_status_not_valid=取消售后单失败，售后单状态不是【待审核】或【卖家同意】或【商家待收货】
trade.after_sale.create_fail_order_combination_in_progress=订单拼团中，无法申请售后

# ========== Cart 模块 1-011-002-000 ==========
trade.cart.item_not_found=购物车项不存在

# ========== Price 相关 1-011-003-000 ==========
trade.price.calculate_pay_price_illegal=支付价格计算异常，原因：价格小于等于 0
trade.price.delivery_price_template_not_found=计算快递运费异常，找不到对应的运费模板
trade.price.coupon_not_match_normal_order=参与秒杀、拼团、砍价的营销商品，无法使用优惠劵
trade.price.seckill_total_limit_exceed=参与秒杀的商品，超过了秒杀总限购数量
trade.price.point_total_limit_exceed=参与积分活动的商品，超过了积分活动商品总限购数量
trade.price.delivery_price_type_illegal=计算快递运费异常，配送方式不匹配
trade.price.coupon_can_not_use=该优惠劵无法使用，原因：{}

# ========== 物流 Express 模块 1-011-004-000 ==========
trade.express.not_exists=快递公司不存在
trade.express.code_duplicate=已经存在该编码的快递公司
trade.express.client_not_provide=需要接入快递服务商，比如【快递100】
trade.express.status_not_enable=快递公司未启用
trade.express.api_query_error=快递查询接口异常
trade.express.api_query_failed=快递查询返回失败，原因：{}

# ========== 物流 Template 模块 1-011-005-000 ==========
trade.express_template.name_duplicate=已经存在该运费模板名
trade.express_template.not_exists=运费模板不存在

# ========== 物流 PICK_UP 模块 1-011-006-000 ==========
trade.pick_up.store_not_exists=自提门店不存在

# ========== 分销用户 模块 1-011-007-000 ==========
trade.brokerage.user_not_exists=分销用户不存在
trade.brokerage.frozen_price_not_enough=用户冻结佣金({})数量不足
trade.brokerage.bind_self=不能绑定自己
trade.brokerage.bind_user_not_enabled=绑定用户没有推广资格
trade.brokerage.bind_condition_admin=仅可在后台绑定推广员
trade.brokerage.bind_mode_register=只有在注册时可以绑定
trade.brokerage.bind_override=已绑定了推广人
trade.brokerage.bind_loop=下级不能绑定自己的上级
trade.brokerage.level_not_support=目前只支持 level 小于等于 2

# ========== 分销提现 模块 1-011-008-000 ==========
trade.brokerage_withdraw.not_exists=佣金提现记录不存在
trade.brokerage_withdraw.status_not_auditing=佣金提现记录状态不是审核中
trade.brokerage_withdraw.min_price=提现金额不能低于 {} 元
trade.brokerage_withdraw.balance_not_enough=您当前最多可提现 {} 元

# ========== 心愿单 模块 1-011-009-000 ==========
trade.wishlist.not_exists=心愿单不存在

# ========== Order Module 1-011-010-000 ==========
trade.order_supplement.not_exists=补款单不存在
trade.order_supplement.order.status.error = 原订单状态不符合
trade.order_supplement.paid.can.not.delete = 补款单已经支付，不能删除