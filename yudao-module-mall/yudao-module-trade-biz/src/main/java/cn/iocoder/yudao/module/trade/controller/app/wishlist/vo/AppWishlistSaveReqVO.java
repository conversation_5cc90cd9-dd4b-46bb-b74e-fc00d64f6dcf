package cn.iocoder.yudao.module.trade.controller.app.wishlist.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "用户 APP - 心愿单新增/修改 Request VO")
@Data
public class AppWishlistSaveReqVO {

    @Schema(description = "编号，唯一自增。", requiredMode = Schema.RequiredMode.REQUIRED, example = "15261")
    private Long id;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31636")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "商品 SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23899")
    @NotNull(message = "商品 SPU 编号不能为空")
    private Long spuId;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2213")
    @NotNull(message = "商品 SKU 编号不能为空")
    private Long skuId;

}