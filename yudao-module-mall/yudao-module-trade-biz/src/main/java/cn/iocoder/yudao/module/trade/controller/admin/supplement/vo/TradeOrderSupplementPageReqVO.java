package cn.iocoder.yudao.module.trade.controller.admin.supplement.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 订单补款单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradeOrderSupplementPageReqVO extends PageParam {

    @Schema(description = "补款单流水号")
    private String no;

    @Schema(description = "用户编号", example = "32535")
    private Long userId;

    @Schema(description = "单据类型", example = "2")
    private Integer orderType;

    @Schema(description = "订单编号", example = "4141")
    private Long orderId;

    @Schema(description = "支付状态", example = "2")
    private Integer status;

}