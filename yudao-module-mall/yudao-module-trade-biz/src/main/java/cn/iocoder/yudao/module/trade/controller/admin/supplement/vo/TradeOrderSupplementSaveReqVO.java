package cn.iocoder.yudao.module.trade.controller.admin.supplement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 订单补款单新增/修改 Request VO")
@Data
public class TradeOrderSupplementSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22169")
    private Long id;

    @Schema(description = "补款单流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "32535")
    private Long userId;

    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer orderType;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4141")
    @NotNull(message = "订单编号不能为空")
    private Long orderId;

    @Schema(description = "原支付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "7110")
    private Integer originalPayPrice;

    @Schema(description = "实际应付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "11892")
    private Integer actualPayPrice;

    @Schema(description = "需补款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "8935")
    @NotNull(message = "需补款金额不能为空")
    private Integer payPrice;

    @Schema(description = "补款原因", example = "不喜欢")
    private String reason;

    @Schema(description = "支付状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;

    @Schema(description = "支付订单编号", example = "16661")
    private Long payOrderId;

    @Schema(description = "支付成功的支付渠道")
    private String payChannelCode;

    @Schema(description = "订单支付时间")
    private LocalDateTime payTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

}