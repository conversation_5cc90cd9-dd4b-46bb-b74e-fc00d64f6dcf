package cn.iocoder.yudao.module.trade.controller.app.cart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Schema(description = "用户 App - 购物车添加购物项 Request VO")
@Data
public class AppCartAddReqVO {

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED,example = "1024")
    @NotNull(message = "商品 SKU 编号不能为空")
    private Long skuId;

    @Schema(description = "新增商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "商品数量必须大于等于 1")
    private Integer count;


    @Schema(description = "单价，单位分", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "9999")
    private Integer price;

    @Schema(description = "用户备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "用户备注")
    private String memo;

    //----代购商品增加以下字段
    @Schema(description = "商品类型:0自营，1代购", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    //{ 0:自营，1代购}
    private Integer type = 0;

    @Schema(description = "商品来源", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "taobao")
    private String source;

    @Schema(description = "商品来源编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123456")
    private String sourceId;

    @Schema(description = "商品来源SKU编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123456")
    private String sourceSkuId;

}
