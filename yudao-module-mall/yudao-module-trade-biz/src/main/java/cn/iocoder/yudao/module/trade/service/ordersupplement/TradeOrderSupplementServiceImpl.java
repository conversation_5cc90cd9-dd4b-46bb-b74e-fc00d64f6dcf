package cn.iocoder.yudao.module.trade.service.ordersupplement;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.pay.api.order.PayOrderApi;
import cn.iocoder.yudao.module.pay.api.order.dto.PayOrderCreateReqDTO;
import cn.iocoder.yudao.module.pay.api.order.dto.PayOrderRespDTO;
import cn.iocoder.yudao.module.pay.enums.order.PayOrderStatusEnum;
import cn.iocoder.yudao.module.trade.api.order.dto.TradeOrderSupplementReqDTO;
import cn.iocoder.yudao.module.trade.convert.supplement.TradeOrderSupplementConvert;
import cn.iocoder.yudao.module.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.trade.dal.redis.no.TradeNoRedisDAO;
import cn.iocoder.yudao.module.trade.enums.order.TradeOrderStatusEnum;
import cn.iocoder.yudao.module.trade.enums.supplement.TradeOrderSupplementStatusEnum;
import cn.iocoder.yudao.module.trade.enums.supplement.TradeOrderSupplementTypeEnum;
import cn.iocoder.yudao.module.trade.framework.order.config.TradeOrderProperties;
import cn.iocoder.yudao.module.trade.service.order.TradeOrderQueryService;
import cn.iocoder.yudao.module.trade.service.order.TradeOrderUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import cn.iocoder.yudao.module.trade.controller.admin.supplement.vo.*;
import cn.iocoder.yudao.module.trade.dal.dataobject.ordersupplement.TradeOrderSupplementDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.trade.dal.mysql.ordersupplement.TradeOrderSupplementMapper;

import java.time.LocalDateTime;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.trade.enums.ErrorCodeConstants.*;

/**
 * 订单补款单 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class TradeOrderSupplementServiceImpl implements TradeOrderSupplementService {

    @Resource
    private TradeOrderSupplementMapper orderSupplementMapper;
    @Resource
    private TradeOrderQueryService orderService;
    @Resource
    private TradeOrderUpdateService orderUpdateService;
    @Resource
    private TradeNoRedisDAO tradeNoRedisDAO;
    @Resource
    private PayOrderApi payOrderApi;
    @Resource
    private TradeOrderProperties tradeOrderProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOrderSupplement(TradeOrderSupplementSaveReqVO createReqVO) {

        // 检查订单存在
        TradeOrderDO order = orderService.getOrder(createReqVO.getOrderId());
        if (order == null) {
            throw exception(ORDER_NOT_FOUND);
        }
        if(!Objects.equals(order.getStatus(), TradeOrderStatusEnum.UNDELIVERED.getStatus())){
            throw exception(ORDER_SUPPLEMENT_ORDER_STATUS_ERROR);
        }

        // 插入
        TradeOrderSupplementDO orderSupplement = BeanUtils.toBean(createReqVO, TradeOrderSupplementDO.class);
        orderSupplement.setNo(tradeNoRedisDAO.generate(TradeNoRedisDAO.TRADE_ORDER_SUPPLEMENT_NO_PREFIX));
        orderSupplement.setUserId(order.getUserId());
        orderSupplement.setOrderType(TradeOrderSupplementTypeEnum.NORMAL_ORDER.getType());
        orderSupplement.setStatus(TradeOrderSupplementStatusEnum.UNPAID.getStatus());
        orderSupplement.setOriginalPayPrice(order.getPayPrice());
        orderSupplement.setActualPayPrice(order.getPayPrice()+createReqVO.getPayPrice());
        orderSupplement.setPayPrice(createReqVO.getPayPrice());

        orderSupplementMapper.insert(orderSupplement);

        // 2 更新原订单的补款信息
        orderUpdateService.updateSupplement(new TradeOrderSupplementReqDTO()
                .setOrderId(order.getId())
                .setSupplementId(orderSupplement.getId())
                .setSupplementPrice(createReqVO.getPayPrice()));

        // 3. 生成支付单
        if (orderSupplement.getPayPrice() > 0) {
            createPayOrder(orderSupplement);
        }
        // 返回
        return orderSupplement.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderSupplement(TradeOrderSupplementSaveReqVO updateReqVO) {
        // 校验存在
        TradeOrderSupplementDO orderSupplement = orderSupplementMapper.selectById(updateReqVO.getId());
        if (orderSupplement == null) {
            throw exception(ORDER_SUPPLEMENT_NOT_EXISTS);
        }
        //如果修改了金额则需要同步修改订单记录中的补款金额
        if(!Objects.equals(orderSupplement.getPayPrice(), updateReqVO.getPayPrice())){
            // 校验订单状态
            TradeOrderDO order = orderService.getOrder(updateReqVO.getOrderId());
            if(!Objects.equals(order.getStatus(), TradeOrderStatusEnum.UNDELIVERED.getStatus())){
                throw exception(ORDER_SUPPLEMENT_ORDER_STATUS_ERROR);
            }
            //更新订单表
            orderUpdateService.updateSupplement(new TradeOrderSupplementReqDTO()
                    .setOrderId(order.getId())
                    .setSupplementId(orderSupplement.getId())
                    .setSupplementPrice(updateReqVO.getPayPrice()));
            //更新支付单表
            payOrderApi.updatePayOrderPrice(orderSupplement.getPayOrderId(), updateReqVO.getPayPrice());
        }

        // 更新
        TradeOrderSupplementDO updateObj = BeanUtils.toBean(updateReqVO, TradeOrderSupplementDO.class);
        orderSupplementMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderSupplement(Long id) {
        // 校验存在
        TradeOrderSupplementDO orderSupplement = orderSupplementMapper.selectById(id);
        if (orderSupplement == null) {
            throw exception(ORDER_SUPPLEMENT_NOT_EXISTS);
        }
        //已支付的补款单不能删除
        if(TradeOrderSupplementStatusEnum.PAID.getStatus().equals(orderSupplement.getStatus())){
            throw exception(ORDER_SUPPLEMENT_PAID_CAN_NOT_DELETE);
        }
        // 校验订单状态 不在未发货状态的订单不能删除
        TradeOrderDO order = orderService.getOrder(orderSupplement.getOrderId());
        if(order==null || !Objects.equals(order.getStatus(), TradeOrderStatusEnum.UNDELIVERED.getStatus())){
            throw exception(ORDER_SUPPLEMENT_ORDER_STATUS_ERROR);
        }

        orderUpdateService.removeSupplement(order.getId());

        // todo 是否需要删除PayOrder 中数据?

        // 删除
        orderSupplementMapper.deleteById(id);
    }

    private void createPayOrder(TradeOrderSupplementDO order) {
        // 创建支付单，用于后续的支付
        PayOrderCreateReqDTO payOrderCreateReqDTO = TradeOrderSupplementConvert.INSTANCE.convert(
                order, tradeOrderProperties);
        Long payOrderId = payOrderApi.createOrder(payOrderCreateReqDTO);

        // 更新到补款单上
        orderSupplementMapper.updateById(new TradeOrderSupplementDO().setId(order.getId()).setPayOrderId(payOrderId));
        order.setPayOrderId(payOrderId);
    }

    private TradeOrderSupplementDO validateOrderSupplementExists(Long id) {

        // 校验订单是否存在
        TradeOrderSupplementDO orderSupplement = orderSupplementMapper.selectById(id);
        if (orderSupplement == null) {
            throw exception(ORDER_SUPPLEMENT_NOT_EXISTS);
        }
        return orderSupplement;
    }

    @Override
    public TradeOrderSupplementDO getOrderSupplement(Long id) {
        return orderSupplementMapper.selectById(id);
    }


    @Override
    public TradeOrderSupplementDO getOrderSupplementApp(Long userId, Long id) {
        TradeOrderSupplementDO orderSupplementDO = orderSupplementMapper.selectById(id);
        if (orderSupplementDO != null
                && ObjectUtil.notEqual(orderSupplementDO.getUserId(), userId)) {
            return null;
        }
        return orderSupplementDO;
    }

    @Override
    public PageResult<TradeOrderSupplementDO> getOrderSupplementPage(TradeOrderSupplementPageReqVO pageReqVO) {
        return orderSupplementMapper.selectPage(pageReqVO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderSupplementPaid(Long id, Long payOrderId) {
        // 1.1 校验订单存在
        TradeOrderSupplementDO order = validateOrderSupplementExists(id);
        //  1.2 校验订单已支付
        if(TradeOrderSupplementStatusEnum.PAID.getStatus().equals(order.getStatus())) {
            if(ObjectUtil.equals(order.getPayOrderId(), payOrderId)){
                log.warn("[updateOrderSupplementPaid][order({}) 已支付，且支付单号相同({})，直接返回]", order, payOrderId);
                return;
            }
            log.error("[updateOrderSupplementPaid][order({}) 支付单不匹配({})，请进行处理！order 数据是：{}]",
                    id, payOrderId, JsonUtils.toJsonString(order));
            throw exception(ORDER_UPDATE_PAID_FAIL_PAY_ORDER_ID_ERROR);
        }

        // 2. 校验支付订单的合法性
        PayOrderRespDTO payOrder = validatePayOrderPaid(order, payOrderId);

        // 3.1 更新 TradeOrderSupplementDO 状态为已支付
        int updateCount = orderSupplementMapper.updateByIdAndStatus(id, order.getStatus(),
                new TradeOrderSupplementDO().setStatus(TradeOrderSupplementStatusEnum.PAID.getStatus())
                        .setPayTime(LocalDateTime.now()).setPayChannelCode(payOrder.getChannelCode()));
        if (updateCount == 0) {
            throw exception(ORDER_UPDATE_PAID_STATUS_NOT_UNPAID);
        }

        // 3.2 更新订单表状态 补款单状态为已支付
        int updateOrderCount = orderUpdateService.updateSupplementStatus(new TradeOrderSupplementReqDTO()
                .setOrderId(order.getOrderId())
                .setSupplementStatus(TradeOrderSupplementStatusEnum.PAID.getStatus()));
        if (updateCount == 0) {
            throw exception(ORDER_UPDATE_PAID_STATUS_NOT_UNPAID);
        }

        // 4. 执行 TradeOrderHandler 的后置处理
        //List<TradeOrderItemDO> orderItems = tradeOrderItemMapper.selectListByOrderId(id);
        //tradeOrderHandlers.forEach(handler -> handler.afterPayOrder(order, orderItems));



        //6.订单支付完成后操作
        // 发送 MQ 消息：用户创建
        //TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
        //
        //    @Override
        //    public void afterCommit() {
        //        tradeOrderProducer.sendOrderSuccessMessage(order.getId(),getCurrentLanguage().getLanguage());
        //    }
        //
        //});


    }

    /**
     * 校验支付订单的合法性
     *
     * @param order 交易订单
     * @param payOrderId 支付订单编号
     * @return 支付订单
     */
    private PayOrderRespDTO validatePayOrderPaid(TradeOrderSupplementDO order, Long payOrderId) {
        // 1. 校验支付单是否存在
        PayOrderRespDTO payOrder = payOrderApi.getOrder(payOrderId);
        if (payOrder == null) {
            log.error("[validatePayOrderPaid][order({}) payOrder({}) 不存在，请进行处理！]", order.getId(), payOrderId);
            throw exception(ORDER_NOT_FOUND);
        }

        // 2.1 校验支付单已支付
        if (!PayOrderStatusEnum.isSuccess(payOrder.getStatus())) {
            log.error("[validatePayOrderPaid][order({}) payOrder({}) 未支付，请进行处理！payOrder 数据是：{}]",
                    order.getId(), payOrderId, JsonUtils.toJsonString(payOrder));
            throw exception(ORDER_UPDATE_PAID_FAIL_PAY_ORDER_STATUS_NOT_SUCCESS);
        }
        // 2.2 校验支付金额一致
        //if (ObjectUtil.notEqual(payOrder.getPrice(), order.getPayPrice())) { ding 修改为对比orderPrice
        if (ObjectUtil.notEqual(payOrder.getOrderPrice(), order.getPayPrice())) {
            log.error("[validatePayOrderPaid][order({}) payOrder({}) 支付金额不匹配，请进行处理！order 数据是：{}，payOrder 数据是：{}]",
                    order.getId(), payOrderId, JsonUtils.toJsonString(order), JsonUtils.toJsonString(payOrder));
            throw exception(ORDER_UPDATE_PAID_FAIL_PAY_PRICE_NOT_MATCH);
        }
        // 2.2 校验支付订单匹配（二次）
        if (ObjectUtil.notEqual(payOrder.getMerchantOrderId(), order.getId().toString())) {
            log.error("[validatePayOrderPaid][order({}) 支付单不匹配({})，请进行处理！payOrder 数据是：{}]",
                    order.getId(), payOrderId, JsonUtils.toJsonString(payOrder));
            throw exception(ORDER_UPDATE_PAID_FAIL_PAY_ORDER_ID_ERROR);
        }
        return payOrder;
    }
}