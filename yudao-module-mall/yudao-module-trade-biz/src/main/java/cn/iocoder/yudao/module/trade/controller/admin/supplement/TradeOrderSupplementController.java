package cn.iocoder.yudao.module.trade.controller.admin.supplement;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.trade.controller.admin.supplement.vo.*;
import cn.iocoder.yudao.module.trade.dal.dataobject.ordersupplement.TradeOrderSupplementDO;
import cn.iocoder.yudao.module.trade.service.ordersupplement.TradeOrderSupplementService;

@Tag(name = "管理后台 - 订单补款单")
@RestController
@RequestMapping("/trade/order-supplement")
@Validated
public class TradeOrderSupplementController {

    @Resource
    private TradeOrderSupplementService orderSupplementService;

    @PostMapping("/create")
    @Operation(summary = "创建订单补款单")
    @PreAuthorize("@ss.hasPermission('trade:order-supplement:create')")
    public CommonResult<Long> createOrderSupplement(@Valid @RequestBody TradeOrderSupplementSaveReqVO createReqVO) {
        return success(orderSupplementService.createOrderSupplement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新订单补款单")
    @PreAuthorize("@ss.hasPermission('trade:order-supplement:update')")
    public CommonResult<Boolean> updateOrderSupplement(@Valid @RequestBody TradeOrderSupplementSaveReqVO updateReqVO) {
        orderSupplementService.updateOrderSupplement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除订单补款单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('trade:order-supplement:delete')")
    public CommonResult<Boolean> deleteOrderSupplement(@RequestParam("id") Long id) {
        orderSupplementService.deleteOrderSupplement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得订单补款单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:order-supplement:query')")
    public CommonResult<TradeOrderSupplementRespVO> getOrderSupplement(@RequestParam("id") Long id) {
        TradeOrderSupplementDO orderSupplement = orderSupplementService.getOrderSupplement(id);
        return success(BeanUtils.toBean(orderSupplement, TradeOrderSupplementRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得订单补款单分页")
    @PreAuthorize("@ss.hasPermission('trade:order-supplement:query')")
    public CommonResult<PageResult<TradeOrderSupplementRespVO>> getOrderSupplementPage(@Valid TradeOrderSupplementPageReqVO pageReqVO) {
        PageResult<TradeOrderSupplementDO> pageResult = orderSupplementService.getOrderSupplementPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TradeOrderSupplementRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出订单补款单 Excel")
    @PreAuthorize("@ss.hasPermission('trade:order-supplement:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOrderSupplementExcel(@Valid TradeOrderSupplementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TradeOrderSupplementDO> list = orderSupplementService.getOrderSupplementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "订单补款单.xls", "数据", TradeOrderSupplementRespVO.class,
                        BeanUtils.toBean(list, TradeOrderSupplementRespVO.class));
    }

}