package cn.iocoder.yudao.module.trade.controller.admin.order.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 订单收货Request VO
 * @author: Ding<PERSON>ia<PERSON>
 * @create: 2025-05-28 09:51
 **/
@Data
public class TradeOrderReceiveReqVO {

    private List<Item> items;

    @Data
    public static class Item{

        /**
         * 仓库编号
         */
        private Long warehouseId;

        /**
         * 商品分类编号
         */
        private Long categoryId;

        /**
         * 订单商品项编号
         */
        private Long itemId;

        /**
         * 库存数量
         */
        private Integer count;
        /**
         * 商品重量 单位：g
         */
        private Integer weight;
        /**
         * 商品体积
         */
        private BigDecimal volume;
        /**
         * 长
         */
        private BigDecimal length;
        /**
         * 宽
         */
        private BigDecimal width;
        /**
         * 高
         */
        private BigDecimal height;

        /**
         * 预包装重量
         */
        private Integer prePackageWeight;
        /**
         * 入库质检图片
         */
        private List<String>  inspectPicUrls;

        /**
         * 入库视频
         */
        private List<String>  inspectVideoUrls;
        /**
         * 相关文件地址
         */
        private String fileUrl;
        /**
         * 备注
         */
        private String remark;

        /**
         * 位置
         */
        private String location;
    }

}
