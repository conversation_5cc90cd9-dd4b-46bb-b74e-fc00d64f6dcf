package cn.iocoder.yudao.module.trade.controller.app.cart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 App - 购物车更新备注 Request VO")
@Data
public class AppCartUpdateMemoReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "编号不能为空")
    private Long id;

    @Schema(description = "商品备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "备注信息")
    private String memo;

}
