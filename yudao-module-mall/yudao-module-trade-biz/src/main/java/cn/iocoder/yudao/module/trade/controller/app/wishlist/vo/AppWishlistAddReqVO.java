package cn.iocoder.yudao.module.trade.controller.app.wishlist.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "用户 App - 心愿单添加商品 Request VO")
@Data
public class AppWishlistAddReqVO {

    @Schema(description = "商品 SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED,example = "1024")
    @NotNull(message = "商品 SPU 编号不能为空")
    private Long spuId;


}
