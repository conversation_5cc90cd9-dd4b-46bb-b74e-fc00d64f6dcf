package cn.iocoder.yudao.module.trade.controller.admin.base.server;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @program: ruoyi-vue-pro
 * @description: 订单服务VO
 * @author: Ding<PERSON>ia<PERSON>
 * @create: 2025-05-30 21:23
 **/
@Data
public class TradeOrderItemServerVO {
    @Schema(description = "服务编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4394")
    private Long id;

    @Schema(description = "服务编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer type;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED)
    private String description;

    @Schema(description = "是否免费", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean free;

    @Schema(description = "收费金额，单位使用：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "4086")
    private Integer price;

    @Schema(description = "排序")
    private Integer sort;
}
