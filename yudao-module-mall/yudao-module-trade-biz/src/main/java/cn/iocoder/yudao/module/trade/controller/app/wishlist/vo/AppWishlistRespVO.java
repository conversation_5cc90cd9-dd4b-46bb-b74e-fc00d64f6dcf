package cn.iocoder.yudao.module.trade.controller.app.wishlist.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "用户 APP - 心愿单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppWishlistRespVO {

    @Schema(description = "编号，唯一自增。", requiredMode = Schema.RequiredMode.REQUIRED, example = "15261")
    @ExcelProperty("编号，唯一自增。")
    private Long id;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31636")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "商品 SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23899")
    @ExcelProperty("商品 SPU 编号")
    private Long spuId;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2213")
    @ExcelProperty("商品 SKU 编号")
    private Long skuId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}