package cn.iocoder.yudao.module.trade.controller.app.wishlist.vo;

import cn.iocoder.yudao.module.trade.controller.app.base.sku.AppProductSkuBaseRespVO;
import cn.iocoder.yudao.module.trade.controller.app.base.spu.AppProductSpuBaseRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description:
 * @author: DingXiao
 * @create: 2024-11-20 13:27
 **/

@Schema(description = "用户 App - 用户的心愿单列表 Response VO")
@Data
public class AppWishlistListRespVO {

    /**
     * 有效的购物项数组
     */
    private List<Wishlist> validList;

    /**
     * 无效的购物项数组
     */
    private List<Wishlist> invalidList;

    @Schema(description = "心愿单项")
    @Data
    public static class Wishlist {

        @Schema(description = "心愿单的编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
        private Long id;

        /**
         * 商品 SPU
         */
        private AppProductSpuBaseRespVO spu;

    }
}
