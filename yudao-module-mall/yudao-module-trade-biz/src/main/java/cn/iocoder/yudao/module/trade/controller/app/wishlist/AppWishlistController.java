package cn.iocoder.yudao.module.trade.controller.app.wishlist;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

import cn.iocoder.yudao.module.trade.controller.app.wishlist.vo.*;
import cn.iocoder.yudao.module.trade.dal.dataobject.wishlist.WishlistDO;
import cn.iocoder.yudao.module.trade.service.wishlist.WishlistService;

/**
 * 这个功能不再使用，改回使用原本项目的收藏Favorite功能 AppFavoriteController
 */
@Tag(name = "用户 APP - 心愿单")
@RestController
@RequestMapping("/trade/wishlist")
@Validated
public class AppWishlistController {

    @Resource
    private WishlistService wishlistService;

    //@PostMapping("/create")
    //@Operation(summary = "创建心愿单")
    //public CommonResult<Long> createWishlist(@Valid @RequestBody AppWishlistSaveReqVO createReqVO) {
    //    return success(wishlistService.createWishlist(createReqVO));
    //}
    //
    //@PutMapping("/update")
    //@Operation(summary = "更新心愿单")
    //public CommonResult<Boolean> updateWishlist(@Valid @RequestBody AppWishlistSaveReqVO updateReqVO) {
    //    wishlistService.updateWishlist(updateReqVO);
    //    return success(true);
    //}
    //
    //@DeleteMapping("/delete")
    //@Operation(summary = "删除心愿单")
    //@Parameter(name = "id", description = "编号", required = true)
    //public CommonResult<Boolean> deleteWishlist(@RequestParam("id") Long id) {
    //    wishlistService.deleteWishlist(id);
    //    return success(true);
    //}
    //
    //@GetMapping("/get")
    //@Operation(summary = "获得心愿单")
    //@Parameter(name = "id", description = "编号", required = true, example = "1024")
    //public CommonResult<AppWishlistRespVO> getWishlist(@RequestParam("id") Long id) {
    //    WishlistDO wishlist = wishlistService.getWishlist(id);
    //    return success(BeanUtils.toBean(wishlist, AppWishlistRespVO.class));
    //}
    //
    //@GetMapping("/page")
    //@Operation(summary = "获得心愿单分页")
    //public CommonResult<PageResult<AppWishlistRespVO>> getWishlistPage(@Valid AppWishlistPageReqVO pageReqVO) {
    //    PageResult<WishlistDO> pageResult = wishlistService.getWishlistPage(pageReqVO);
    //    return success(BeanUtils.toBean(pageResult, AppWishlistRespVO.class));
    //}
    //
    //@GetMapping("/export-excel")
    //@Operation(summary = "导出心愿单 Excel")
    //@ApiAccessLog(operateType = EXPORT)
    //public void exportWishlistExcel(@Valid AppWishlistPageReqVO pageReqVO,
    //          HttpServletResponse response) throws IOException {
    //    pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
    //    List<WishlistDO> list = wishlistService.getWishlistPage(pageReqVO).getList();
    //    // 导出 Excel
    //    ExcelUtils.write(response, "心愿单.xls", "数据", AppWishlistRespVO.class,
    //                    BeanUtils.toBean(list, AppWishlistRespVO.class));
    //}


    @PostMapping("/add")
    @Operation(summary = "添加心愿单商品")
    public CommonResult<Long> addCart(@Valid @RequestBody AppWishlistAddReqVO wishlistAddReqVO) {
        return success(wishlistService.addWishlist(getLoginUserId(), wishlistAddReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除心愿单商品")
    @Parameter(name = "ids", description = "心愿单商品编号", required = true, example = "1024,2048")
    public CommonResult<Boolean> deleteWishlist(@RequestParam("ids") List<Long> ids) {
        wishlistService.deleteWishlist(getLoginUserId(), ids);
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "查询用户的收藏列表")
    public CommonResult<AppWishlistListRespVO> getCartList() {
        return success(wishlistService.getWishlistList(getLoginUserId()));
    }

}