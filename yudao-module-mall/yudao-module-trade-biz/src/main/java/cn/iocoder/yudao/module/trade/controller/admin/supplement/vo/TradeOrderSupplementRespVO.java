package cn.iocoder.yudao.module.trade.controller.admin.supplement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 订单补款单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TradeOrderSupplementRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22169")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "补款单流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("补款单流水号")
    private String no;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "32535")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("单据类型")
    private Integer orderType;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4141")
    @ExcelProperty("订单编号")
    private Long orderId;

    @Schema(description = "原支付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "7110")
    @ExcelProperty("原支付金额")
    private Integer originalPayPrice;

    @Schema(description = "实际应付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "11892")
    @ExcelProperty("实际应付金额")
    private Integer actualPayPrice;

    @Schema(description = "需补款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "8935")
    @ExcelProperty("需补款金额")
    private Integer payPrice;

    @Schema(description = "补款原因", example = "不喜欢")
    @ExcelProperty("补款原因")
    private String reason;

    @Schema(description = "支付状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("支付状态")
    private Integer status;

    @Schema(description = "支付订单编号", example = "16661")
    @ExcelProperty("支付订单编号")
    private Long payOrderId;

    @Schema(description = "支付成功的支付渠道")
    @ExcelProperty("支付成功的支付渠道")
    private String payChannelCode;

    @Schema(description = "订单支付时间")
    @ExcelProperty("订单支付时间")
    private LocalDateTime payTime;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}