package cn.iocoder.yudao.module.trade.controller.app.base.server;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @program: ruoyi-vue-pro
 * @description: 订单项服务Respon VO
 * @author: Ding<PERSON><PERSON><PERSON>
 * @create: 2025-05-09 10:04
 **/
@Data
public class AppItemServerRespVO {
    @Schema(description = "服务编号", example = "2048")
    private Long id;
}
