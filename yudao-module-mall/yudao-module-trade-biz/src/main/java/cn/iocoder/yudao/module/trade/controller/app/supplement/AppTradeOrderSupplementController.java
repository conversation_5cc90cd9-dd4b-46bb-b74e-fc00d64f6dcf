package cn.iocoder.yudao.module.trade.controller.app.supplement;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.trade.controller.app.supplement.vo.AppTradeOrderSupplementRespVO;
import cn.iocoder.yudao.module.trade.dal.dataobject.ordersupplement.TradeOrderSupplementDO;
import cn.iocoder.yudao.module.trade.service.ordersupplement.TradeOrderSupplementService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * @program: ruoyi-vue-pro
 * @description: 用户APP补款单控制器
 * @author: DingXiao
 * @create: 2025-05-26 12:17
 **/
@RestController
@RequestMapping("/trade/order-supplement")
@Validated
@Slf4j
public class AppTradeOrderSupplementController {

    @Resource
    TradeOrderSupplementService  tradeOrderSupplementService;


    @GetMapping("/get")
    @Operation(summary = "获得补款单")
    public CommonResult<AppTradeOrderSupplementRespVO> getOrderSupplement(Long id){

        TradeOrderSupplementDO orderSupplement = tradeOrderSupplementService.getOrderSupplementApp(getLoginUserId(),id);
        return CommonResult.success(BeanUtils.toBean(orderSupplement, AppTradeOrderSupplementRespVO.class));
    }
}
