package cn.iocoder.yudao.module.promotion.controller.admin.helparticle.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 帮助文章分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PromotionHelpArticlePageReqVO extends PageParam {

    @Schema(description = "分类编号", example = "4141")
    private Long categoryId;

    @Schema(description = "文章编码;用于前端路由")
    private String code;

    @Schema(description = "中文标题")
    private String titleZh;

    @Schema(description = "英文标题")
    private String titleEn;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "是否FAQ;0-否，1-是")
    private Boolean faq;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}