package cn.iocoder.yudao.module.promotion.service.helparticle;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.promotion.controller.admin.helparticle.vo.*;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpArticlePageReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpArticleSearchReqVO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helparticle.PromotionHelpArticleDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 帮助文章 Service 接口
 *
 * <AUTHOR>
 */
public interface PromotionHelpArticleService {

    /**
     * 创建帮助文章
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHelpArticle(@Valid PromotionHelpArticleSaveReqVO createReqVO);

    /**
     * 更新帮助文章
     *
     * @param updateReqVO 更新信息
     */
    void updateHelpArticle(@Valid PromotionHelpArticleSaveReqVO updateReqVO);

    /**
     * 删除帮助文章
     *
     * @param id 编号
     */
    void deleteHelpArticle(Long id);

    /**
     * 获得帮助文章
     *
     * @param id 编号
     * @return 帮助文章
     */
    PromotionHelpArticleDO getHelpArticle(Long id);

    /**
     * 获得帮助文章
     *
     * @param id 编号
     * @param language 语言
     * @return 帮助文章
     */
    PromotionHelpArticleDO getHelpArticle(Long id, Locale language);

    /**
     * 获得帮助文章分页
     *
     * @param pageReqVO 分页查询
     * @return 帮助文章分页
     */
    PageResult<PromotionHelpArticleDO> getHelpArticlePage(PromotionHelpArticlePageReqVO pageReqVO);

    // ----------------------APP-------------------------

    /**
     * 获得帮助文章列表
     *
     * @return 帮助文章列表
     */
     List<PromotionHelpArticleDO> getHelpArticleList();

    /**
     * 根据分类编码和文章编码获取文章详情
     *
     * @param categoryCode 分类编码
     * @param articleCode 文章编码
     * @param language 语言
     * @return 帮助文章详情
     */
     PromotionHelpArticleDO getHelpArticleByCode(String categoryCode, String articleCode, Locale language);

    /**
     * 提交文章反馈
     *
     * @param articleId 文章ID
     * @param isHelpful 是否有用
     * @return 是否成功
     */
     boolean submitFeedback(Long articleId, Boolean isHelpful);

    /**
     * 搜索帮助文章
     *
     * @param reqVO 搜索请求参数，包含关键词、分类 ID 和分页信息
     * @param language 语言
     * @return 帮助文章分页结果
     */
     PageResult<PromotionHelpArticleDO> searchHelpArticles(AppPromotionHelpArticleSearchReqVO reqVO, Locale language);

     /**
      * 获取帮助文章分页
      *
      * @param pageReqVO 分页查询参数
      * @param language 语言
      * @return 帮助文章分页结果
      */
    PageResult<PromotionHelpArticleDO> getHelpArticlePageApp(@Valid AppPromotionHelpArticlePageReqVO pageReqVO, Locale language);
}