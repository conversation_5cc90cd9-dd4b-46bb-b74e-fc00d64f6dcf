package cn.iocoder.yudao.module.promotion.controller.admin.helparticle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 帮助文章新增/修改 Request VO")
@Data
public class PromotionHelpArticleSaveReqVO {

    @Schema(description = "帮助文章编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21786")
    private Long id;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4141")
    @NotNull(message = "分类编号不能为空")
    private Long categoryId;

    @Schema(description = "文章编码;用于前端路由", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "文章编码;用于前端路由不能为空")
    private String code;

    @Schema(description = "文章作者")
    private String author;

    @Schema(description = "浏览次数", example = "2397")
    private Integer browseCount;

    @Schema(description = "中文标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "中文标题不能为空")
    private String titleZh;

    @Schema(description = "英文标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "英文标题不能为空")
    private String titleEn;

    @Schema(description = "法语标题")
    private String titleFr;

    @Schema(description = "德语标题")
    private String titleDe;

    @Schema(description = "西班牙语标题")
    private String titleEs;

    @Schema(description = "阿拉伯语标题")
    private String titleAr;

    @Schema(description = "中文内容")
    private String contentZh;

    @Schema(description = "英文内容")
    private String contentEn;

    @Schema(description = "法语内容")
    private String contentFr;

    @Schema(description = "德语内容")
    private String contentDe;

    @Schema(description = "西班牙语内容")
    private String contentEs;

    @Schema(description = "阿拉伯语内容")
    private String contentAr;

    @Schema(description = "相关文章ID;用逗号分隔，如1,2,3")
    private String relatedArticles;

    @Schema(description = "有帮助反馈数量", example = "21810")
    private Integer helpfulCount;

    @Schema(description = "无帮助反馈数量", example = "4529")
    private Integer unhelpfulCount;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "是否FAQ;0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否FAQ;0-否，1-是不能为空")
    private Boolean faq;

}