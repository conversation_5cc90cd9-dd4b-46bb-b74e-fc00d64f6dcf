package cn.iocoder.yudao.module.promotion.controller.app.display.vo;

import cn.iocoder.yudao.module.product.api.sku.dto.ProductSkuRespDTO;
import cn.iocoder.yudao.module.product.api.spu.dto.ProductSpuRespDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "用户 APP - 促销展示位 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppDisplayRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13390")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "展示位标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("展示位标题")
    private String title;

    @Schema(description = "展示位副标题")
    @ExcelProperty("展示位副标题")
    private String subTitle;

    @Schema(description = "图片 URL", example = "https://www.iocoder.cn")
    @ExcelProperty("图片 URL")
    private String picUrl;

    @Schema(description = "跳转地址", example = "https://www.iocoder.cn")
    @ExcelProperty("跳转地址")
    private String url;

    @Schema(description = "标签;标签，可用来区分具体位置")
    @ExcelProperty("标签;标签，可用来区分具体位置")
    private String tag;

    @Schema(description = "商品 SPU 编号集合")
    @ExcelProperty("商品 SPU 编号集合")
    private Set<Long> spuIds;

    @Schema(description = "商品 SKU 编号集合")
    @ExcelProperty("商品 SKU 编号集合")
    private Set<Long> skuIds;

    @Schema(description = "活动状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "活动状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "位置", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "位置", converter = DictConvert.class)
    @DictFormat("promotion_display_position") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer position;

    @Schema(description = "描述", example = "描述信息")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String memo;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "商品 SPU 集合")
    private List<ProductSpuRespDTO> spus;

    @Schema(description = "商品 SKU 集合")
    private List<ProductSkuRespDTO> skus;


}