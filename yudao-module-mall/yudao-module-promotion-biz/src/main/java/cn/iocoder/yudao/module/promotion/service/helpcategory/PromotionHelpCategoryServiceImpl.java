package cn.iocoder.yudao.module.promotion.service.helpcategory;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import cn.iocoder.yudao.module.promotion.controller.admin.helpcategory.vo.*;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helpcategory.PromotionHelpCategoryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpCategoryArticleRespVO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helparticle.PromotionHelpArticleDO;
import cn.iocoder.yudao.module.promotion.dal.mysql.helparticle.PromotionHelpArticleMapper;
import cn.iocoder.yudao.module.promotion.dal.mysql.helpcategory.PromotionHelpCategoryMapper;
import cn.iocoder.yudao.module.promotion.util.PromotionI18nUtils;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.promotion.enums.ErrorCodeConstants.*;

/**
 * 帮助分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PromotionHelpCategoryServiceImpl implements PromotionHelpCategoryService {

    @Resource
    private PromotionHelpCategoryMapper helpCategoryMapper;

    @Resource
    private PromotionHelpArticleMapper helpArticleMapper;

    @Override
    public Long createHelpCategory(PromotionHelpCategorySaveReqVO createReqVO) {
        // 插入
        PromotionHelpCategoryDO helpCategory = BeanUtils.toBean(createReqVO, PromotionHelpCategoryDO.class);
        helpCategoryMapper.insert(helpCategory);
        // 返回
        return helpCategory.getId();
    }

    @Override
    public void updateHelpCategory(PromotionHelpCategorySaveReqVO updateReqVO) {
        // 校验存在
        validateHelpCategoryExists(updateReqVO.getId());
        // 更新
        PromotionHelpCategoryDO updateObj = BeanUtils.toBean(updateReqVO, PromotionHelpCategoryDO.class);
        helpCategoryMapper.updateById(updateObj);
    }

    @Override
    public void deleteHelpCategory(Long id) {
        // 校验存在
        validateHelpCategoryExists(id);
        // 删除
        helpCategoryMapper.deleteById(id);
    }

    private void validateHelpCategoryExists(Long id) {
        if (helpCategoryMapper.selectById(id) == null) {
            throw exception(HELP_CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public PromotionHelpCategoryDO getHelpCategory(Long id) {
        return helpCategoryMapper.selectById(id);
    }

    @Override
    public PageResult<PromotionHelpCategoryDO> getHelpCategoryPage(PromotionHelpCategoryPageReqVO pageReqVO) {
        return helpCategoryMapper.selectPage(pageReqVO);
    }


    @Override
    public List<PromotionHelpCategoryDO> getHelpCategoryList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return helpCategoryMapper.selectList();
        }
        return helpCategoryMapper.selectBatchIds(ids);
    }


    @Override
    public List<PromotionHelpCategoryDO> getHelpCategoryListByLang(Locale language) {
        // 1. 获取所有有效的帮助分类列表
        List<PromotionHelpCategoryDO> helpCategoryList = helpCategoryMapper.selectListByStatus(CommonStatusEnum.ENABLE.getStatus());
        if (CollUtil.isEmpty(helpCategoryList)) {
            return Collections.emptyList();
        }

        // 2. 根据语言参数处理标题和描述
        return helpCategoryList.stream()
                .map(category -> PromotionI18nUtils.processHelpCategoryI18n(category, language))
                .collect(Collectors.toList());
    }

    @Override
    public List<AppPromotionHelpCategoryArticleRespVO> getHelpCategoryArticleList(Locale language) {
        // 1. 获取所有有效的帮助分类列表
        List<PromotionHelpCategoryDO> helpCategoryList = getHelpCategoryListByLang(language);
        if (CollUtil.isEmpty(helpCategoryList)) {
            return Collections.emptyList();
        }

        // 2. 一次性查询所有启用状态的文章，不包含内容字段
        List<PromotionHelpArticleDO> allArticles = helpArticleMapper.selectListByStatusWithoutContent(CommonStatusEnum.ENABLE.getStatus());

        // 3. 按分类分组文章
        Map<Long, List<PromotionHelpArticleDO>> articlesByCategoryId = allArticles.stream()
                .collect(Collectors.groupingBy(PromotionHelpArticleDO::getCategoryId));

        // 4. 按分类分组构建结果
        List<AppPromotionHelpCategoryArticleRespVO> result = new ArrayList<>();
        for (PromotionHelpCategoryDO category : helpCategoryList) {
            // 4.1 创建分类对象
            AppPromotionHelpCategoryArticleRespVO categoryVO = new AppPromotionHelpCategoryArticleRespVO();
            categoryVO.setCode(category.getCode());
            categoryVO.setIcon(category.getIcon());
            categoryVO.setId(category.getId());
            categoryVO.setTitle(category.getTitle());
            categoryVO.setDescription(category.getDescription());
            categoryVO.setRecommend(category.getRecommend());
            categoryVO.setSort(category.getSort());

            // 4.2 获取当前分类的文章列表
            List<PromotionHelpArticleDO> categoryArticles = articlesByCategoryId.getOrDefault(category.getId(), Collections.emptyList());

            // 4.3 处理文章标题的多语言
            List<AppPromotionHelpCategoryArticleRespVO.Item> items = new ArrayList<>();
            if (CollUtil.isNotEmpty(categoryArticles)) {
                for (PromotionHelpArticleDO article : categoryArticles) {
                    AppPromotionHelpCategoryArticleRespVO.Item item = new AppPromotionHelpCategoryArticleRespVO.Item();
                    item.setId(article.getId());
                    item.setCode(article.getCode());
                    item.setAuthor(article.getAuthor());
                    item.setBrowseCount(article.getBrowseCount());
                    item.setRelatedArticles(article.getRelatedArticles());
                    item.setHelpfulCount(article.getHelpfulCount());
                    item.setUnhelpfulCount(article.getUnhelpfulCount());
                    item.setSort(article.getSort());
                    item.setFaq(article.getFaq());

                    // 根据语言设置标题
                    PromotionHelpArticleDO processedArticle = PromotionI18nUtils.processHelpArticleTitleI18n(article, language);
                    item.setTitle(processedArticle.getTitle());

                    items.add(item);
                }
            }

            // 4.4 设置分类下的文章列表
            categoryVO.setItems(items);

            // 4.5 添加到结果列表
            result.add(categoryVO);
        }

        return result;
    }
}