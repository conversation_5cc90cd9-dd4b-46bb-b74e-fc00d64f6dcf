package cn.iocoder.yudao.module.promotion.service.display;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.web.core.context.LanguageContext;
import cn.iocoder.yudao.module.product.api.spu.ProductSpuApi;
import cn.iocoder.yudao.module.product.api.spu.dto.ProductSpuRespDTO;
import cn.iocoder.yudao.module.promotion.controller.admin.display.vo.DisplayPageReqVO;
import cn.iocoder.yudao.module.promotion.controller.admin.display.vo.DisplaySaveReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.display.vo.AppDisplayRespVO;
import cn.iocoder.yudao.module.promotion.convert.display.DisplayConvert;
import cn.iocoder.yudao.module.promotion.dal.dataobject.display.DisplayDO;
import cn.iocoder.yudao.module.promotion.dal.mysql.display.DisplayMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.promotion.enums.ErrorCodeConstants.DISPLAY_NOT_EXISTS;

/**
 * 促销展示位 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DisplayServiceImpl implements DisplayService {

    @Resource
    private DisplayMapper displayMapper;

    @Resource
    ProductSpuApi productSpuApi;

    @Override
    public Long createDisplay(DisplaySaveReqVO createReqVO) {
        // 插入
        DisplayDO display = BeanUtils.toBean(createReqVO, DisplayDO.class);
        displayMapper.insert(display);
        // 返回
        return display.getId();
    }

    @Override
    public void updateDisplay(DisplaySaveReqVO updateReqVO) {
        // 校验存在
        validateDisplayExists(updateReqVO.getId());
        // 更新
        DisplayDO updateObj = BeanUtils.toBean(updateReqVO, DisplayDO.class);
        displayMapper.updateById(updateObj);
    }

    @Override
    public void deleteDisplay(Long id) {
        // 校验存在
        validateDisplayExists(id);
        // 删除
        displayMapper.deleteById(id);
    }

    private void validateDisplayExists(Long id) {
        if (displayMapper.selectById(id) == null) {
            throw exception(DISPLAY_NOT_EXISTS);
        }
    }

    @Override
    public DisplayDO getDisplay(Long id) {
        return displayMapper.selectById(id);
    }

    @Override
    public PageResult<DisplayDO> getDisplayPage(DisplayPageReqVO pageReqVO) {
        return displayMapper.selectPage(pageReqVO);
    }

    @Override
    public AppDisplayRespVO getDisplayByPosition(Integer position, String tag) {
        AppDisplayRespVO respVO = new AppDisplayRespVO();
        DisplayDO displayDO = displayMapper.selectDisplayByPosition(position,tag);
        if(displayDO == null){
            return respVO;
        }
        respVO = DisplayConvert.INSTANCE.convert01(displayDO, LanguageContext.getCurrentLanguage().toLanguageTag());
        if(CollectionUtil.isNotEmpty(respVO.getSpuIds())){
            List<ProductSpuRespDTO> spuList = productSpuApi.getSpuList(respVO.getSpuIds());
            respVO.setSpus(spuList);
        }
        return respVO;
    }

    @Override
    public List<AppDisplayRespVO> getDisplayListByPosition(Integer position) {
        List<AppDisplayRespVO> respVOList = new ArrayList<>();
        List<DisplayDO> displayDOList = displayMapper.selectDisplayListByPosition(position);
        if(CollectionUtil.isEmpty(displayDOList)){
            return respVOList;
        }
        respVOList = DisplayConvert.INSTANCE.convertList01(displayDOList, LanguageContext.getCurrentLanguage().toLanguageTag());
        for (AppDisplayRespVO vo : respVOList) {
            if(CollectionUtil.isNotEmpty(vo.getSpuIds())){
                List<ProductSpuRespDTO> spuList = productSpuApi.getSpuList(vo.getSpuIds());
                vo.setSpus(spuList);
            }
        }

        return respVOList;
    }
}