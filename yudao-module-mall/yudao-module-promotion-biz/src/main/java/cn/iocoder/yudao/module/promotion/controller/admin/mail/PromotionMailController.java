package cn.iocoder.yudao.module.promotion.controller.admin.mail;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.promotion.controller.admin.mail.vo.*;
import cn.iocoder.yudao.module.promotion.dal.dataobject.mail.PromotionMailDO;
import cn.iocoder.yudao.module.promotion.service.mail.PromotionMailService;

@Tag(name = "管理后台 - 促销邮件")
@RestController
@RequestMapping("/promotion/mail")
@Validated
public class PromotionMailController {

    @Resource
    private PromotionMailService mailService;

    @PostMapping("/create")
    @Operation(summary = "创建促销邮件")
    @PreAuthorize("@ss.hasPermission('promotion:mail:create')")
    public CommonResult<Long> createMail(@Valid @RequestBody PromotionMailSaveReqVO createReqVO) {
        return success(mailService.createMail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新促销邮件")
    @PreAuthorize("@ss.hasPermission('promotion:mail:update')")
    public CommonResult<Boolean> updateMail(@Valid @RequestBody PromotionMailSaveReqVO updateReqVO) {
        mailService.updateMail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除促销邮件")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('promotion:mail:delete')")
    public CommonResult<Boolean> deleteMail(@RequestParam("id") Long id) {
        mailService.deleteMail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得促销邮件")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('promotion:mail:query')")
    public CommonResult<PromotionMailRespVO> getMail(@RequestParam("id") Long id) {
        PromotionMailDO mail = mailService.getMail(id);
        return success(BeanUtils.toBean(mail, PromotionMailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得促销邮件分页")
    @PreAuthorize("@ss.hasPermission('promotion:mail:query')")
    public CommonResult<PageResult<PromotionMailRespVO>> getMailPage(@Valid PromotionMailPageReqVO pageReqVO) {
        PageResult<PromotionMailDO> pageResult = mailService.getMailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PromotionMailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出促销邮件 Excel")
    @PreAuthorize("@ss.hasPermission('promotion:mail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMailExcel(@Valid PromotionMailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PromotionMailDO> list = mailService.getMailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "促销邮件.xls", "数据", PromotionMailRespVO.class,
                        BeanUtils.toBean(list, PromotionMailRespVO.class));
    }

    @PutMapping("/pushMail")
    @Operation(summary = "推送促销邮件")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('promotion:mail:create')")
    public CommonResult<Boolean> pushMail(@Valid @RequestBody PromotionMailPushReqVO pushReqVO) {
         mailService.pushMail(pushReqVO);
        return success(true);
    }

    @PutMapping("/previewPushMail")
    @Operation(summary = "测试推送促销邮件")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('promotion:mail:create')")
    public CommonResult<Boolean> previewPushMail(@Valid @RequestBody PromotionMailPushReqVO pushReqVO) {
        mailService.previewPushMail(pushReqVO);
        return success(true);
    }

}