package cn.iocoder.yudao.module.promotion.controller.admin.banner.vo;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.promotion.enums.banner.BannerPositionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Banner Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 * <AUTHOR>
 */
@Data
public class BannerBaseVO {

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标题不能为空")
    private String title;

    @Schema(description = "副标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subTitle;

    @Schema(description = "跳转链接", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "跳转链接不能为空")
    private String url;

    @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "图片地址不能为空")
    private String picUrl;

    @Schema(description = "position", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "position 不能为空")
    @InEnum(BannerPositionEnum.class)
    private Integer position;

    @Schema(description = "标签", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tag;

    @Schema(description = "CSS", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String css;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    @InEnum(CommonStatusEnum.class)
    private Integer status;

    @Schema(description = "备注")
    private String memo;

    @Schema(description = "英文标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String titleEn;

    @Schema(description = "英文副标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subTitleEn;

    @Schema(description = "德语标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String titleDe;

    @Schema(description = "德语副标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subTitleDe;

    @Schema(description = "法语标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String titleFr;

    @Schema(description = "法语副标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subTitleFr;

    @Schema(description = "西语标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String titleEs;

    @Schema(description = "西语副标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subTitleEs;

    @Schema(description = "阿语标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String titleAr;

    @Schema(description = "阿语副标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subTitleAr;

}
