package cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Schema(description = "用户 APP - 帮助文章 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppPromotionHelpCategoryArticleRespVO {

    @Schema(description = "分类编码;用于前端路由", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4141")
    private Long id;

    @Schema(description = "分类标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "FAQ")
    private String title;

    @Schema(description = "分类描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "常见问题")
    private String description;

    @Schema(description = "是否推荐", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean recommend;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sort;


    List<Item> items;

    @Data
    public static class Item {
        @Schema(description = "帮助文章编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21786")
        private Long id;

        @Schema(description = "文章编码;用于前端路由", requiredMode = Schema.RequiredMode.REQUIRED)
        private String code;

        @Schema(description = "文章作者")
        private String author;

        @Schema(description = "浏览次数", example = "2397")
        private Integer browseCount;

        @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
        private String title;

        @Schema(description = "内容")
        private String content;

        @Schema(description = "相关文章ID;用逗号分隔，如1,2,3")
        private Set<Long> relatedArticles;

        @Schema(description = "有帮助反馈数量", example = "21810")
        private Integer helpfulCount;

        @Schema(description = "无帮助反馈数量", example = "4529")
        private Integer unhelpfulCount;

        @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer sort;

        @Schema(description = "是否FAQ", requiredMode = Schema.RequiredMode.REQUIRED)
        private Boolean faq;

    }




}