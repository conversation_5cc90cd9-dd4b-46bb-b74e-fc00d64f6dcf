package cn.iocoder.yudao.module.promotion.controller.admin.helpcategory.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 帮助分类分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PromotionHelpCategoryPageReqVO extends PageParam {

    @Schema(description = "分类编码;用于前端路由")
    private String code;

    @Schema(description = "中文标题")
    private String titleZh;

    @Schema(description = "英文标题")
    private String titleEn;

    @Schema(description = "状态", example = "2")
    private Integer status;

}