package cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "用户 APP - 帮助文章 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppPromotionHelpArticleRespVO {

    @Schema(description = "帮助文章编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21786")
    @ExcelProperty("帮助文章编号")
    private Long id;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4141")
    @ExcelProperty("分类编号")
    private Long categoryId;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4141")
    @ExcelProperty("分类编号")
    private Long categoryTitle;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4141")
    @ExcelProperty("分类编号")
    private Long categoryDescription;

    @Schema(description = "文章编码;用于前端路由", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("文章编码;用于前端路由")
    private String code;

    @Schema(description = "文章作者")
    @ExcelProperty("文章作者")
    private String author;

    @Schema(description = "浏览次数", example = "2397")
    @ExcelProperty("浏览次数")
    private Integer browseCount;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "内容")
    @ExcelProperty("内容")
    private String content;

    @Schema(description = "相关文章ID;用逗号分隔，如1,2,3")
    @ExcelProperty("相关文章ID;用逗号分隔，如1,2,3")
    private Set<Long> relatedArticles;

    @Schema(description = "有帮助反馈数量", example = "21810")
    @ExcelProperty("有帮助反馈数量")
    private Integer helpfulCount;

    @Schema(description = "无帮助反馈数量", example = "4529")
    @ExcelProperty("无帮助反馈数量")
    private Integer unhelpfulCount;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;


    @Schema(description = "是否FAQ", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否FAQ")
    private Boolean faq;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}