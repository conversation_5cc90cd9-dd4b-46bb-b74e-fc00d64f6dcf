package cn.iocoder.yudao.module.promotion.controller.admin.helpcategory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 帮助分类新增/修改 Request VO")
@Data
public class PromotionHelpCategorySaveReqVO {

    @Schema(description = "帮助分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15312")
    private Long id;

    @Schema(description = "分类编码;用于前端路由", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "分类编码;用于前端路由不能为空")
    private String code;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "中文标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "中文标题不能为空")
    private String titleZh;

    @Schema(description = "英文标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "英文标题不能为空")
    private String titleEn;

    @Schema(description = "法语标题")
    private String titleFr;

    @Schema(description = "德语标题")
    private String titleDe;

    @Schema(description = "西班牙语标题")
    private String titleEs;

    @Schema(description = "阿拉伯语标题")
    private String titleAr;

    @Schema(description = "中文描述")
    private String descriptionZh;

    @Schema(description = "英文描述")
    private String descriptionEn;

    @Schema(description = "法语描述")
    private String descriptionFr;

    @Schema(description = "德语描述")
    private String descriptionDe;

    @Schema(description = "西班牙语描述")
    private String descriptionEs;

    @Schema(description = "阿拉伯语描述")
    private String descriptionAr;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "是否推荐", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否推荐不能为空")
    private Boolean recommend;

}