package cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

@Schema(description = "用户 APP - 帮助文章 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppPromotionHelpArticleSimpleRespVO {

    @Schema(description = "帮助文章编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21786")
    @ExcelProperty("帮助文章编号")
    private Long id;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4141")
    @ExcelProperty("分类编号")
    private Long categoryId;

    @Schema(description = "分类标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "FAQ")
    @ExcelProperty("分类标题")
    private String categoryTitle;

    @Schema(description = "分类描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "常见问题")
    @ExcelProperty("分类描述")
    private String categoryDescription;


    @Schema(description = "文章作者")
    @ExcelProperty("文章作者")
    private String author;

    @Schema(description = "浏览次数", example = "2397")
    @ExcelProperty("浏览次数")
    private Integer browseCount;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "内容")
    @ExcelProperty("内容")
    private String content;

    @Schema(description = "相关文章")
    @ExcelProperty("相关文章")
    private Set<Long> relatedArticles;

    @Schema(description = "有帮助反馈数量", example = "21810")
    @ExcelProperty("有帮助反馈数量")
    private Integer helpfulCount;

    @Schema(description = "无帮助反馈数量", example = "4529")
    @ExcelProperty("无帮助反馈数量")
    private Integer unhelpfulCount;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "是否FAQ", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否FAQ")
    private Boolean faq;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

}