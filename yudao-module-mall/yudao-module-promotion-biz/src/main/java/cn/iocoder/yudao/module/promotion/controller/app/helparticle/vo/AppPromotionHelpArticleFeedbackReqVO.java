package cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "帮助文章反馈请求 VO")
@Data
public class AppPromotionHelpArticleFeedbackReqVO {

    @Schema(description = "文章ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "文章ID不能为空")
    private Long articleId;

    @Schema(description = "是否有用", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否有用不能为空")
    private Boolean isHelpful;
}
