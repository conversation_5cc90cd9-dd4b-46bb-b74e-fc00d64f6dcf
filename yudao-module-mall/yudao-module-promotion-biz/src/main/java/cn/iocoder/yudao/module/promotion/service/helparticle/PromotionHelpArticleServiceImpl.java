package cn.iocoder.yudao.module.promotion.service.helparticle;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.promotion.controller.admin.helparticle.vo.PromotionHelpArticlePageReqVO;
import cn.iocoder.yudao.module.promotion.controller.admin.helparticle.vo.PromotionHelpArticleSaveReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpArticlePageReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpArticleSearchReqVO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helparticle.PromotionHelpArticleDO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helpcategory.PromotionHelpCategoryDO;
import cn.iocoder.yudao.module.promotion.dal.mysql.helparticle.PromotionHelpArticleMapper;
import cn.iocoder.yudao.module.promotion.dal.mysql.helpcategory.PromotionHelpCategoryMapper;
import cn.iocoder.yudao.module.promotion.util.PromotionI18nUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.promotion.enums.ErrorCodeConstants.HELP_ARTICLE_NOT_EXISTS;

/**
 * 帮助文章 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PromotionHelpArticleServiceImpl implements PromotionHelpArticleService {

    @Resource
    private PromotionHelpArticleMapper helpArticleMapper;

    @Resource
    private PromotionHelpCategoryMapper helpCategoryMapper;


    @Override
    public Long createHelpArticle(PromotionHelpArticleSaveReqVO createReqVO) {
        // 插入
        PromotionHelpArticleDO helpArticle = BeanUtils.toBean(createReqVO, PromotionHelpArticleDO.class);
        helpArticleMapper.insert(helpArticle);
        // 返回
        return helpArticle.getId();
    }

    @Override
    public void updateHelpArticle(PromotionHelpArticleSaveReqVO updateReqVO) {
        // 校验存在
        validateHelpArticleExists(updateReqVO.getId());
        // 更新
        PromotionHelpArticleDO updateObj = BeanUtils.toBean(updateReqVO, PromotionHelpArticleDO.class);
        helpArticleMapper.updateById(updateObj);
    }

    @Override
    public void deleteHelpArticle(Long id) {
        // 校验存在
        validateHelpArticleExists(id);
        // 删除
        helpArticleMapper.deleteById(id);
    }

    private void validateHelpArticleExists(Long id) {
        if (helpArticleMapper.selectById(id) == null) {
            throw exception(HELP_ARTICLE_NOT_EXISTS);
        }
    }

    @Override
    public PromotionHelpArticleDO getHelpArticle(Long id) {
        return helpArticleMapper.selectById(id);
    }

    @Override
    public PageResult<PromotionHelpArticleDO> getHelpArticlePage(PromotionHelpArticlePageReqVO pageReqVO) {
        return helpArticleMapper.selectPage(pageReqVO);
    }

    // ------------------------APP-------------------------


    @Override
    public PromotionHelpArticleDO getHelpArticle(Long id, Locale language) {
        if (id == null) {
            return null;
        }
        PromotionHelpArticleDO article = helpArticleMapper.selectById(id);
        if (article == null) {
            return null;
        }
        // 根据语言设置文章的标题和内容
        PromotionHelpArticleDO result = PromotionI18nUtils.processHelpArticleFullI18n(article, language);

        // 增加浏览次数 异步增加浏览次数，不影响主流程
        incrementBrowseCount(article.getId());
        return result;
    }

    @Override
    public List<PromotionHelpArticleDO> getHelpArticleList() {
        return helpArticleMapper.selectList();
    }

    @Override
    public PromotionHelpArticleDO getHelpArticleByCode(String categoryCode, String articleCode, Locale language) {
        // 1. 参数校验
        if (StrUtil.isBlank(categoryCode) || StrUtil.isBlank(articleCode)) {
            return null;
        }

        // 2. 先查询分类信息
        PromotionHelpCategoryDO category = helpCategoryMapper.selectOne(new LambdaQueryWrapper<PromotionHelpCategoryDO>()
                .eq(PromotionHelpCategoryDO::getCode, categoryCode)
                .eq(PromotionHelpCategoryDO::getStatus, CommonStatusEnum.ENABLE.getStatus()));
        if (category == null) {
            return null;
        }

        // 3. 根据分类 ID 和文章编码查询文章
        PromotionHelpArticleDO article = helpArticleMapper.selectByCategoryIdAndCode(
                category.getId(), articleCode, CommonStatusEnum.ENABLE.getStatus());
        if (article == null) {
            return null;
        }

        // 3. 根据语言设置文章的标题和内容
        PromotionHelpArticleDO result = PromotionI18nUtils.processHelpArticleFullI18n(article, language);

        // 4. 增加浏览次数
        // 异步增加浏览次数，不影响主流程
        incrementBrowseCount(article.getId());

        return result;
    }

    /**
     * 增加文章浏览次数
     *
     * @param articleId 文章ID
     */
    @Async
    public void incrementBrowseCount(Long articleId) {
        try {
            // 异步处理，不影响主流程
            PromotionHelpArticleDO article = helpArticleMapper.selectById(articleId);
            if (article != null) {
                PromotionHelpArticleDO updateObj = new PromotionHelpArticleDO();
                updateObj.setId(articleId);
                updateObj.setBrowseCount(article.getBrowseCount() + 1);
                helpArticleMapper.updateById(updateObj);
            }
        } catch (Exception e) {
            // 异步方法中的异常不应影响主流程，所以这里捕获并记录日志
            log.error("[增加文章浏览次数]异步处理异常", e);
        }
    }

    @Override
    public boolean submitFeedback(Long articleId, Boolean isHelpful) {
        // 1. 参数校验
        if (articleId == null || isHelpful == null) {
            return false;
        }

        // 2. 查询文章信息
        PromotionHelpArticleDO article = helpArticleMapper.selectById(articleId);
        if (article == null || !CommonStatusEnum.ENABLE.getStatus().equals(article.getStatus())) {
            return false;
        }

        // 3. 更新文章的反馈计数
        PromotionHelpArticleDO updateObj = new PromotionHelpArticleDO();
        updateObj.setId(articleId);
        if (Boolean.TRUE.equals(isHelpful)) {
            updateObj.setHelpfulCount(article.getHelpfulCount() + 1);
        } else {
            updateObj.setUnhelpfulCount(article.getUnhelpfulCount() + 1);
        }

        return helpArticleMapper.updateById(updateObj) > 0;
    }

    @Override
    public PageResult<PromotionHelpArticleDO> getHelpArticlePageApp(AppPromotionHelpArticlePageReqVO pageReqVO, Locale language) {
        // 1. 查询文章列表，不包含内容字段
        PageResult<PromotionHelpArticleDO> pageResult = helpArticleMapper.selectPageApp(pageReqVO, CommonStatusEnum.ENABLE.getStatus());

        // 2. 处理多语言标题
        List<PromotionHelpArticleDO> articles = pageResult.getList().stream()
                .map(article -> PromotionI18nUtils.processHelpArticleTitleI18n(article, language))
                .collect(Collectors.toList());

        return new PageResult<>(articles, pageResult.getTotal());
    }

    @Override
    public PageResult<PromotionHelpArticleDO> searchHelpArticles(AppPromotionHelpArticleSearchReqVO reqVO, Locale language) {
        // 1. 参数校验
        if (StrUtil.isBlank(reqVO.getKeyword())) {
            return PageResult.empty();
        }

        // 2. 确定语言代码
        String lang = language != null ? language.getLanguage() : "en";

        // 3. 根据关键词和语言搜索文章
        PageResult<PromotionHelpArticleDO> pageResult = helpArticleMapper.searchArticlesByLanguage(
                reqVO, lang, CommonStatusEnum.ENABLE.getStatus());

        // 4. 处理多语言标题
        List<PromotionHelpArticleDO> articles = pageResult.getList().stream()
                .map(article -> PromotionI18nUtils.processHelpArticleTitleI18n(article, language))
                .collect(Collectors.toList());

        return new PageResult<>(articles, pageResult.getTotal());
    }
}