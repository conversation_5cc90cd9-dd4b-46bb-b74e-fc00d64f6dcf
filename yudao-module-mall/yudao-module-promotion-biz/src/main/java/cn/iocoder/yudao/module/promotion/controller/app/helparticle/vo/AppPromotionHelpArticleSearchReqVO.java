package cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "帮助文章搜索请求 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppPromotionHelpArticleSearchReqVO extends PageParam {

    @Schema(description = "搜索关键词", requiredMode = Schema.RequiredMode.REQUIRED, example = "密码")
    private String keyword;

    @Schema(description = "分类ID", example = "1")
    private Long categoryId; // 可选，用于在特定分类下搜索
}
