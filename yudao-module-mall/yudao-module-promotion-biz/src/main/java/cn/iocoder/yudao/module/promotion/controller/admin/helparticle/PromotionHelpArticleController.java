package cn.iocoder.yudao.module.promotion.controller.admin.helparticle;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.promotion.convert.help.HelpArticleConvert;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helpcategory.PromotionHelpCategoryDO;
import cn.iocoder.yudao.module.promotion.service.helpcategory.PromotionHelpCategoryService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;

import cn.iocoder.yudao.module.promotion.controller.admin.helparticle.vo.*;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helparticle.PromotionHelpArticleDO;
import cn.iocoder.yudao.module.promotion.service.helparticle.PromotionHelpArticleService;

@Tag(name = "管理后台 - 帮助文章")
@RestController
@RequestMapping("/promotion/help-article")
@Validated
public class PromotionHelpArticleController {

    @Resource
    private PromotionHelpArticleService helpArticleService;

    @Resource
    private PromotionHelpCategoryService helpCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建帮助文章")
    @PreAuthorize("@ss.hasPermission('promotion:help-article:create')")
    public CommonResult<Long> createHelpArticle(@Valid @RequestBody PromotionHelpArticleSaveReqVO createReqVO) {
        return success(helpArticleService.createHelpArticle(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新帮助文章")
    @PreAuthorize("@ss.hasPermission('promotion:help-article:update')")
    public CommonResult<Boolean> updateHelpArticle(@Valid @RequestBody PromotionHelpArticleSaveReqVO updateReqVO) {
        helpArticleService.updateHelpArticle(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除帮助文章")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('promotion:help-article:delete')")
    public CommonResult<Boolean> deleteHelpArticle(@RequestParam("id") Long id) {
        helpArticleService.deleteHelpArticle(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得帮助文章")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('promotion:help-article:query')")
    public CommonResult<PromotionHelpArticleRespVO> getHelpArticle(@RequestParam("id") Long id) {
        PromotionHelpArticleDO helpArticle = helpArticleService.getHelpArticle(id);
        return success(BeanUtils.toBean(helpArticle, PromotionHelpArticleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得帮助文章分页")
    @PreAuthorize("@ss.hasPermission('promotion:help-article:query')")
    public CommonResult<PageResult<PromotionHelpArticleRespVO>> getHelpArticlePage(@Valid PromotionHelpArticlePageReqVO pageReqVO) {
        PageResult<PromotionHelpArticleDO> pageResult = helpArticleService.getHelpArticlePage(pageReqVO);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal()));
        }

        Map<Long, PromotionHelpCategoryDO> helpCategoryMap = helpCategoryService.getHelpCategoryMap(convertList(pageResult.getList(),PromotionHelpArticleDO::getCategoryId));

        return success(new PageResult<>(HelpArticleConvert.INSTANCE.convertList(pageResult.getList(), helpCategoryMap),pageResult.getTotal()));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出帮助文章 Excel")
    @PreAuthorize("@ss.hasPermission('promotion:help-article:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportHelpArticleExcel(@Valid PromotionHelpArticlePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PromotionHelpArticleDO> list = helpArticleService.getHelpArticlePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "帮助文章.xls", "数据", PromotionHelpArticleRespVO.class,
                        BeanUtils.toBean(list, PromotionHelpArticleRespVO.class));
    }

}