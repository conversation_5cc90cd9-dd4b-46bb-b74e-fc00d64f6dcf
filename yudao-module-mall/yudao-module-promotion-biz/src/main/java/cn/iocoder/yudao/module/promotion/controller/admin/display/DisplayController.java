package cn.iocoder.yudao.module.promotion.controller.admin.display;

import cn.iocoder.yudao.module.promotion.convert.display.DisplayConvert;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.promotion.controller.admin.display.vo.*;
import cn.iocoder.yudao.module.promotion.dal.dataobject.display.DisplayDO;
import cn.iocoder.yudao.module.promotion.service.display.DisplayService;

@Tag(name = "管理后台 - 促销展示位")
@RestController
@RequestMapping("/promotion/display")
@Validated
public class DisplayController {

    @Resource
    private DisplayService displayService;

    @PostMapping("/create")
    @Operation(summary = "创建促销展示位")
    @PreAuthorize("@ss.hasPermission('promotion:display:create')")
    public CommonResult<Long> createDisplay(@Valid @RequestBody DisplaySaveReqVO createReqVO) {
        return success(displayService.createDisplay(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新促销展示位")
    @PreAuthorize("@ss.hasPermission('promotion:display:update')")
    public CommonResult<Boolean> updateDisplay(@Valid @RequestBody DisplaySaveReqVO updateReqVO) {
        displayService.updateDisplay(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除促销展示位")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('promotion:display:delete')")
    public CommonResult<Boolean> deleteDisplay(@RequestParam("id") Long id) {
        displayService.deleteDisplay(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得促销展示位")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('promotion:display:query')")
    public CommonResult<DisplayRespVO> getDisplay(@RequestParam("id") Long id) {
        DisplayDO display = displayService.getDisplay(id);
        return success(BeanUtils.toBean(display, DisplayRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得促销展示位分页")
    @PreAuthorize("@ss.hasPermission('promotion:display:query')")
    public CommonResult<PageResult<DisplayRespVO>> getDisplayPage(@Valid DisplayPageReqVO pageReqVO) {
        PageResult<DisplayDO> pageResult = displayService.getDisplayPage(pageReqVO);
        //return success(BeanUtils.toBean(pageResult, DisplayRespVO.class));
        PageResult<DisplayRespVO> displayRespVOPageResult = DisplayConvert.INSTANCE.convertPage(pageResult);
        return success(displayRespVOPageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出促销展示位 Excel")
    @PreAuthorize("@ss.hasPermission('promotion:display:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDisplayExcel(@Valid DisplayPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DisplayDO> list = displayService.getDisplayPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "促销展示位.xls", "数据", DisplayRespVO.class,
                        BeanUtils.toBean(list, DisplayRespVO.class));
    }

}