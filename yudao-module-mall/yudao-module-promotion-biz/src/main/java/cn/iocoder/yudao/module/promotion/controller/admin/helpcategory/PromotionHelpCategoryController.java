package cn.iocoder.yudao.module.promotion.controller.admin.helpcategory;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.promotion.controller.admin.helpcategory.vo.*;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helpcategory.PromotionHelpCategoryDO;
import cn.iocoder.yudao.module.promotion.service.helpcategory.PromotionHelpCategoryService;

@Tag(name = "管理后台 - 帮助分类")
@RestController
@RequestMapping("/promotion/help-category")
@Validated
public class PromotionHelpCategoryController {

    @Resource
    private PromotionHelpCategoryService helpCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建帮助分类")
    @PreAuthorize("@ss.hasPermission('promotion:help-category:create')")
    public CommonResult<Long> createHelpCategory(@Valid @RequestBody PromotionHelpCategorySaveReqVO createReqVO) {
        return success(helpCategoryService.createHelpCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新帮助分类")
    @PreAuthorize("@ss.hasPermission('promotion:help-category:update')")
    public CommonResult<Boolean> updateHelpCategory(@Valid @RequestBody PromotionHelpCategorySaveReqVO updateReqVO) {
        helpCategoryService.updateHelpCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除帮助分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('promotion:help-category:delete')")
    public CommonResult<Boolean> deleteHelpCategory(@RequestParam("id") Long id) {
        helpCategoryService.deleteHelpCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得帮助分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('promotion:help-category:query')")
    public CommonResult<PromotionHelpCategoryRespVO> getHelpCategory(@RequestParam("id") Long id) {
        PromotionHelpCategoryDO helpCategory = helpCategoryService.getHelpCategory(id);
        return success(BeanUtils.toBean(helpCategory, PromotionHelpCategoryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得帮助分类分页")
    @PreAuthorize("@ss.hasPermission('promotion:help-category:query')")
    public CommonResult<PageResult<PromotionHelpCategoryRespVO>> getHelpCategoryPage(@Valid PromotionHelpCategoryPageReqVO pageReqVO) {
        PageResult<PromotionHelpCategoryDO> pageResult = helpCategoryService.getHelpCategoryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PromotionHelpCategoryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出帮助分类 Excel")
    @PreAuthorize("@ss.hasPermission('promotion:help-category:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportHelpCategoryExcel(@Valid PromotionHelpCategoryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PromotionHelpCategoryDO> list = helpCategoryService.getHelpCategoryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "帮助分类.xls", "数据", PromotionHelpCategoryRespVO.class,
                        BeanUtils.toBean(list, PromotionHelpCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得帮助分类分页")
    @PreAuthorize("@ss.hasPermission('promotion:help-category:query')")
    public CommonResult<List<PromotionHelpCategoryRespVO>> getHelpCategoryList() {
        List<PromotionHelpCategoryDO> list = helpCategoryService.getHelpCategoryList(null);
        return success(BeanUtils.toBean(list, PromotionHelpCategoryRespVO.class));
    }

}