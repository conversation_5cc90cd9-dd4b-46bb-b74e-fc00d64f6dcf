package cn.iocoder.yudao.module.promotion.controller.app.helpcategory;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLanguage;

import cn.iocoder.yudao.module.promotion.controller.app.helpcategory.vo.*;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpCategoryArticleRespVO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helpcategory.PromotionHelpCategoryDO;
import cn.iocoder.yudao.module.promotion.service.helpcategory.PromotionHelpCategoryService;

@Tag(name = "用户 APP - 帮助分类")
@RestController
@RequestMapping("/promotion/help-category")
@Validated
public class AppPromotionHelpCategoryController {

    @Resource
    private PromotionHelpCategoryService helpCategoryService;

    @GetMapping("/get")
    @Operation(summary = "获得帮助分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<AppPromotionHelpCategoryRespVO> getHelpCategory(@RequestParam("id") Long id) {
        PromotionHelpCategoryDO helpCategory = helpCategoryService.getHelpCategory(id);
        return success(BeanUtils.toBean(helpCategory, AppPromotionHelpCategoryRespVO.class));
    }

    //@GetMapping("/page")
    //@Operation(summary = "获得帮助分类分页")
    //public CommonResult<PageResult<AppPromotionHelpCategoryRespVO>> getHelpCategoryPage(@Valid AppPromotionHelpCategoryPageReqVO pageReqVO) {
    //    PageResult<PromotionHelpCategoryDO> pageResult = helpCategoryService.getHelpCategoryPage(pageReqVO);
    //    return success(BeanUtils.toBean(pageResult, AppPromotionHelpCategoryRespVO.class));
    //}

    @GetMapping("/simple-list")
    @Operation(summary = "获得帮助分类列表")
    @PermitAll
    public CommonResult<List<AppPromotionHelpCategoryRespVO>> getHelpCategoryList() {
        List<PromotionHelpCategoryDO> list = helpCategoryService.getHelpCategoryListByLang(getCurrentLanguage());
        return success(BeanUtils.toBean(list, AppPromotionHelpCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得帮助分类及其文章列表，按分类分组")
    @PermitAll
    public CommonResult<List<AppPromotionHelpCategoryArticleRespVO>> getHelpCategoryArticleList() {
        List<AppPromotionHelpCategoryArticleRespVO> list = helpCategoryService.getHelpCategoryArticleList(getCurrentLanguage());
        return success(list);
    }

}