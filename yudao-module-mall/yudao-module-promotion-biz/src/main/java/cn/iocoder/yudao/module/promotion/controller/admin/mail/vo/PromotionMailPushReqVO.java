package cn.iocoder.yudao.module.promotion.controller.admin.mail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 促销邮件 推送 Request VO")
@Data
public class PromotionMailPushReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "569")
    private Long id;

    @Schema(description = "测试邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mail;


}