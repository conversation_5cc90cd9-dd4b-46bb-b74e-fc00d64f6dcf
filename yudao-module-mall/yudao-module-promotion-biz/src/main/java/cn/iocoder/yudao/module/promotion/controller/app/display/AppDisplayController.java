package cn.iocoder.yudao.module.promotion.controller.app.display;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.web.core.context.LanguageContext;
import cn.iocoder.yudao.module.promotion.controller.app.banner.vo.AppBannerRespVO;
import cn.iocoder.yudao.module.promotion.controller.app.display.vo.AppDisplayRespVO;
import cn.iocoder.yudao.module.promotion.convert.banner.BannerConvert;
import cn.iocoder.yudao.module.promotion.dal.dataobject.banner.BannerDO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.display.DisplayDO;
import cn.iocoder.yudao.module.promotion.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.promotion.service.display.DisplayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * @program: ruoyi-vue-pro
 * @description: 促销展示位控制器
 * @author: DingXiao
 * @create: 2024-11-22 20:58
 **/
@Tag(name = "用户 APP - 促销展示位")
@RestController
@RequestMapping("/promotion/display")
@Validated
public class AppDisplayController {

    @Resource
    private DisplayService displayService;

    @GetMapping("/get/{position}")
    @Operation(summary = "获得 展示位 列表")
    @Parameter(name = "position", description = "Display position", example = "1")
    @Parameter(name = "tag", description = "Display Tag", example = "hot")
    @PermitAll
    public CommonResult<AppDisplayRespVO> getDisplay(@PathVariable("position") Integer position, @RequestParam(value = "tag", required = false) String tag) {
        AppDisplayRespVO display = displayService.getDisplayByPosition(position,tag);
        return success(display);
    }

    @GetMapping("/list")
    @Operation(summary = "获得 展示位 列表")
    @Parameter(name = "position", description = "Display position", example = "1")
    @PermitAll
    @Cacheable(cacheNames = RedisKeyConstants.DISPLAY_LIST+"#7200" , key = "#position ?: 'defaultPosition'") //2小时缓存
    public CommonResult<List<AppDisplayRespVO>> getDisplayList(@RequestParam(value = "position", required = false) Integer position) {
        List<AppDisplayRespVO> displayList = displayService.getDisplayListByPosition(position);
        return success(displayList);
    }
}
