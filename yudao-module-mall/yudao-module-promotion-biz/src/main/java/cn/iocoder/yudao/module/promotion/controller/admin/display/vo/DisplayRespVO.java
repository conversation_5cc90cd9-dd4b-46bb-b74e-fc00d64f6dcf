package cn.iocoder.yudao.module.promotion.controller.admin.display.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 促销展示位 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DisplayRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13390")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "展示位标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("展示位标题")
    private String title;

    @Schema(description = "展示位副标题")
    @ExcelProperty("展示位副标题")
    private String subTitle;

    @Schema(description = "图片 URL", example = "https://www.iocoder.cn")
    @ExcelProperty("图片 URL")
    private String picUrl;

    @Schema(description = "跳转地址", example = "https://www.iocoder.cn")
    @ExcelProperty("跳转地址")
    private String url;

    @Schema(description = "标签;标签，可用来区分具体位置")
    @ExcelProperty("标签;标签，可用来区分具体位置")
    private String tag;

    @Schema(description = "商品 SPU 编号集合")
    @ExcelProperty("商品 SPU 编号集合")
    private Set<Long>  spuIds;

    @Schema(description = "商品 SKU 编号集合")
    @ExcelProperty("商品 SKU 编号集合")
    private Set<Long>  skuIds;

    @Schema(description = "活动状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "活动状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "位置", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "位置", converter = DictConvert.class)
    @DictFormat("promotion_display_position") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer position;

    @Schema(description = "描述", example = "描述信息")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String memo;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    @Schema(description = "展示位标题英文")
    private String titleEn;

    @Schema(description = "展示位副标题英文")
    private String subTitleEn;

    @Schema(description = "描述英文")
    private String descriptionEn;

    @Schema(description = "展示位标题德语")
    private String titleDe;

    @Schema(description = "展示位副标题德语")
    private String subTitleDe;

    @Schema(description = "描述德语")
    private String descriptionDe;

    @Schema(description = "展示位标题法语")
    private String titleFr;

    @Schema(description = "展示位副标题法语")
    private String subTitleFr;

    @Schema(description = "描述法语")
    private String descriptionFr;

    @Schema(description = "展示位标题西语")
    private String titleEs;

    @Schema(description = "展示位副标题西语")
    private String subTitleEs;

    @Schema(description = "描述西语")
    private String descriptionEs;

    @Schema(description = "展示位标题阿文")
    private String titleAr;

    @Schema(description = "展示位副标题阿文")
    private String subTitleAr;

    @Schema(description = "描述阿文")
    private String descriptionAr;

}