package cn.iocoder.yudao.module.promotion.controller.admin.helparticle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 帮助文章 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PromotionHelpArticleRespVO {

    @Schema(description = "帮助文章编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21786")
    @ExcelProperty("帮助文章编号")
    private Long id;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4141")
    @ExcelProperty("分类编号")
    private Long categoryId;

    @Schema(description = "分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "新手指导")
    @ExcelProperty("分类")
    private String categoryTitle;

    @Schema(description = "文章编码;用于前端路由", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("文章编码;用于前端路由")
    private String code;

    @Schema(description = "文章作者")
    @ExcelProperty("文章作者")
    private String author;

    @Schema(description = "浏览次数", example = "2397")
    @ExcelProperty("浏览次数")
    private Integer browseCount;

    @Schema(description = "中文标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("中文标题")
    private String titleZh;

    @Schema(description = "英文标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("英文标题")
    private String titleEn;

    @Schema(description = "法语标题")
    @ExcelProperty("法语标题")
    private String titleFr;

    @Schema(description = "德语标题")
    @ExcelProperty("德语标题")
    private String titleDe;

    @Schema(description = "西班牙语标题")
    @ExcelProperty("西班牙语标题")
    private String titleEs;

    @Schema(description = "阿拉伯语标题")
    @ExcelProperty("阿拉伯语标题")
    private String titleAr;

    @Schema(description = "中文内容")
    @ExcelProperty("中文内容")
    private String contentZh;

    @Schema(description = "英文内容")
    @ExcelProperty("英文内容")
    private String contentEn;

    @Schema(description = "法语内容")
    @ExcelProperty("法语内容")
    private String contentFr;

    @Schema(description = "德语内容")
    @ExcelProperty("德语内容")
    private String contentDe;

    @Schema(description = "西班牙语内容")
    @ExcelProperty("西班牙语内容")
    private String contentEs;

    @Schema(description = "阿拉伯语内容")
    @ExcelProperty("阿拉伯语内容")
    private String contentAr;

    @Schema(description = "相关文章ID;用逗号分隔，如1,2,3")
    @ExcelProperty("相关文章ID;用逗号分隔，如1,2,3")
    private Set<Long> relatedArticles;

    @Schema(description = "有帮助反馈数量", example = "21810")
    @ExcelProperty("有帮助反馈数量")
    private Integer helpfulCount;

    @Schema(description = "无帮助反馈数量", example = "4529")
    @ExcelProperty("无帮助反馈数量")
    private Integer unhelpfulCount;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "是否FAQ;0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否FAQ;0-否，1-是")
    private Boolean faq;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}