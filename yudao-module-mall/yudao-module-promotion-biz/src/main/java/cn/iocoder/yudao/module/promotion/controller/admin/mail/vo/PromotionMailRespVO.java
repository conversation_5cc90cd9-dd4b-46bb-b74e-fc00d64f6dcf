package cn.iocoder.yudao.module.promotion.controller.admin.mail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 促销邮件 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PromotionMailRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "569")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "展示位标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("展示位标题")
    private String title;

    @Schema(description = "模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模板ID")
    private String mailTemplateId;

    @Schema(description = "模板编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模板编码")
    private String mailTemplateCode;

    @Schema(description = "类别", example = "1")
    @ExcelProperty(value = "类别", converter = DictConvert.class)
    @DictFormat("promotion_mail_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer type;

    @Schema(description = "内容编号集合")
    private Set<Long> contentIds;

    @Schema(description = "活动状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "活动状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "用户类别;0所有用户，1指定用户，2下单 过的用户，", example = "1")
    @ExcelProperty(value = "用户类别;0所有用户，1指定用户，2下单 过的用户，", converter = DictConvert.class)
    @DictFormat("promotion_user_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer userType;

    @Schema(description = "用户集合")
    private Set<Long> userIds;

    @Schema(description = "发送时间")
    @ExcelProperty("发送时间")
    private LocalDateTime sendTime;

    @Schema(description = "已推送次数", example = "26125")
    @ExcelProperty("已推送次数")
    private Integer pushCount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}