package cn.iocoder.yudao.module.promotion.controller.app.helparticle;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLanguage;

import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.*;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpArticleFeedbackReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpArticleSearchReqVO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helparticle.PromotionHelpArticleDO;
import cn.iocoder.yudao.module.promotion.service.helparticle.PromotionHelpArticleService;

@Tag(name = "用户 APP - 帮助文章")
@RestController
@RequestMapping("/promotion/help-article")
@Validated
public class AppPromotionHelpArticleController {

    @Resource
    private PromotionHelpArticleService helpArticleService;


    @GetMapping("/get")
    @Operation(summary = "获得帮助文章")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<AppPromotionHelpArticleSimpleRespVO> getHelpArticle(@RequestParam("id") Long id) {
        //PromotionHelpArticleDO helpArticle = helpArticleService.getHelpArticle(id);
        //return success(BeanUtils.toBean(helpArticle, AppPromotionHelpArticleRespVO.class));

        PromotionHelpArticleDO helpArticle = helpArticleService.getHelpArticle(id,getCurrentLanguage());
        return success(BeanUtils.toBean(helpArticle, AppPromotionHelpArticleSimpleRespVO.class));
    }

    @GetMapping("/detail/{category}/{article}")
    @Operation(summary = "获得帮助文章")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<AppPromotionHelpArticleSimpleRespVO> getHelpArticleByCode(@PathVariable("category") String category,@PathVariable("article") String article) {
        PromotionHelpArticleDO helpArticle = helpArticleService.getHelpArticleByCode(category, article,getCurrentLanguage());
        return success(BeanUtils.toBean(helpArticle, AppPromotionHelpArticleSimpleRespVO.class));
    }

    //@GetMapping("/page")
    //@Operation(summary = "获得帮助文章分页")
    //public CommonResult<PageResult<AppPromotionHelpArticleRespVO>> getHelpArticlePage(@Valid AppPromotionHelpArticlePageReqVO pageReqVO) {
    //    PageResult<PromotionHelpArticleDO> pageResult = helpArticleService.getHelpArticlePageApp(pageReqVO);
    //    return success(BeanUtils.toBean(pageResult, AppPromotionHelpArticleRespVO.class));
    //}

    @GetMapping("/list")
    @Operation(summary = "获得帮助文章列表")
    @PermitAll
    public CommonResult<List<AppPromotionHelpArticleRespVO>> getHelpArticleList() {
        List<PromotionHelpArticleDO> list = helpArticleService.getHelpArticleList();
        return success(BeanUtils.toBean(list, AppPromotionHelpArticleRespVO.class));
    }

    @PostMapping("/feedback")
    @Operation(summary = "提交文章反馈")
    @PermitAll
    public CommonResult<Boolean> submitFeedback(@RequestBody @Valid AppPromotionHelpArticleFeedbackReqVO reqVO) {
        boolean result = helpArticleService.submitFeedback(reqVO.getArticleId(), reqVO.getIsHelpful());
        return success(result);
    }

    @GetMapping("/page")
    @Operation(summary = "获得帮助文章分页")
    @PermitAll
    public CommonResult<PageResult<AppPromotionHelpArticleRespVO>> getHelpArticlePage(@Valid AppPromotionHelpArticlePageReqVO pageReqVO) {
        PageResult<PromotionHelpArticleDO> pageResult = helpArticleService.getHelpArticlePageApp(pageReqVO, getCurrentLanguage());
        return success(BeanUtils.toBean(pageResult, AppPromotionHelpArticleRespVO.class));
    }

    @GetMapping("/search")
    @Operation(summary = "搜索帮助文章")
    @PermitAll
    public CommonResult<PageResult<AppPromotionHelpArticleRespVO>> searchHelpArticles(@Valid AppPromotionHelpArticleSearchReqVO reqVO) {
        PageResult<PromotionHelpArticleDO> pageResult = helpArticleService.searchHelpArticles(reqVO, getCurrentLanguage());
        return success(BeanUtils.toBean(pageResult, AppPromotionHelpArticleRespVO.class));
    }

}