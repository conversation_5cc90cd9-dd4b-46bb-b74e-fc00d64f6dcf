package cn.iocoder.yudao.module.promotion.controller.app.article;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.promotion.controller.app.article.vo.article.AppArticlePageReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.article.vo.article.AppArticleRespVO;
import cn.iocoder.yudao.module.promotion.convert.article.ArticleConvert;
import cn.iocoder.yudao.module.promotion.dal.dataobject.article.ArticleDO;
import cn.iocoder.yudao.module.promotion.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.promotion.service.article.ArticleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.string.StrUtils.generateSlug;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLanguage;

@Tag(name = "用户 APP - 文章")
@RestController
@RequestMapping("/promotion/article")
@Validated
public class AppArticleController {

    @Resource
    private ArticleService articleService;

    @RequestMapping("/list")
    @Operation(summary = "获得文章详情列表")
    @Parameters({
            @Parameter(name = "recommendHot", description = "是否热门", example = "false"), // 场景一：查看指定的文章
            @Parameter(name = "recommendBanner", description = "是否轮播图", example = "false") // 场景二：查看指定的文章
    })
    @PermitAll
    public CommonResult<List<AppArticleRespVO>> getArticleList(
            @RequestParam(value = "recommendHot", required = false) Boolean recommendHot,
            @RequestParam(value = "recommendBanner", required = false) Boolean recommendBanner) {
        return success(ArticleConvert.INSTANCE.convertList03(
                articleService.getArticleCategoryListByRecommend(recommendHot, recommendBanner)));
    }

    @RequestMapping("/page")
    @Operation(summary = "获得文章详情分页")
    @PermitAll
    public CommonResult<PageResult<AppArticleRespVO>> getArticlePage(AppArticlePageReqVO pageReqVO) {

        PageResult<AppArticleRespVO> pageResult = articleService.getArticlePage(pageReqVO,getCurrentLanguage().getLanguage());
        return success(pageResult);
    }

    @RequestMapping("/get")
    @Operation(summary = "获得文章详情")
    @Parameters({
            @Parameter(name = "id", description = "文章编号", example = "1024"),
            @Parameter(name = "title", description = "文章标题", example = "1024"),
    })
    @PermitAll
    public CommonResult<AppArticleRespVO> getArticle(@RequestParam(value = "id", required = false) Long id,
                                                     @RequestParam(value = "title", required = false) String title) {
        AppArticleRespVO resp = articleService.getArticleById(id,getCurrentLanguage().getLanguage());
        return success(resp);
    }

    @PutMapping("/add-browse-count")
    @Operation(summary = "增加文章浏览量")
    @Parameter(name = "id", description = "文章编号", example = "1024")
    @PermitAll
    public CommonResult<Boolean> addBrowseCount(@RequestParam("id") Long id) {
        articleService.addArticleBrowseCount(id);
        return success(true);
    }

    @GetMapping("/urls/{categoryId}/{domain}")
    @Operation(summary = "获取所有Blog的Url列表,给sitemap使用")
    @PermitAll
    @Cacheable(cacheNames = RedisKeyConstants.BLOG_URLS+"#43200",key = "#domain") //12小时缓存
    public List<String> getBlogUrls(@PathVariable("categoryId") Long categoryId,@PathVariable("domain") String domain){

        List<ArticleDO> list = articleService.getListByCategoryId(categoryId);
        List<String> blogUrls = new ArrayList<>();

        // 拼接每个商品的完整 URL
        for (ArticleDO article : list) {
            String blogUrl = formatCategoryUrl(article,domain);
            blogUrls.add(blogUrl);
        }

        return blogUrls;
    }

    private String formatCategoryUrl(ArticleDO article,String domain) {
        return "https://www."+domain + ".com/blog/" + article.getId()+"/"+generateSlug(article.getTitleEn());
    }

}