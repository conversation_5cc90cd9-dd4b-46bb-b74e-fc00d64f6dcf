package cn.iocoder.yudao.module.promotion.controller.admin.mail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 促销邮件新增/修改 Request VO")
@Data
public class PromotionMailSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "569")
    private Long id;

    @Schema(description = "展示位标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "展示位标题不能为空")
    private String title;

    @Schema(description = "邮件模板编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22445")
    @NotNull(message = "邮件模板编号不能为空")
    private Long mailTemplateId;

    @Schema(description = "模板编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "模板编码不能为空")
    private String mailTemplateCode;

    @Schema(description = "图片")
    private String banner;

    @Schema(description = "邮件参数", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mailTemplateParams;

    @Schema(description = "类别;1商品，2优惠券", example = "1")
    private Integer type;

    @Schema(description = "活动状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "活动状态不能为空")
    private Integer status;

    @Schema(description = "内容编号集合;商品或优惠券编号")
    private Set<Long> contentIds;

    @Schema(description = "用户类别;0所有用户，1指定用户，2下单 过的用户，", example = "1")
    private Integer userType;

    @Schema(description = "用户编号集合")
    private Set<Long> userIds;

    @Schema(description = "发送时间")
    private LocalDateTime sendTime;

    @Schema(description = "已推送次数", example = "26125")
    private Integer pushCount;

    @Schema(description = "备注", example = "随便")
    private String memo;

}