package cn.iocoder.yudao.module.promotion.controller.admin.display.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 促销展示位新增/修改 Request VO")
@Data
public class DisplaySaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13390")
    private Long id;

    @Schema(description = "展示位标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "展示位标题不能为空")
    private String title;

    @Schema(description = "展示位副标题")
    private String subTitle;

    @Schema(description = "图片 URL", example = "https://www.iocoder.cn")
    private String picUrl;

    @Schema(description = "跳转地址", example = "https://www.iocoder.cn")
    private String url;

    @Schema(description = "标签;标签，可用来区分具体位置")
    private String tag;

    @Schema(description = "商品 SPU 编号集合")
    private Set<Long>  spuIds;

    @Schema(description = "商品 SKU 编号集合")
    private Set<Long>  skuIds;

    @Schema(description = "活动状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "活动状态不能为空")
    private Integer status;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "位置", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "位置不能为空")
    private Integer position;

    @Schema(description = "描述", example = "你说的对")
    private String description;

    @Schema(description = "备注", example = "你说的对")
    private String memo;

    @Schema(description = "展示位点击次数", example = "29846")
    private Integer browseCount;

    @Schema(description = "展示位标题英语")
    private String titleEn;

    @Schema(description = "展示位副标题英语")
    private String subTitleEn;

    @Schema(description = "描述英文", example = "你说的对")
    private String descriptionEn;

    @Schema(description = "展示位标题德语")
    private String titleDe;

    @Schema(description = "展示位副标题德语")
    private String subTitleDe;

    @Schema(description = "描述德语", example = "你说的对")
    private String descriptionDe;

    @Schema(description = "展示位标题法语")
    private String titleFr;

    @Schema(description = "展示位副标题法语")
    private String subTitleFr;

    @Schema(description = "描述法语", example = "你说的对")
    private String descriptionFr;

    @Schema(description = "展示位标题西语")
    private String titleEs;

    @Schema(description = "展示位副标题西语")
    private String subTitleEs;

    @Schema(description = "描述西语", example = "你说的对")
    private String descriptionEs;

    @Schema(description = "展示位标题阿语")
    private String titleAr;

    @Schema(description = "展示位副标题阿语")
    private String subTitleAr;

    @Schema(description = "描述阿语", example = "你说的对")
    private String descriptionAr;

}