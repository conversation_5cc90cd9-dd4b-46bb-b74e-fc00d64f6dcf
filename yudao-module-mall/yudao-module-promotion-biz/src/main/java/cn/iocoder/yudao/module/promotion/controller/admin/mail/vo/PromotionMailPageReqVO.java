package cn.iocoder.yudao.module.promotion.controller.admin.mail.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 促销邮件分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PromotionMailPageReqVO extends PageParam {

    @Schema(description = "展示位标题")
    private String title;

    @Schema(description = "类别;1商品，2优惠券", example = "1")
    private Integer type;

    @Schema(description = "用户类别;0所有用户，1指定用户，2下单 过的用户，", example = "1")
    private Integer userType;

}