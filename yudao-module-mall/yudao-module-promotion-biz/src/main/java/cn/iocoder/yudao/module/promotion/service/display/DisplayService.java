package cn.iocoder.yudao.module.promotion.service.display;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.promotion.controller.admin.display.vo.*;
import cn.iocoder.yudao.module.promotion.controller.app.display.vo.AppDisplayRespVO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.display.DisplayDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 促销展示位 Service 接口
 *
 * <AUTHOR>
 */
public interface DisplayService {

    /**
     * 创建促销展示位
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDisplay(@Valid DisplaySaveReqVO createReqVO);

    /**
     * 更新促销展示位
     *
     * @param updateReqVO 更新信息
     */
    void updateDisplay(@Valid DisplaySaveReqVO updateReqVO);

    /**
     * 删除促销展示位
     *
     * @param id 编号
     */
    void deleteDisplay(Long id);

    /**
     * 获得促销展示位
     *
     * @param id 编号
     * @return 促销展示位
     */
    DisplayDO getDisplay(Long id);

    /**
     * 获得促销展示位分页
     *
     * @param pageReqVO 分页查询
     * @return 促销展示位分页
     */
    PageResult<DisplayDO> getDisplayPage(DisplayPageReqVO pageReqVO);

    /**
     * 获得促销展示位列表
     *
     * @param position 位置
     * @param tag 标签
     * @return 促销展示位列表
     */
    AppDisplayRespVO getDisplayByPosition(Integer position, String tag);

    /**
     * 获得促销展示位列表
     *
     * @param position 位置
     * @return 促销展示位列表
     */
    List<AppDisplayRespVO> getDisplayListByPosition(Integer position);
}