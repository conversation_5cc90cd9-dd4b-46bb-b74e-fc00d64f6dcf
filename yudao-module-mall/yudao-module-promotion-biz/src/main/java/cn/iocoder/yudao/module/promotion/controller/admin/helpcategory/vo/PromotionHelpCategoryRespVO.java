package cn.iocoder.yudao.module.promotion.controller.admin.helpcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 帮助分类 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PromotionHelpCategoryRespVO {

    @Schema(description = "帮助分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15312")
    @ExcelProperty("帮助分类编号")
    private Long id;

    @Schema(description = "分类编码;用于前端路由", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分类编码;用于前端路由")
    private String code;

    @Schema(description = "图标")
    @ExcelProperty("图标")
    private String icon;

    @Schema(description = "中文标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("中文标题")
    private String titleZh;

    @Schema(description = "英文标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("英文标题")
    private String titleEn;

    /**
     * 是否推荐
     */
    @Schema(description = "是否推荐", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否推荐")
    private Boolean recommend;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}