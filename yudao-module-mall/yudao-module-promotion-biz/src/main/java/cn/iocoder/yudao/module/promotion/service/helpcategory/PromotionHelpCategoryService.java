package cn.iocoder.yudao.module.promotion.service.helpcategory;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.promotion.controller.admin.helpcategory.vo.PromotionHelpCategoryPageReqVO;
import cn.iocoder.yudao.module.promotion.controller.admin.helpcategory.vo.PromotionHelpCategorySaveReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpCategoryArticleRespVO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helpcategory.PromotionHelpCategoryDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 帮助分类 Service 接口
 *
 * <AUTHOR>
 */
public interface PromotionHelpCategoryService {

    /**
     * 创建帮助分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHelpCategory(@Valid PromotionHelpCategorySaveReqVO createReqVO);

    /**
     * 更新帮助分类
     *
     * @param updateReqVO 更新信息
     */
    void updateHelpCategory(@Valid PromotionHelpCategorySaveReqVO updateReqVO);

    /**
     * 删除帮助分类
     *
     * @param id 编号
     */
    void deleteHelpCategory(Long id);

    /**
     * 获得帮助分类
     *
     * @param id 编号
     * @return 帮助分类
     */
    PromotionHelpCategoryDO getHelpCategory(Long id);

    /**
     * 获得帮助分类分页
     *
     * @param pageReqVO 分页查询
     * @return 帮助分类分页
     */
    PageResult<PromotionHelpCategoryDO> getHelpCategoryPage(PromotionHelpCategoryPageReqVO pageReqVO);


    /**
     * 获得帮助分类列表
     *
     * @return 帮助分类列表
     */
    List<PromotionHelpCategoryDO> getHelpCategoryList(Collection<Long> ids);

    default Map<Long, PromotionHelpCategoryDO> getHelpCategoryMap(Collection<Long> ids){
        List<PromotionHelpCategoryDO> list = getHelpCategoryList(ids);
        return CollectionUtils.convertMap(list, PromotionHelpCategoryDO::getId);
    }


    /**
     * 根据语言获得帮助分类列表
     *
     * @return 帮助分类列表
     */
    List<PromotionHelpCategoryDO> getHelpCategoryListByLang(Locale language);

    /**
     * 获取帮助分类及其文章列表，按分类分组
     *
     * @param language 语言
     * @return 帮助分类及其文章列表
     */
    List<AppPromotionHelpCategoryArticleRespVO> getHelpCategoryArticleList(Locale language);
}