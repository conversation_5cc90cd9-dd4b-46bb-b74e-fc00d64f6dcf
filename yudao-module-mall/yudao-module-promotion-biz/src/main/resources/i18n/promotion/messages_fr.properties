# Activité de promotion 1-013-001-000
promotion.discount_activity.not_exists=L'activité de réduction à durée limitée n'existe pas
promotion.discount_activity.spu_conflicts=Certains produits participent déjà à une autre activité de réduction [{}]
promotion.discount_activity.update_fail_status_closed=L'activité de réduction est fermée et ne peut pas être modifiée
promotion.discount_activity.delete_fail_status_not_closed=L'activité de réduction n'est pas fermée et ne peut pas être supprimée
promotion.discount_activity.close_fail_status_closed=L'activité de réduction est déjà fermée et ne peut pas être fermée à nouveau

# Bannière 1-013-002-000
promotion.banner.not_exists=La bannière n'existe pas

# Modèle de coupon 1-013-004-000
promotion.coupon_template.not_exists=Le modèle de coupon n'existe pas
promotion.coupon_template.total_count_too_small=La quantité totale ne peut pas être inférieure à la quantité déjà réclamée ({})
promotion.coupon_template.not_enough=La quantité restante est insuffisante pour la réclamation
promotion.coupon_template.user_already_take=Vous avez déjà réclamé ce coupon
promotion.coupon_template.expired=Le coupon a expiré
promotion.coupon_template.cannot_take=La méthode de réclamation est incorrecte

# Coupon 1-013-005-000
promotion.coupon.not_exists=Le coupon n'existe pas
promotion.coupon.delete_fail_used=Impossible de supprimer le coupon, il a déjà été utilisé
promotion.coupon.status_not_unused=Le coupon n'est pas dans un état inutilisé
promotion.coupon.valid_time_not_now=Le coupon n'est pas valide à cette heure
promotion.coupon.status_not_used=Le coupon n'est pas dans un état utilisé

# Activité de réduction totale 1-013-006-000
promotion.reward_activity.not_exists=L'activité de réduction totale n'existe pas
promotion.reward_activity.spu_conflicts=Certains produits participent déjà à une autre activité de réduction totale
promotion.reward_activity.update_fail_status_closed=L'activité est fermée et ne peut pas être modifiée
promotion.reward_activity.delete_fail_status_not_closed=L'activité n'est pas fermée et ne peut pas être supprimée
promotion.reward_activity.close_fail_status_closed=L'activité est déjà fermée
promotion.reward_activity.scope_exists=Le champ des produits est en conflit avec une autre activité de réduction totale [{}] : {}

# Activité de boutique de points 1-013-007-000
promotion.point_activity.not_exists=L'activité de boutique de points n'existe pas
promotion.point_activity.spu_conflicts=Certains produits participent déjà à une autre activité de boutique de points
promotion.point_activity.update_fail_status_closed=L'activité est fermée et ne peut pas être modifiée
promotion.point_activity.delete_fail_status_not_closed_or_end=L'activité n'est pas fermée ou terminée et ne peut pas être supprimée
promotion.point_activity.close_fail_status_closed=L'activité est déjà fermée
promotion.point_activity.join_activity_status_closed=Échec de l'échange : l'activité est fermée
promotion.point_activity.join_activity_single_limit_count_exceed=Échec de l'échange : la limite d'achat unique est dépassée
promotion.point_activity.join_activity_product_not_exists=Échec de l'échange : le produit n'existe pas
promotion.point_activity.update_stock_fail=Échec de l'échange : le stock est insuffisant

# Activité de vente flash 1-013-008-000
promotion.seckill_activity.not_exists=L'activité de vente flash n'existe pas
promotion.seckill_activity.spu_conflicts=Certains produits participent déjà à une autre vente flash, conflit d'horaires
promotion.seckill_activity.update_fail_status_closed=L'activité est fermée et ne peut pas être modifiée
promotion.seckill_activity.delete_fail_status_not_closed_or_end=L'activité n'est pas fermée ou terminée et ne peut pas être supprimée
promotion.seckill_activity.close_fail_status_closed=L'activité est déjà fermée
promotion.seckill_activity.update_stock_fail=Échec de la vente flash : stock insuffisant
promotion.seckill_activity.time_error=Échec de la vente flash : hors de la plage horaire
promotion.seckill_activity.status_closed=Échec de la vente flash : l'activité est fermée
promotion.seckill_activity.single_limit_count_exceed=Échec de la vente flash : limite d'achat unique dépassée
promotion.seckill_activity.product_not_exists=Échec de la vente flash : le produit n'existe pas

# Créneau horaire de vente flash 1-013-009-000
promotion.seckill_config.not_exists=Le créneau horaire de vente flash n'existe pas
promotion.seckill_config.time_conflicts=Conflit d'horaires pour le créneau de vente flash
promotion.seckill_config.disable=Le créneau horaire est désactivé

# Activité d'achat groupé 1-013-010-000
promotion.combination_activity.not_exists=L'activité d'achat groupé n'existe pas
promotion.combination_activity.spu_conflicts=Certains produits participent déjà à une autre activité d'achat groupé
promotion.combination_activity.status_disable_not_update=L'activité est fermée et ne peut pas être modifiée
promotion.combination_activity.delete_fail_status_not_closed_or_end=L'activité n'est pas fermée ou terminée
promotion.combination_activity.status_disable=Échec de l'achat groupé : l'activité est fermée
promotion.combination_activity.product_not_exists=Échec de l'achat groupé : le produit n'existe pas
promotion.combination_activity.update_stock_fail=Échec de l'achat groupé : stock insuffisant

# Enregistrement d'achat groupé 1-013-011-000
promotion.combination_record.not_exists=L'enregistrement d'achat groupé n'existe pas
promotion.combination_record.exists=Échec : vous avez déjà participé à cet achat groupé
promotion.combination_record.head_not_exists=Échec : l'achat groupé parent n'existe pas
promotion.combination_record.user_full=Échec : le groupe est complet
promotion.combination_record.failed_have_joined=Échec : un enregistrement actif existe déjà
promotion.combination_record.failed_time_not_start=Échec : l'activité n'a pas encore commencé
promotion.combination_record.failed_time_end=Échec : l'activité est terminée
promotion.combination_record.failed_single_limit_count_exceed=Échec : limite d'achat unique dépassée
promotion.combination_record.failed_total_limit_count_exceed=Échec : limite d'achat totale dépassée
promotion.combination_record.failed_order_status_unpaid=Échec : commande non payée, veuillez payer

# Activité d'enchère 1-013-012-000
promotion.bargain_activity.not_exists=L'activité d'enchère n'existe pas
promotion.bargain_activity.spu_conflicts=Certains produits participent déjà à une autre enchère
promotion.bargain_activity.status_disable=L'activité d'enchère est fermée
promotion.bargain_activity.delete_fail_status_not_closed_or_end=L'activité d'enchère n'est pas fermée ou terminée
promotion.bargain_activity.stock_not_enough=Stock insuffisant pour l'enchère
promotion.bargain_activity.status_closed=L'activité d'enchère est déjà fermée
promotion.bargain_activity.time_end=L'activité d'enchère est terminée

# Enregistrement d'enchère 1-013-013-000
promotion.bargain_record.not_exists=L'enregistrement d'enchère n'existe pas
promotion.bargain_record.create_fail_exists=Échec : vous participez déjà à cette enchère
promotion.bargain_record.create_fail_limit=Échec : limite de participation atteinte
promotion.bargain_record.join_not_success=Échec de la commande : l'enchère a échoué
promotion.bargain_record.already_order=Échec de la commande : l'enchère a déjà été commandée

# Aide à l'enchère 1-013-014-000
promotion.bargain_help.not_in_process=Échec de l'aide : l'enchère n'est pas en cours
promotion.bargain_help.record_self=Échec de l'aide : vous ne pouvez pas vous aider vous-même
promotion.bargain_help.create_fail_limit=Échec de l'aide : limite atteinte
promotion.bargain_help.create_fail_conflict=Échec de l'aide : veuillez réessayer
promotion.bargain_help.help_exists=Échec de l'aide : vous avez déjà aidé

# Catégorie d'article 1-013-015-000
promotion.article_category.not_exists=La catégorie d'article n'existe pas
promotion.article_category.delete_fail_have_articles=Échec de la suppression : des articles sont associés

# Gestion des articles 1-013-016-000
promotion.article.not_exists=L'article n'existe pas

# Modèle DIY 1-013-017-000
promotion.diy_template.not_exists=Le modèle DIY n'existe pas
promotion.diy_template.name_used=Le nom du modèle ({}) est déjà utilisé
promotion.diy_template.used_cannot_delete=Impossible de supprimer un modèle en cours d'utilisation

# Page DIY 1-013-018-000
promotion.diy_page.not_exists=La page DIY n'existe pas
promotion.diy_page.name_used=Le nom de la page ({}) est déjà utilisé

# Conversation de service client 1-013-019-000
promotion.kefu_conversation.not_exists=La conversation du service client n'existe pas

# Message de service client 1-013-020-000
promotion.kefu_message.not_exists=Le message du service client n'existe pas

# Emplacement promotionnel 1-013-021-000
promotion.display.not_exists=L'emplacement promotionnel n'existe pas
