# Promotion Activities 1-013-001-000
promotion.discount_activity.not_exists=The limited-time discount activity does not exist
promotion.discount_activity.spu_conflicts=Some products are already part of another discount activity [{}]
promotion.discount_activity.update_fail_status_closed=The discount activity is closed and cannot be modified
promotion.discount_activity.delete_fail_status_not_closed=The discount activity is not closed and cannot be deleted
promotion.discount_activity.close_fail_status_closed=The discount activity is already closed and cannot be closed again

# Banner 1-013-002-000
promotion.banner.not_exists=The banner does not exist

# Coupon Template 1-013-004-000
promotion.coupon_template.not_exists=The coupon template does not exist
promotion.coupon_template.total_count_too_small=The issuance quantity cannot be less than the claimed quantity ({})
promotion.coupon_template.not_enough=Insufficient remaining quantity to claim
promotion.coupon_template.user_already_take=You have already claimed this coupon
promotion.coupon_template.expired=The coupon has expired
promotion.coupon_template.cannot_take=Invalid method to claim the coupon

# Coupon 1-013-005-000
promotion.coupon.not_exists=The coupon does not exist
promotion.coupon.delete_fail_used=Failed to recycle the coupon, it has already been used
promotion.coupon.status_not_unused=The coupon is not in an unused state
promotion.coupon.valid_time_not_now=The coupon is not valid at this time
promotion.coupon.status_not_used=The coupon is not in a used state

# Full Reduction Activity 1-013-006-000
promotion.reward_activity.not_exists=The full reduction activity does not exist
promotion.reward_activity.spu_conflicts=Some products are already part of another full reduction activity
promotion.reward_activity.update_fail_status_closed=The activity is closed and cannot be modified
promotion.reward_activity.delete_fail_status_not_closed=The activity is not closed and cannot be deleted
promotion.reward_activity.close_fail_status_closed=The activity is already closed and cannot be closed again
promotion.reward_activity.scope_exists=The product scope conflicts with another full reduction activity [{}]: {}

# Points Mall Activity 1-013-007-000
promotion.point_activity.not_exists=The points mall activity does not exist
promotion.point_activity.spu_conflicts=Some products are already part of another points mall activity
promotion.point_activity.update_fail_status_closed=The points activity is closed and cannot be modified
promotion.point_activity.delete_fail_status_not_closed_or_end=The points activity is not closed or has not ended and cannot be deleted
promotion.point_activity.close_fail_status_closed=The points activity is already closed
promotion.point_activity.join_activity_status_closed=Redemption failed: the points activity is closed
promotion.point_activity.join_activity_single_limit_count_exceed=Redemption failed: exceeded single purchase limit
promotion.point_activity.join_activity_product_not_exists=Redemption failed: the product does not exist
promotion.point_activity.update_stock_fail=Redemption failed: insufficient stock for the product

# Flash Sale Activity 1-013-008-000
promotion.seckill_activity.not_exists=The flash sale activity does not exist
promotion.seckill_activity.spu_conflicts=Some products are already part of another flash sale, time conflict
promotion.seckill_activity.update_fail_status_closed=The flash sale is closed and cannot be modified
promotion.seckill_activity.delete_fail_status_not_closed_or_end=The flash sale is not closed or has not ended and cannot be deleted
promotion.seckill_activity.close_fail_status_closed=The flash sale is already closed
promotion.seckill_activity.update_stock_fail=Flash sale failed: insufficient stock
promotion.seckill_activity.time_error=Flash sale failed: not within the activity time range
promotion.seckill_activity.status_closed=Flash sale failed: the activity is closed
promotion.seckill_activity.single_limit_count_exceed=Flash sale failed: exceeded single purchase limit
promotion.seckill_activity.product_not_exists=Flash sale failed: the product does not exist

# Flash Sale Time Slot 1-013-009-000
promotion.seckill_config.not_exists=The flash sale time slot does not exist
promotion.seckill_config.time_conflicts=The flash sale time slot conflicts
promotion.seckill_config.disable=The flash sale time slot is disabled

# Group Buy Activity 1-013-010-000
promotion.combination_activity.not_exists=The group buy activity does not exist
promotion.combination_activity.spu_conflicts=Some products are already part of another group buy activity
promotion.combination_activity.status_disable_not_update=The group buy activity is closed and cannot be modified
promotion.combination_activity.delete_fail_status_not_closed_or_end=The group buy activity is not closed or has not ended
promotion.combination_activity.status_disable=Group buy failed: the activity is closed
promotion.combination_activity.product_not_exists=Group buy failed: the product does not exist
promotion.combination_activity.update_stock_fail=Group buy failed: insufficient product stock

# Group Buy Record 1-013-011-000
promotion.combination_record.not_exists=The group buy record does not exist
promotion.combination_record.exists=Group buy failed: already participated in this group buy
promotion.combination_record.head_not_exists=Group buy failed: the parent group buy does not exist
promotion.combination_record.user_full=Group buy failed: the group is already full
promotion.combination_record.failed_have_joined=Group buy failed: you already have an ongoing group buy record
promotion.combination_record.failed_time_not_start=Group buy failed: the activity has not started
promotion.combination_record.failed_time_end=Group buy failed: the activity has ended
promotion.combination_record.failed_single_limit_count_exceed=Group buy failed: exceeded single purchase limit
promotion.combination_record.failed_total_limit_count_exceed=Group buy failed: exceeded total purchase limit
promotion.combination_record.failed_order_status_unpaid=Group buy failed: unpaid orders exist, please complete payment

# Bargain Activity 1-013-012-000
promotion.bargain_activity.not_exists=The bargain activity does not exist
promotion.bargain_activity.spu_conflicts=Some products are already part of another bargain activity
promotion.bargain_activity.status_disable=The bargain activity is closed and cannot be modified
promotion.bargain_activity.delete_fail_status_not_closed_or_end=The bargain activity is not closed or has not ended
promotion.bargain_activity.stock_not_enough=Insufficient stock for the bargain activity
promotion.bargain_activity.status_closed=The bargain activity is already closed
promotion.bargain_activity.time_end=The bargain activity has ended

# Bargain Record 1-013-013-000
promotion.bargain_record.not_exists=The bargain record does not exist
promotion.bargain_record.create_fail_exists=Participation failed: you have already joined the current bargain activity
promotion.bargain_record.create_fail_limit=Participation failed: you have reached the participation limit for this bargain activity
promotion.bargain_record.join_not_success=Order failed: the bargain was not successful
promotion.bargain_record.already_order=Order failed: the bargain has already been placed

# Bargain Help 1-013-014-000
promotion.bargain_help.not_in_process=Help failed: the bargain record is not in progress
promotion.bargain_help.record_self=Help failed: you cannot help yourself
promotion.bargain_help.create_fail_limit=Help failed: you have reached the participation limit
promotion.bargain_help.create_fail_conflict=Help failed: please try again
promotion.bargain_help.help_exists=Help failed: you have already helped

# Article Category 1-013-015-000
promotion.article_category.not_exists=The article category does not exist
promotion.article_category.delete_fail_have_articles=Failed to delete: associated articles exist

# Article Management 1-013-016-000
promotion.article.not_exists=The article does not exist

# DIY Template 1-013-017-000
promotion.diy_template.not_exists=The DIY template does not exist
promotion.diy_template.name_used=The template name ({}) is already in use
promotion.diy_template.used_cannot_delete=Cannot delete a template that is currently in use

# DIY Page 1-013-018-000
promotion.diy_page.not_exists=The DIY page does not exist
promotion.diy_page.name_used=The page name ({}) is already in use

# Customer Service Conversation 1-013-019-000
promotion.kefu_conversation.not_exists=The customer service conversation does not exist

# Customer Service Message 1-013-020-000
promotion.kefu_message.not_exists=The customer service message does not exist

# Promotion Display Slot 1-013-021-000
promotion.display.not_exists=The promotion display slot does not exist

# Promotion Mail 1-013-022-000
promotion.mail.not_exists=The promotion mail does not exist

# Help Category 1_013_023_000
promotion.help.category.not.exists=Help category does not exist

# Help article 1_013_024_000
promotion.help.article.not.exists=Help article does not exist
