# ========== APP 模块 ==========
pay.app.not_found=التطبيق غير موجود
pay.app.is_disabled=التطبيق معطل
pay.app.exist_order_cant_delete=لا يمكن حذف التطبيق لوجود طلبات دفع مرتبطة
pay.app.exist_refund_cant_delete=لا يمكن حذف التطبيق لوجود طلبات استرداد مرتبطة
pay.app.key_exists=معرف التطبيق موجود بالفعل

# ========== CHANNEL 模块 ==========
pay.channel.not_found=إعدادات قناة الدفع غير موجودة
pay.channel.is_disabled=قناة الدفع معطلة
pay.channel.exist_same_channel_error=توجد قناة مشابهة بالفعل

# ========== ORDER 模块 ==========
pay.order.not_found=طلب الدفع غير موجود
pay.order.status_is_not_waiting=طلب الدفع ليس في حالة الانتظار
pay.order.status_is_success=تم دفع الطلب بالفعل، يرجى تحديث الصفحة
pay.order.is_expired=طلب الدفع منتهي الصلاحية
pay.order.submit_channel_error=خطأ أثناء بدء الدفع، رمز الخطأ: {}، الرسالة: {}
pay.order.refund_fail_status_error=فشل الاسترداد بسبب أن حالة الطلب ليست مدفوعة أو مستردة

# ========== ORDER 模块(拓展单) ==========
pay.order_extension.not_found=طلب الدفع الإضافي غير موجود
pay.order_extension.status_is_not_waiting=طلب الدفع الإضافي ليس في حالة الانتظار
pay.order_extension.is_paid=تم دفع الطلب بالفعل، يرجى انتظار النتيجة

# ========== 支付模块(退款) ==========
pay.refund.price_exceed=مبلغ الاسترداد يتجاوز الحد المسموح به
pay.refund.has_refunding=يوجد استرداد قيد المعالجة بالفعل
pay.refund.exists=طلب الاسترداد موجود بالفعل
pay.refund.not_found=طلب الاسترداد غير موجود
pay.refund.status_is_not_waiting=طلب الاسترداد ليس في حالة الانتظار

# ========== 钱包模块 ==========
pay.wallet.not_found=محفظة المستخدم غير موجودة
pay.wallet.balance_not_enough=الرصيد في المحفظة غير كافٍ
pay.wallet.transaction_not_found=المعاملة المرتبطة بالمحفظة غير موجودة
pay.wallet.refund_exist=يوجد استرداد مرتبط بالمحفظة بالفعل
pay.wallet.freeze_price_not_enough=الرصيد المجمد في المحفظة غير كافٍ

# ========== 钱包充值模块 ==========
pay.wallet_recharge.not_found=سجل شحن المحفظة غير موجود
pay.wallet_recharge.update_paid_status_not_unpaid=فشل تحديث حالة الدفع، السجل ليس في حالة الانتظار
pay.wallet_recharge.update_paid_pay_order_id_error=فشل تحديث حالة الدفع، معرف الطلب غير متطابق
pay.wallet_recharge.update_paid_pay_order_status_not_success=فشل تحديث حالة الدفع، الطلب ليس في حالة النجاح
pay.wallet_recharge.update_paid_pay_price_not_match=فشل تحديث حالة الدفع، مبلغ الطلب غير متطابق
pay.wallet_recharge.refund_fail_not_paid=فشل الاسترداد لأن الطلب لم يتم دفعه
pay.wallet_recharge.refund_fail_refunded=فشل الاسترداد لأن الطلب تم استرداده بالفعل
pay.wallet_recharge.refund_balance_not_enough=فشل الاسترداد بسبب نقص الرصيد
pay.wallet_recharge.refund_fail_refund_order_id_error=فشل تحديث الاسترداد، معرف الطلب غير متطابق
pay.wallet_recharge.refund_fail_refund_not_found=فشل تحديث الاسترداد، طلب الاسترداد غير موجود
pay.wallet_recharge.refund_fail_refund_price_not_match=فشل تحديث الاسترداد، مبلغ الاسترداد غير متطابق
pay.wallet_recharge.package_not_found=حزمة شحن المحفظة غير موجودة
pay.wallet_recharge.package_is_disabled=حزمة شحن المحفظة معطلة
pay.wallet_recharge.package_name_exists=اسم حزمة شحن المحفظة موجود بالفعل

# ========== 转账模块 ==========
pay.transfer.submit_channel_error=خطأ أثناء بدء التحويل، رمز الخطأ: {}، الرسالة: {}
pay.transfer.not_found=طلب التحويل غير موجود
pay.transfer.same_merchant_transfer_type_not_match=أنواع التحويلات غير متطابقة
pay.transfer.same_merchant_transfer_price_not_match=مبالغ التحويلات غير متطابقة
pay.transfer.merchant_transfer_exists=تم بدء التحويل لهذا الطلب بالفعل، يرجى مراجعة حالة الطلب
pay.transfer.status_is_not_waiting=طلب التحويل ليس في حالة الانتظار
pay.transfer.status_is_not_pending=طلب التحويل ليس في حالة الانتظار أو قيد التنفيذ

# ========== 示例订单 ==========
pay.demo_order.not_found=طلب النموذج غير موجود
pay.demo_order.update_paid_status_not_unpaid=فشل تحديث حالة الدفع، الطلب ليس في حالة الانتظار
pay.demo_order.update_paid_fail_pay_order_id_error=فشل تحديث حالة الدفع، معرف الطلب غير متطابق
pay.demo_order.update_paid_fail_pay_order_status_not_success=فشل تحديث حالة الدفع، الطلب ليس في حالة النجاح
pay.demo_order.update_paid_fail_pay_price_not_match=فشل تحديث حالة الدفع، مبلغ الطلب غير متطابق
pay.demo_order.refund_fail_not_paid=فشل الاسترداد لأن الطلب لم يتم دفعه
pay.demo_order.refund_fail_refunded=فشل الاسترداد لأن الطلب تم استرداده بالفعل
pay.demo_order.refund_fail_refund_not_found=فشل الاسترداد لأن طلب الاسترداد غير موجود
pay.demo_order.refund_fail_refund_not_success=فشل الاسترداد لأن طلب الاسترداد لم ينجح
pay.demo_order.refund_fail_refund_order_id_error=فشل الاسترداد لأن معرف الطلب غير متطابق
pay.demo_order.refund_fail_refund_price_not_match=فشل الاسترداد لأن مبلغ الاسترداد غير متطابق

# ========== 示例转账订单 ==========
pay.demo_transfer.not_found=طلب التحويل النموذجي غير موجود
pay.demo_transfer.fail_transfer_id_error=فشل التحويل، معرف الطلب غير متطابق
pay.demo_transfer.fail_price_not_match=فشل التحويل، مبلغ الطلب غير متطابق
