# ========== APP 模块 ==========
pay.app.not_found=Die Anwendung existiert nicht
pay.app.is_disabled=Die Anwendung wurde deaktiviert
pay.app.exist_order_cant_delete=Die Anwendung kann nicht gelöscht werden, da Bestellungen vorhanden sind
pay.app.exist_refund_cant_delete=Die Anwendung kann nicht gelöscht werden, da Rückerstattungen vorhanden sind
pay.app.key_exists=Die Anwendungskennung existiert bereits

# ========== CHANNEL 模块 ==========
pay.channel.not_found=Die Konfiguration des Zahlungskanals existiert nicht
pay.channel.is_disabled=Der Zahlungskanal wurde deaktiviert
pay.channel.exist_same_channel_error=Ein identischer Zahlungskanal existiert bereits

# ========== ORDER 模块 ==========
pay.order.not_found=Die Zahlungsbestellung existiert nicht
pay.order.status_is_not_waiting=Die Zahlungsbestellung ist nicht im Wartestatus
pay.order.status_is_success=Die Bestellung wurde bereits bezahlt, bitte aktualisieren Sie die Seite
pay.order.is_expired=Die Zahlungsbestellung ist abgelaufen
pay.order.submit_channel_error=Fehler bei der Zahlungsanfrage, Fehlercode: {}, Fehlermeldung: {}
pay.order.refund_fail_status_error=Rückerstattung fehlgeschlagen, die Bestellung ist weder bezahlt noch zurückerstattet

# ========== ORDER 模块(拓展单) ==========
pay.order_extension.not_found=Die Zahlungserweiterung existiert nicht
pay.order_extension.status_is_not_waiting=Die Zahlungserweiterung ist nicht im Wartestatus
pay.order_extension.is_paid=Die Bestellung wurde bereits bezahlt, bitte warten Sie auf das Zahlungsergebnis

# ========== 支付模块(退款) ==========
pay.refund.price_exceed=Der Erstattungsbetrag übersteigt den erstattungsfähigen Betrag
pay.refund.has_refunding=Es gibt bereits eine laufende Rückerstattung
pay.refund.exists=Es existiert bereits eine Rückerstattungsanfrage
pay.refund.not_found=Die Rückerstattungsbestellung existiert nicht
pay.refund.status_is_not_waiting=Die Rückerstattungsbestellung ist nicht im Wartestatus

# ========== 钱包模块 ==========
pay.wallet.not_found=Die Geldbörse des Benutzers existiert nicht
pay.wallet.balance_not_enough=Das Guthaben in der Geldbörse ist unzureichend
pay.wallet.transaction_not_found=Die Geldbörsentransaktion wurde nicht gefunden
pay.wallet.refund_exist=Eine Rückerstattung der Geldbörse existiert bereits
pay.wallet.freeze_price_not_enough=Das eingefrorene Guthaben in der Geldbörse ist unzureichend

# ========== 钱包充值模块 ==========
pay.wallet_recharge.not_found=Die Geldbörsenaufladung existiert nicht
pay.wallet_recharge.update_paid_status_not_unpaid=Fehler beim Aktualisieren des Zahlungsstatus, die Geldbörsenaufladung ist nicht unbezahlter Status
pay.wallet_recharge.update_paid_pay_order_id_error=Fehler beim Aktualisieren des Zahlungsstatus, die Bestellnummer stimmt nicht überein
pay.wallet_recharge.update_paid_pay_order_status_not_success=Fehler beim Aktualisieren des Zahlungsstatus, die Bestellung ist nicht erfolgreich
pay.wallet_recharge.update_paid_pay_price_not_match=Fehler beim Aktualisieren des Zahlungsstatus, der Betrag stimmt nicht überein
pay.wallet_recharge.refund_fail_not_paid=Rückerstattung fehlgeschlagen, die Bestellung wurde nicht bezahlt
pay.wallet_recharge.refund_fail_refunded=Rückerstattung fehlgeschlagen, die Bestellung wurde bereits zurückerstattet
pay.wallet_recharge.refund_balance_not_enough=Rückerstattung fehlgeschlagen, Guthaben unzureichend
pay.wallet_recharge.refund_fail_refund_order_id_error=Fehler beim Aktualisieren der Rückerstattung, die Bestellnummer stimmt nicht überein
pay.wallet_recharge.refund_fail_refund_not_found=Fehler beim Aktualisieren der Rückerstattung, die Bestellung existiert nicht
pay.wallet_recharge.refund_fail_refund_price_not_match=Fehler beim Aktualisieren der Rückerstattung, der Betrag stimmt nicht überein
pay.wallet_recharge.package_not_found=Das Aufladepaket existiert nicht
pay.wallet_recharge.package_is_disabled=Das Aufladepaket wurde deaktiviert
pay.wallet_recharge.package_name_exists=Der Name des Aufladepakets existiert bereits

# ========== 转账模块 ==========
pay.transfer.submit_channel_error=Fehler beim Initiieren der Überweisung, Fehlercode: {}, Fehlermeldung: {}
pay.transfer.not_found=Die Überweisungsbestellung existiert nicht
pay.transfer.same_merchant_transfer_type_not_match=Die Überweisungstypen stimmen nicht überein
pay.transfer.same_merchant_transfer_price_not_match=Die Überweisungsbeträge stimmen nicht überein
pay.transfer.merchant_transfer_exists=Eine Überweisung für diesen Vorgang wurde bereits initiiert, bitte prüfen Sie den Bestellstatus
pay.transfer.status_is_not_waiting=Die Überweisungsbestellung ist nicht im Wartestatus
pay.transfer.status_is_not_pending=Die Überweisungsbestellung ist weder wartend noch in Bearbeitung

# ========== 示例订单 ==========
pay.demo_order.not_found=Die Demo-Bestellung existiert nicht
pay.demo_order.update_paid_status_not_unpaid=Fehler beim Aktualisieren des Zahlungsstatus, die Bestellung ist nicht unbezahlter Status
pay.demo_order.update_paid_fail_pay_order_id_error=Fehler beim Aktualisieren des Zahlungsstatus, die Bestellnummer stimmt nicht überein
pay.demo_order.update_paid_fail_pay_order_status_not_success=Fehler beim Aktualisieren des Zahlungsstatus, die Bestellung ist nicht erfolgreich
pay.demo_order.update_paid_fail_pay_price_not_match=Fehler beim Aktualisieren des Zahlungsstatus, der Betrag stimmt nicht überein
pay.demo_order.refund_fail_not_paid=Rückerstattung fehlgeschlagen, die Bestellung wurde nicht bezahlt
pay.demo_order.refund_fail_refunded=Rückerstattung fehlgeschlagen, die Bestellung wurde bereits zurückerstattet
pay.demo_order.refund_fail_refund_not_found=Rückerstattung fehlgeschlagen, die Rückerstattungsbestellung existiert nicht
pay.demo_order.refund_fail_refund_not_success=Rückerstattung fehlgeschlagen, die Rückerstattungsbestellung war nicht erfolgreich
pay.demo_order.refund_fail_refund_order_id_error=Rückerstattung fehlgeschlagen, die Bestellnummer stimmt nicht überein
pay.demo_order.refund_fail_refund_price_not_match=Rückerstattung fehlgeschlagen, der Betrag stimmt nicht überein

# ========== 示例转账订单 ==========
pay.demo_transfer.not_found=Die Demo-Überweisung existiert nicht
pay.demo_transfer.fail_transfer_id_error=Überweisungsfehler, die Bestellnummer stimmt nicht überein
pay.demo_transfer.fail_price_not_match=Überweisungsfehler, der Betrag stimmt nicht überein
