# ========== APP 模块 ==========
pay.app.not_found=L'application n'existe pas
pay.app.is_disabled=L'application a été désactivée
pay.app.exist_order_cant_delete=Impossible de supprimer l'application avec des commandes existantes
pay.app.exist_refund_cant_delete=Impossible de supprimer l'application avec des remboursements existants
pay.app.key_exists=L'identifiant de l'application existe déjà

# ========== CHANNEL 模块 ==========
pay.channel.not_found=La configuration du canal de paiement n'existe pas
pay.channel.is_disabled=Le canal de paiement a été désactivé
pay.channel.exist_same_channel_error=Le canal de paiement existe déjà

# ========== ORDER 模块 ==========
pay.order.not_found=La commande de paiement n'existe pas
pay.order.status_is_not_waiting=La commande de paiement n'est pas en attente
pay.order.status_is_success=La commande a déjà été payée, veuillez actualiser la page
pay.order.is_expired=La commande de paiement a expiré
pay.order.submit_channel_error=Erreur lors de l'initiation du paiement, code : {}, message : {}
pay.order.refund_fail_status_error=Échec du remboursement, la commande n'est ni payée ni remboursée

# ========== ORDER 模块(拓展单) ==========
pay.order_extension.not_found=L'extension de transaction de paiement n'existe pas
pay.order_extension.status_is_not_waiting=L'extension de transaction de paiement n'est pas en attente
pay.order_extension.is_paid=La commande a déjà été payée, veuillez attendre le résultat du paiement

# ========== 支付模块(退款) ==========
pay.refund.price_exceed=Le montant du remboursement dépasse le montant remboursable
pay.refund.has_refunding=Un remboursement est déjà en cours
pay.refund.exists=Une commande de remboursement existe déjà
pay.refund.not_found=La commande de remboursement n'existe pas
pay.refund.status_is_not_waiting=La commande de remboursement n'est pas en attente

# ========== 钱包模块 ==========
pay.wallet.not_found=Le portefeuille utilisateur n'existe pas
pay.wallet.balance_not_enough=Solde insuffisant dans le portefeuille
pay.wallet.transaction_not_found=La transaction du portefeuille n'a pas été trouvée
pay.wallet.refund_exist=Un remboursement de portefeuille existe déjà
pay.wallet.freeze_price_not_enough=Solde gelé insuffisant dans le portefeuille

# ========== 钱包充值模块 ==========
pay.wallet_recharge.not_found=Le rechargement du portefeuille n'existe pas
pay.wallet_recharge.update_paid_status_not_unpaid=Échec de la mise à jour du statut de paiement, le rechargement n'est pas non payé
pay.wallet_recharge.update_paid_pay_order_id_error=Échec de la mise à jour du statut de paiement, l'ID de commande ne correspond pas
pay.wallet_recharge.update_paid_pay_order_status_not_success=Échec de la mise à jour du statut de paiement, la commande n'est pas réussie
pay.wallet_recharge.update_paid_pay_price_not_match=Échec de la mise à jour du statut de paiement, le montant ne correspond pas
pay.wallet_recharge.refund_fail_not_paid=Échec du remboursement, la commande n'a pas été payée
pay.wallet_recharge.refund_fail_refunded=Échec du remboursement, la commande a déjà été remboursée
pay.wallet_recharge.refund_balance_not_enough=Échec du remboursement, solde insuffisant
pay.wallet_recharge.refund_fail_refund_order_id_error=Échec de mise à jour du remboursement, l'ID de la commande ne correspond pas
pay.wallet_recharge.refund_fail_refund_not_found=Échec de mise à jour du remboursement, la commande n'existe pas
pay.wallet_recharge.refund_fail_refund_price_not_match=Échec de mise à jour du remboursement, le montant ne correspond pas
pay.wallet_recharge.package_not_found=Le forfait de rechargement n'existe pas
pay.wallet_recharge.package_is_disabled=Le forfait de rechargement a été désactivé
pay.wallet_recharge.package_name_exists=Le nom du forfait de rechargement existe déjà

# ========== 转账模块 ==========
pay.transfer.submit_channel_error=Erreur lors de l'initiation du transfert, code : {}, message : {}
pay.transfer.not_found=La commande de transfert n'existe pas
pay.transfer.same_merchant_transfer_type_not_match=Les types de demandes de transfert ne correspondent pas
pay.transfer.same_merchant_transfer_price_not_match=Les montants des demandes de transfert ne correspondent pas
pay.transfer.merchant_transfer_exists=Un transfert a déjà été initié pour cette affaire, vérifiez l'état de la commande
pay.transfer.status_is_not_waiting=La commande de transfert n'est pas en attente
pay.transfer.status_is_not_pending=La commande de transfert n'est ni en attente ni en cours

# ========== 示例订单 ==========
pay.demo_order.not_found=La commande de démonstration n'existe pas
pay.demo_order.update_paid_status_not_unpaid=Échec de mise à jour du statut de paiement, la commande n'est pas non payée
pay.demo_order.update_paid_fail_pay_order_id_error=Échec de mise à jour du statut de paiement, l'ID de la commande ne correspond pas
pay.demo_order.update_paid_fail_pay_order_status_not_success=Échec de mise à jour du statut de paiement, la commande n'est pas réussie
pay.demo_order.update_paid_fail_pay_price_not_match=Échec de mise à jour du statut de paiement, le montant ne correspond pas
pay.demo_order.refund_fail_not_paid=Échec du remboursement, la commande n'a pas été payée
pay.demo_order.refund_fail_refunded=Échec du remboursement, la commande a déjà été remboursée
pay.demo_order.refund_fail_refund_not_found=Échec du remboursement, la commande de remboursement n'existe pas
pay.demo_order.refund_fail_refund_not_success=Échec du remboursement, la commande de remboursement n'a pas réussi
pay.demo_order.refund_fail_refund_order_id_error=Échec du remboursement, l'ID de la commande ne correspond pas
pay.demo_order.refund_fail_refund_price_not_match=Échec du remboursement, le montant ne correspond pas

# ========== 示例转账订单 ==========
pay.demo_transfer.not_found=La commande de transfert de démonstration n'existe pas
pay.demo_transfer.fail_transfer_id_error=Échec du transfert, l'ID de la commande ne correspond pas
pay.demo_transfer.fail_price_not_match=Échec du transfert, le montant ne correspond pas
