# ========== APP 模块 ==========
pay.app.not_found=Application does not exist
pay.app.is_disabled=Application has been disabled
pay.app.exist_order_cant_delete=Cannot delete payment application with existing orders
pay.app.exist_refund_cant_delete=Cannot delete payment application with existing refunds
pay.app.key_exists=Payment application identifier already exists

# ========== CHANNEL 模块 ==========
pay.channel.not_found=Payment channel configuration does not exist
pay.channel.is_disabled=Payment channel has been disabled
pay.channel.exist_same_channel_error=Payment channel already exists

# ========== ORDER 模块 ==========
pay.order.not_found=Payment order does not exist
pay.order.status_is_not_waiting=Payment order is not pending
pay.order.status_is_success=Order has already been paid, please refresh the page
pay.order.is_expired=Payment order has expired
pay.order.submit_channel_error=Payment initiation error, code: {}, message: {}
pay.order.refund_fail_status_error=Payment order refund failed, reason: status is neither paid nor refunded

# ========== ORDER 模块(拓展单) ==========
pay.order_extension.not_found=Payment transaction extension does not exist
pay.order_extension.status_is_not_waiting=Payment transaction extension is not pending
pay.order_extension.is_paid=Order has already been paid, please wait for the payment result

# ========== 支付模块(退款) ==========
pay.refund.price_exceed=Refund amount exceeds the refundable amount
pay.refund.has_refunding=Refund is already in process
pay.refund.exists=Refund order already exists
pay.refund.not_found=Refund order does not exist
pay.refund.status_is_not_waiting=Refund order is not pending

# ========== 钱包模块 ==========
pay.wallet.not_found=User wallet does not exist
pay.wallet.balance_not_enough=Insufficient wallet balance
pay.wallet.transaction_not_found=Wallet transaction not found
pay.wallet.refund_exist=Wallet refund already exists
pay.wallet.freeze_price_not_enough=Insufficient wallet freeze balance
pay.wallet.pay_password_required=Payment password is required for wallet payment
pay.wallet.pay_password_error=Payment password error

# ========== 钱包充值模块 ==========
pay.wallet_recharge.not_found=Wallet recharge record does not exist
pay.wallet_recharge.update_paid_status_not_unpaid=Failed to update wallet recharge payment status, record is not unpaid
pay.wallet_recharge.update_paid_pay_order_id_error=Failed to update wallet recharge payment status, payment order ID mismatch
pay.wallet_recharge.update_paid_pay_order_status_not_success=Failed to update wallet recharge payment status, payment order is not successful
pay.wallet_recharge.update_paid_pay_price_not_match=Failed to update wallet recharge payment status, payment amount mismatch
pay.wallet_recharge.refund_fail_not_paid=Wallet refund failed, recharge order not paid
pay.wallet_recharge.refund_fail_refunded=Wallet refund failed, recharge order already refunded
pay.wallet_recharge.refund_balance_not_enough=Wallet refund failed, insufficient balance
pay.wallet_recharge.refund_fail_refund_order_id_error=Wallet refund update failed, refund order ID mismatch
pay.wallet_recharge.refund_fail_refund_not_found=Wallet refund update failed, refund order does not exist
pay.wallet_recharge.refund_fail_refund_price_not_match=Wallet refund update failed, refund amount mismatch
pay.wallet_recharge.package_not_found=Wallet recharge package does not exist
pay.wallet_recharge.package_is_disabled=Wallet recharge package is disabled
pay.wallet_recharge.package_name_exists=Wallet recharge package name already exists

# ========== 转账模块 ==========
pay.transfer.submit_channel_error=Transfer initiation error, code: {}, message: {}
pay.transfer.not_found=Transfer order does not exist
pay.transfer.same_merchant_transfer_type_not_match=Types of two identical transfer requests do not match
pay.transfer.same_merchant_transfer_price_not_match=Amounts of two identical transfer requests do not match
pay.transfer.merchant_transfer_exists=Transfer has already been initiated for this business, please check transfer order status
pay.transfer.status_is_not_waiting=Transfer order is not pending
pay.transfer.status_is_not_pending=Transfer order is not pending or in process

# ========== 汇率模块 ==========
pay.exchange_rate.not_found=Exchange rate does not exist

# ========== 示例订单 ==========
pay.demo_order.not_found=Demo order does not exist
pay.demo_order.update_paid_status_not_unpaid=Failed to update demo order payment status, order is not unpaid
pay.demo_order.update_paid_fail_pay_order_id_error=Failed to update demo order payment status, payment order ID mismatch
pay.demo_order.update_paid_fail_pay_order_status_not_success=Failed to update demo order payment status, payment order is not successful
pay.demo_order.update_paid_fail_pay_price_not_match=Failed to update demo order payment status, payment amount mismatch
pay.demo_order.refund_fail_not_paid=Refund failed, demo order not paid
pay.demo_order.refund_fail_refunded=Refund failed, demo order already refunded
pay.demo_order.refund_fail_refund_not_found=Refund failed, refund order does not exist
pay.demo_order.refund_fail_refund_not_success=Refund failed, refund order not successful
pay.demo_order.refund_fail_refund_order_id_error=Refund failed, refund order ID mismatch
pay.demo_order.refund_fail_refund_price_not_match=Refund failed, refund amount mismatch

# ========== 示例转账订单 ==========
pay.demo_transfer.not_found=Demo transfer order does not exist
pay.demo_transfer.fail_transfer_id_error=Transfer failed, transfer order ID mismatch
pay.demo_transfer.fail_price_not_match=Transfer failed, transfer amount mismatch
