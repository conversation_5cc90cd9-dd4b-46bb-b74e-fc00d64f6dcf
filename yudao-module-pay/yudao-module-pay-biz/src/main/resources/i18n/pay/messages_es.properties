# ========== APP 模块 ==========
pay.app.not_found=La aplicación no existe
pay.app.is_disabled=La aplicación está deshabilitada
pay.app.exist_order_cant_delete=No se puede eliminar la aplicación porque hay pedidos asociados
pay.app.exist_refund_cant_delete=No se puede eliminar la aplicación porque hay reembolsos asociados
pay.app.key_exists=El identificador de la aplicación ya existe

# ========== CHANNEL 模块 ==========
pay.channel.not_found=La configuración del canal de pago no existe
pay.channel.is_disabled=El canal de pago está deshabilitado
pay.channel.exist_same_channel_error=Ya existe un canal idéntico

# ========== ORDER 模块 ==========
pay.order.not_found=El pedido de pago no existe
pay.order.status_is_not_waiting=El pedido de pago no está en estado pendiente
pay.order.status_is_success=El pedido ya ha sido pagado, por favor actualice la página
pay.order.is_expired=El pedido de pago ha expirado
pay.order.submit_channel_error=Error al iniciar el pago, código de error: {}, mensaje de error: {}
pay.order.refund_fail_status_error=El reembolso falló porque el estado del pedido no es pagado o reembolsado

# ========== ORDER 模块(拓展单) ==========
pay.order_extension.not_found=La extensión de la transacción de pago no existe
pay.order_extension.status_is_not_waiting=La extensión de la transacción de pago no está en estado pendiente
pay.order_extension.is_paid=El pedido ya ha sido pagado, espere el resultado del pago

# ========== 支付模块(退款) ==========
pay.refund.price_exceed=El monto del reembolso excede el monto reembolsable del pedido
pay.refund.has_refunding=Ya hay un reembolso en proceso
pay.refund.exists=Ya existe una solicitud de reembolso
pay.refund.not_found=El pedido de reembolso no existe
pay.refund.status_is_not_waiting=El pedido de reembolso no está en estado pendiente

# ========== 钱包模块 ==========
pay.wallet.not_found=La billetera del usuario no existe
pay.wallet.balance_not_enough=Saldo insuficiente en la billetera
pay.wallet.transaction_not_found=La transacción de la billetera no fue encontrada
pay.wallet.refund_exist=Ya existe un reembolso en la billetera
pay.wallet.freeze_price_not_enough=El saldo congelado de la billetera es insuficiente

# ========== 钱包充值模块 ==========
pay.wallet_recharge.not_found=El registro de recarga de billetera no existe
pay.wallet_recharge.update_paid_status_not_unpaid=Error al actualizar el estado del pago, el registro de recarga no está en estado pendiente
pay.wallet_recharge.update_paid_pay_order_id_error=Error al actualizar el estado del pago, el ID del pedido no coincide
pay.wallet_recharge.update_paid_pay_order_status_not_success=Error al actualizar el estado del pago, el estado del pedido no es exitoso
pay.wallet_recharge.update_paid_pay_price_not_match=Error al actualizar el estado del pago, el monto del pedido no coincide
pay.wallet_recharge.refund_fail_not_paid=El reembolso falló porque el pedido no ha sido pagado
pay.wallet_recharge.refund_fail_refunded=El reembolso falló porque el pedido ya ha sido reembolsado
pay.wallet_recharge.refund_balance_not_enough=El reembolso falló por saldo insuficiente
pay.wallet_recharge.refund_fail_refund_order_id_error=Error al actualizar el reembolso, el ID del pedido no coincide
pay.wallet_recharge.refund_fail_refund_not_found=Error al actualizar el reembolso, el pedido no existe
pay.wallet_recharge.refund_fail_refund_price_not_match=Error al actualizar el reembolso, el monto no coincide
pay.wallet_recharge.package_not_found=El paquete de recarga no existe
pay.wallet_recharge.package_is_disabled=El paquete de recarga está deshabilitado
pay.wallet_recharge.package_name_exists=El nombre del paquete de recarga ya existe

# ========== 转账模块 ==========
pay.transfer.submit_channel_error=Error al iniciar la transferencia, código de error: {}, mensaje de error: {}
pay.transfer.not_found=El pedido de transferencia no existe
pay.transfer.same_merchant_transfer_type_not_match=Los tipos de transferencia no coinciden
pay.transfer.same_merchant_transfer_price_not_match=Los montos de transferencia no coinciden
pay.transfer.merchant_transfer_exists=La transferencia para esta operación ya se ha iniciado, por favor revise el estado del pedido
pay.transfer.status_is_not_waiting=El pedido de transferencia no está en estado pendiente
pay.transfer.status_is_not_pending=El pedido de transferencia no está pendiente o en proceso

# ========== 示例订单 ==========
pay.demo_order.not_found=El pedido de ejemplo no existe
pay.demo_order.update_paid_status_not_unpaid=Error al actualizar el estado del pago, el pedido no está en estado pendiente
pay.demo_order.update_paid_fail_pay_order_id_error=Error al actualizar el estado del pago, el ID del pedido no coincide
pay.demo_order.update_paid_fail_pay_order_status_not_success=Error al actualizar el estado del pago, el estado del pedido no es exitoso
pay.demo_order.update_paid_fail_pay_price_not_match=Error al actualizar el estado del pago, el monto del pedido no coincide
pay.demo_order.refund_fail_not_paid=El reembolso falló porque el pedido no ha sido pagado
pay.demo_order.refund_fail_refunded=El reembolso falló porque el pedido ya ha sido reembolsado
pay.demo_order.refund_fail_refund_not_found=El reembolso falló porque el pedido de reembolso no existe
pay.demo_order.refund_fail_refund_not_success=El reembolso falló porque el pedido de reembolso no fue exitoso
pay.demo_order.refund_fail_refund_order_id_error=El reembolso falló porque el ID del pedido no coincide
pay.demo_order.refund_fail_refund_price_not_match=El reembolso falló porque el monto no coincide

# ========== 示例转账订单 ==========
pay.demo_transfer.not_found=El pedido de transferencia de ejemplo no existe
pay.demo_transfer.fail_transfer_id_error=Error en la transferencia, el ID del pedido no coincide
pay.demo_transfer.fail_price_not_match=Error en la transferencia, el monto no coincide
