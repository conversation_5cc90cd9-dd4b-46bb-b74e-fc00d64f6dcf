# ========== APP 模块 ==========
pay.app.not_found=应用不存在
pay.app.is_disabled=应用已被禁用
pay.app.exist_order_cant_delete=支付应用存在支付订单，无法删除
pay.app.exist_refund_cant_delete=支付应用存在退款订单，无法删除
pay.app.key_exists=支付应用标识已存在

# ========== CHANNEL 模块 ==========
pay.channel.not_found=支付渠道的配置不存在
pay.channel.is_disabled=支付渠道已被禁用
pay.channel.exist_same_channel_error=已存在相同的支付渠道

# ========== ORDER 模块 ==========
pay.order.not_found=支付订单不存在
pay.order.status_is_not_waiting=支付订单不处于待支付状态
pay.order.status_is_success=订单已支付，请刷新页面
pay.order.is_expired=支付订单已过期
pay.order.submit_channel_error=发起支付报错，错误码：{}，错误提示：{}
pay.order.refund_fail_status_error=支付订单退款失败，原因：状态不是已支付或已退款

# ========== ORDER 拓展单 ==========
pay.order_extension.not_found=支付交易拓展单不存在
pay.order_extension.status_is_not_waiting=支付交易拓展单不处于待支付状态
pay.order_extension.is_paid=订单已支付，请等待支付结果

# ========== 退款模块 ==========
pay.refund.price_exceed=退款金额超过订单可退款金额
pay.refund.has_refunding=已存在正在处理的退款
pay.refund.exists=已存在退款单
pay.refund.not_found=支付退款单不存在
pay.refund.status_is_not_waiting=支付退款单不处于待退款状态

# ========== 钱包模块 ==========
pay.wallet.not_found=用户钱包不存在
pay.wallet.balance_not_enough=钱包余额不足
pay.wallet.transaction_not_found=未找到对应的钱包交易
pay.wallet.refund_exist=已存在钱包退款
pay.wallet.freeze_price_not_enough=钱包冻结金额不足
pay.wallet.pay_password_required=使用余额支付需要验证支付密码
pay.wallet.pay_password_error=支付密码错误

# ========== 钱包充值模块 ==========
pay.wallet_recharge.not_found=钱包充值记录不存在
pay.wallet_recharge.update_paid_status_not_unpaid=钱包充值更新支付状态失败，充值记录不是未支付状态
pay.wallet_recharge.update_paid_pay_order_id_error=钱包充值更新支付状态失败，支付单编号不匹配
pay.wallet_recharge.update_paid_pay_order_status_not_success=钱包充值更新支付状态失败，支付单状态不是支付成功
pay.wallet_recharge.update_paid_pay_price_not_match=钱包充值更新支付状态失败，支付金额不匹配
pay.wallet_recharge.refund_fail_not_paid=钱包发起退款失败，充值订单未支付
pay.wallet_recharge.refund_fail_refunded=钱包发起退款失败，充值订单已退款
pay.wallet_recharge.refund_balance_not_enough=钱包发起退款失败，钱包余额不足
pay.wallet_recharge.refund_fail_refund_order_id_error=钱包退款更新失败，退款单编号不匹配
pay.wallet_recharge.refund_fail_refund_not_found=钱包退款更新失败，退款订单不存在
pay.wallet_recharge.refund_fail_refund_price_not_match=钱包退款更新失败，退款金额不匹配
pay.wallet_recharge.package_not_found=钱包充值套餐不存在
pay.wallet_recharge.package_is_disabled=钱包充值套餐已被禁用
pay.wallet_recharge.package_name_exists=钱包充值套餐名称已存在

# ========== 转账模块 ==========
pay.transfer.submit_channel_error=发起转账报错，错误码：{}，错误提示：{}
pay.transfer.not_found=转账单不存在
pay.transfer.same_merchant_transfer_type_not_match=两次相同转账请求的类型不匹配
pay.transfer.same_merchant_transfer_price_not_match=两次相同转账请求的金额不匹配
pay.transfer.merchant_transfer_exists=该笔业务的转账已发起，请查询相关状态
pay.transfer.status_is_not_waiting=转账单不处于待转账状态
pay.transfer.status_is_not_pending=转账单不处于待转账或转账中状态

# ========== 汇率模块 ==========
pay.exchange_rate.not_found=汇率不存在

# ========== 示例订单模块 ==========
pay.demo_order.not_found=示例订单不存在
pay.demo_order.update_paid_status_not_unpaid=示例订单更新支付状态失败，订单不是未支付状态
pay.demo_order.update_paid_fail_pay_order_id_error=示例订单更新支付状态失败，支付单编号不匹配
pay.demo_order.update_paid_fail_pay_order_status_not_success=示例订单更新支付状态失败，支付单状态不是支付成功
pay.demo_order.update_paid_fail_pay_price_not_match=示例订单更新支付状态失败，支付金额不匹配
pay.demo_order.refund_fail_not_paid=发起退款失败，示例订单未支付
pay.demo_order.refund_fail_refunded=发起退款失败，示例订单已退款
pay.demo_order.refund_fail_refund_not_found=发起退款失败，退款订单不存在
pay.demo_order.refund_fail_refund_not_success=发起退款失败，退款订单未成功退款
pay.demo_order.refund_fail_refund_order_id_error=发起退款失败，退款单编号不匹配
pay.demo_order.refund_fail_refund_price_not_match=发起退款失败，退款金额不匹配

# ========== 示例转账订单模块 ==========
pay.demo_transfer.not_found=示例转账单不存在
pay.demo_transfer.fail_transfer_id_error=转账失败，转账单编号不匹配
pay.demo_transfer.fail_price_not_match=转账失败，转账金额不匹配
