package cn.iocoder.yudao.module.pay.controller.admin.exchangerate.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 汇率新增/修改 Request VO")
@Data
public class ExchangeRateSaveReqVO {

    @Schema(description = "日志主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31897")
    private Long id;

    @Schema(description = "目标货币", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "目标货币不能为空")
    private String currency;

    @Schema(description = "货币符号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("货币符号")
    private String symbol;

    @Schema(description = "货币名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "货币名称不能为空")
    private String currencyName;

    @Schema(description = "汇率值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "汇率值不能为空")
    private BigDecimal rate;

    @Schema(description = "汇率来源")
    private String rateSource;

    @Schema(description = "是否默认", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "是否默认不能为空")
    private Boolean defaultStatus;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "开启状态不能为空")
    private Integer status;

    @Schema(description = "备注", example = "你猜")
    private String memo;

}