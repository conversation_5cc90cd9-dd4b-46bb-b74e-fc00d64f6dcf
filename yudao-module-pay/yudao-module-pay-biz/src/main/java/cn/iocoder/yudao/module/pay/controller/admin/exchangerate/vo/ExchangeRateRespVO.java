package cn.iocoder.yudao.module.pay.controller.admin.exchangerate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 汇率 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ExchangeRateRespVO {

    @Schema(description = "日志主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31897")
    @ExcelProperty("日志主键")
    private Long id;

    @Schema(description = "目标货币", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("目标货币")
    private String currency;

    @Schema(description = "货币符号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("货币符号")
    private String symbol;

    @Schema(description = "货币名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("货币名称")
    private String currencyName;

    @Schema(description = "汇率值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("汇率值")
    private BigDecimal rate;

    @Schema(description = "汇率来源")
    @ExcelProperty("汇率来源")
    private String rateSource;

    @Schema(description = "是否默认", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("是否默认")
    private Boolean defaultStatus;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("开启状态")
    private Integer status;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String memo;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}