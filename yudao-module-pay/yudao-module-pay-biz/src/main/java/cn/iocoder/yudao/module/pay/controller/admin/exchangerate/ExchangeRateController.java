package cn.iocoder.yudao.module.pay.controller.admin.exchangerate;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pay.controller.admin.exchangerate.vo.*;
import cn.iocoder.yudao.module.pay.dal.dataobject.exchangerate.ExchangeRateDO;
import cn.iocoder.yudao.module.pay.service.exchangerate.ExchangeRateService;

@Tag(name = "管理后台 - 汇率")
@RestController
@RequestMapping("/pay/exchange-rate")
@Validated
public class ExchangeRateController {

    @Resource
    private ExchangeRateService exchangeRateService;

    @PostMapping("/create")
    @Operation(summary = "创建汇率")
    @PreAuthorize("@ss.hasPermission('pay:exchange-rate:create')")
    public CommonResult<Long> createExchangeRate(@Valid @RequestBody ExchangeRateSaveReqVO createReqVO) {
        return success(exchangeRateService.createExchangeRate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新汇率")
    @PreAuthorize("@ss.hasPermission('pay:exchange-rate:update')")
    public CommonResult<Boolean> updateExchangeRate(@Valid @RequestBody ExchangeRateSaveReqVO updateReqVO) {
        exchangeRateService.updateExchangeRate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除汇率")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pay:exchange-rate:delete')")
    public CommonResult<Boolean> deleteExchangeRate(@RequestParam("id") Long id) {
        exchangeRateService.deleteExchangeRate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得汇率")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pay:exchange-rate:query')")
    public CommonResult<ExchangeRateRespVO> getExchangeRate(@RequestParam("id") Long id) {
        ExchangeRateDO exchangeRate = exchangeRateService.getExchangeRate(id);
        return success(BeanUtils.toBean(exchangeRate, ExchangeRateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得汇率分页")
    @PreAuthorize("@ss.hasPermission('pay:exchange-rate:query')")
    public CommonResult<PageResult<ExchangeRateRespVO>> getExchangeRatePage(@Valid ExchangeRatePageReqVO pageReqVO) {
        PageResult<ExchangeRateDO> pageResult = exchangeRateService.getExchangeRatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ExchangeRateRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出汇率 Excel")
    @PreAuthorize("@ss.hasPermission('pay:exchange-rate:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportExchangeRateExcel(@Valid ExchangeRatePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ExchangeRateDO> list = exchangeRateService.getExchangeRatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "汇率.xls", "数据", ExchangeRateRespVO.class,
                        BeanUtils.toBean(list, ExchangeRateRespVO.class));
    }

}