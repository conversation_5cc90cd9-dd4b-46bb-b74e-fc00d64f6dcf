# ========== ERP Supplier (1-030-100-000) ==========
erp.supplier.not.exists=Supplier does not exist
erp.supplier.not.enable=Supplier ({}) is not enabled

# ========== ERP Purchase Order (1-030-101-000) ==========
erp.purchase.order.not.exists=Purchase order does not exist
erp.purchase.order.delete.fail.approve=Purchase order ({}) is already approved and cannot be deleted
erp.purchase.order.process.fail=Reversal failed, only approved purchase orders can be reversed
erp.purchase.order.approve.fail=Approval failed, only unapproved purchase orders can be approved
erp.purchase.order.no.exists=Failed to generate purchase order number, please resubmit
erp.purchase.order.update.fail.approve=Purchase order ({}) is already approved and cannot be modified
erp.purchase.order.not.approve=Purchase order is not approved, operation not allowed
erp.purchase.order.item.in.fail.product.exceed=Purchase order item ({}) exceeds maximum allowed quantity ({})
erp.purchase.order.process.fail.exists.in=Reversal failed, corresponding purchase receipt already exists
erp.purchase.order.item.return.fail.in.exceed=Purchase order item ({}) exceeds maximum allowed return quantity ({})
erp.purchase.order.process.fail.exists.return=Reversal failed, corresponding purchase return already exists

# ========== ERP Purchase In (1-030-102-000) ==========
erp.purchase.in.not.exists=Purchase receipt does not exist
erp.purchase.in.delete.fail.approve=Purchase receipt ({}) is already approved and cannot be deleted
erp.purchase.in.process.fail=Reversal failed, only approved receipts can be reversed
erp.purchase.in.approve.fail=Approval failed, only unapproved receipts can be approved
erp.purchase.in.no.exists=Failed to generate receipt number, please resubmit
erp.purchase.in.update.fail.approve=Purchase receipt ({}) is already approved and cannot be modified
erp.purchase.in.not.approve=Purchase receipt is not approved, operation not allowed
erp.purchase.in.fail.payment.price.exceed=Payment amount ({}) exceeds total purchase receipt amount ({})
erp.purchase.in.process.fail.exists.payment=Reversal failed, corresponding payment record already exists

# ========== ERP Purchase Return (1-030-103-000) ==========
erp.purchase.return.not.exists=Purchase return does not exist
erp.purchase.return.delete.fail.approve=Purchase return ({}) is already approved and cannot be deleted
erp.purchase.return.process.fail=Reversal failed, only approved returns can be reversed
erp.purchase.return.approve.fail=Approval failed, only unapproved returns can be approved
erp.purchase.return.no.exists=Failed to generate return number, please resubmit
erp.purchase.return.update.fail.approve=Purchase return ({}) is already approved and cannot be modified
erp.purchase.return.not.approve=Purchase return is not approved, operation not allowed
erp.purchase.return.fail.refund.price.exceed=Refund amount ({}) exceeds total purchase return amount ({})
erp.purchase.return.process.fail.exists.refund=Reversal failed, corresponding refund record already exists

# ========== ERP Customer (1-030-200-000) ==========
erp.customer.not.exists=Customer does not exist
erp.customer.not.enable=Customer ({}) is not enabled

# ========== ERP Sale Order (1-030-201-000) ==========
erp.sale.order.not.exists=Sale order does not exist
erp.sale.order.delete.fail.approve=Sale order ({}) is already approved and cannot be deleted
erp.sale.order.process.fail=Reversal failed, only approved sale orders can be reversed
erp.sale.order.approve.fail=Approval failed, only unapproved sale orders can be approved
erp.sale.order.no.exists=Failed to generate sale order number, please resubmit
erp.sale.order.update.fail.approve=Sale order ({}) is already approved and cannot be modified
erp.sale.order.not.approve=Sale order is not approved, operation not allowed
erp.sale.order.item.out.fail.product.exceed=Sale order item ({}) exceeds maximum allowed quantity ({})
erp.sale.order.process.fail.exists.out=Reversal failed, corresponding sale shipment already exists
erp.sale.order.item.return.fail.out.exceed=Sale order item ({}) exceeds maximum allowed return quantity ({})
erp.sale.order.process.fail.exists.return=Reversal failed, corresponding sale return already exists

# ========== ERP Sale Out (1-030-202-000) ==========
erp.sale.out.not.exists=Sale shipment does not exist
erp.sale.out.delete.fail.approve=Sale shipment ({}) is already approved and cannot be deleted
erp.sale.out.process.fail=Reversal failed, only approved shipments can be reversed
erp.sale.out.approve.fail=Approval failed, only unapproved shipments can be approved
erp.sale.out.no.exists=Failed to generate shipment number, please resubmit
erp.sale.out.update.fail.approve=Sale shipment ({}) is already approved and cannot be modified
erp.sale.out.not.approve=Sale shipment is not approved, operation not allowed
erp.sale.out.fail.receipt.price.exceed=Receipt amount ({}) exceeds total sale shipment amount ({})
erp.sale.out.process.fail.exists.receipt=Reversal failed, corresponding receipt record already exists

# ========== ERP Sale Return (1-030-203-000) ==========
erp.sale.return.not.exists=Sale return does not exist
erp.sale.return.delete.fail.approve=Sale return ({}) is already approved and cannot be deleted
erp.sale.return.process.fail=Reversal failed, only approved returns can be reversed
erp.sale.return.approve.fail=Approval failed, only unapproved returns can be approved
erp.sale.return.no.exists=Failed to generate return number, please resubmit
erp.sale.return.update.fail.approve=Sale return ({}) is already approved and cannot be modified
erp.sale.return.not.approve=Sale return is not approved, operation not allowed
erp.sale.return.fail.refund.price.exceed=Refund amount ({}) exceeds total sale return amount ({})
erp.sale.return.process.fail.exists.refund=Reversal failed, corresponding refund record already exists

# ========== ERP Warehouse (1-030-400-000) ==========
erp.warehouse.not.exists=Warehouse does not exist
erp.warehouse.not.enable=Warehouse ({}) is not enabled

# ========== ERP Other Stock In (1-030-401-000) ==========
erp.stock.in.not.exists=Other stock in does not exist
erp.stock.in.delete.fail.approve=Other stock in ({}) is already approved and cannot be deleted
erp.stock.in.process.fail=Reversal failed, only approved stock in records can be reversed
erp.stock.in.approve.fail=Approval failed, only unapproved stock in records can be approved
erp.stock.in.no.exists=Failed to generate stock in number, please resubmit
erp.stock.in.update.fail.approve=Other stock in ({}) is already approved and cannot be modified

# ========== ERP Other Stock Out (1-030-402-000) ==========
erp.stock.out.not.exists=Other stock out does not exist
erp.stock.out.delete.fail.approve=Other stock out ({}) is already approved and cannot be deleted
erp.stock.out.process.fail=Reversal failed, only approved stock out records can be reversed
erp.stock.out.approve.fail=Approval failed, only unapproved stock out records can be approved
erp.stock.out.no.exists=Failed to generate stock out number, please resubmit
erp.stock.out.update.fail.approve=Other stock out ({}) is already approved and cannot be modified

# ========== ERP Stock Move (1-030-403-000) ==========
erp.stock.move.not.exists=Stock move does not exist
erp.stock.move.delete.fail.approve=Stock move ({}) is already approved and cannot be deleted
erp.stock.move.process.fail=Reversal failed, only approved stock move records can be reversed
erp.stock.move.approve.fail=Approval failed, only unapproved stock move records can be approved
erp.stock.move.no.exists=Failed to generate stock move number, please resubmit
erp.stock.move.update.fail.approve=Stock move ({}) is already approved and cannot be modified

# ========== ERP Stock Check (1-030-403-000) ==========
erp.stock.check.not.exists=Stock check does not exist
erp.stock.check.delete.fail.approve=Stock check ({}) is already approved and cannot be deleted
erp.stock.check.process.fail=Reversal failed, only approved stock check records can be reversed
erp.stock.check.approve.fail=Approval failed, only unapproved stock check records can be approved
erp.stock.check.no.exists=Failed to generate stock check number, please resubmit
erp.stock.check.update.fail.approve=Stock check ({}) is already approved and cannot be modified

# ========== ERP Product Inventory (1-030-404-000) ==========
erp.stock.count.negative=Operation failed, product ({}) in warehouse ({}) has inventory: {}, which is less than the change quantity: {}
erp.stock.count.negative2=Operation failed, product ({}) in warehouse ({}) has insufficient inventory

# ========== ERP Product (1-030-500-000) ==========
erp.product.not.exists=Product does not exist
erp.product.not.enable=Product ({}) is not enabled

# ========== ERP Product Category (1-030-501-000) ==========
erp.product.category.not.exists=Product category does not exist
erp.product.category.exits.children=There are child categories, cannot be deleted
erp.product.category.parent.not.exits=Parent category does not exist
erp.product.category.parent.error=Cannot set itself as the parent category
erp.product.category.name.duplicate=A category with this name already exists
erp.product.category.parent.is.child=Cannot set its own child category as parent
erp.product.category.exits.product=There are products using this category, cannot be deleted

# ========== ERP Product Unit (1-030-502-000) ==========
erp.product.unit.not.exists=Product unit does not exist
erp.product.unit.name.duplicate=A product unit with this name already exists
erp.product.unit.exits.product=There are products using this unit, cannot be deleted

# ========== ERP Account (1-030-600-000) ==========
erp.account.not.exists=Account does not exist
erp.account.not.enable=Account ({}) is not enabled

# ========== ERP Payment (1-030-601-000) ==========
erp.finance.payment.not.exists=Payment record does not exist
erp.finance.payment.delete.fail.approve=Payment record ({}) is already approved and cannot be deleted
erp.finance.payment.process.fail=Reversal failed, only approved payment records can be reversed
erp.finance.payment.approve.fail=Approval failed, only unapproved payment records can be approved
erp.finance.payment.no.exists=Failed to generate payment number, please resubmit
erp.finance.payment.update.fail.approve=Payment record ({}) is already approved and cannot be modified

# ========== ERP Receipt (1-030-602-000) ==========
erp.finance.receipt.not.exists=Receipt does not exist
erp.finance.receipt.delete.fail.approve=Receipt ({}) is already approved and cannot be deleted
erp.finance.receipt.process.fail=Reversal failed, only approved receipt records can be reversed
erp.finance.receipt.approve.fail=Approval failed, only unapproved receipt records can be approved
erp.finance.receipt.no.exists=Failed to generate receipt number, please resubmit
erp.finance.receipt.update.fail.approve=Receipt ({}) is already approved and cannot be modified
