# ========== ERP 供应商（1-030-100-000） ==========
erp.supplier.not.exists=供应商不存在
erp.supplier.not.enable=供应商({})未启用

# ========== ERP 采购订单（1-030-101-000） ==========
erp.purchase.order.not.exists=采购订单不存在
erp.purchase.order.delete.fail.approve=采购订单({})已审核，无法删除
erp.purchase.order.process.fail=反审核失败，只有已审核的采购订单才能反审核
erp.purchase.order.approve.fail=审核失败，只有未审核的采购订单才能审核
erp.purchase.order.no.exists=生成采购单号失败，请重新提交
erp.purchase.order.update.fail.approve=采购订单({})已审核，无法修改
erp.purchase.order.not.approve=采购订单未审核，无法操作
erp.purchase.order.item.in.fail.product.exceed=采购订单项({})超过最大允许入库数量({})
erp.purchase.order.process.fail.exists.in=反审核失败，已存在对应的采购入库单
erp.purchase.order.item.return.fail.in.exceed=采购订单项({})超过最大允许退货数量({})
erp.purchase.order.process.fail.exists.return=反审核失败，已存在对应的采购退货单

# ========== ERP 采购入库（1-030-102-000） ==========
erp.purchase.in.not.exists=采购入库单不存在
erp.purchase.in.delete.fail.approve=采购入库单({})已审核，无法删除
erp.purchase.in.process.fail=反审核失败，只有已审核的入库单才能反审核
erp.purchase.in.approve.fail=审核失败，只有未审核的入库单才能审核
erp.purchase.in.no.exists=生成入库单失败，请重新提交
erp.purchase.in.update.fail.approve=采购入库单({})已审核，无法修改
erp.purchase.in.not.approve=采购入库单未审核，无法操作
erp.purchase.in.fail.payment.price.exceed=付款金额({})超过采购入库单总金额({})
erp.purchase.in.process.fail.exists.payment=反审核失败，已存在对应的付款单

# ========== ERP 采购退货（1-030-103-000） ==========
erp.purchase.return.not.exists=采购退货单不存在
erp.purchase.return.delete.fail.approve=采购退货单({})已审核，无法删除
erp.purchase.return.process.fail=反审核失败，只有已审核的退货单才能反审核
erp.purchase.return.approve.fail=审核失败，只有未审核的退货单才能审核
erp.purchase.return.no.exists=生成退货单失败，请重新提交
erp.purchase.return.update.fail.approve=采购退货单({})已审核，无法修改
erp.purchase.return.not.approve=采购退货单未审核，无法操作
erp.purchase.return.fail.refund.price.exceed=退款金额({})超过采购退货单总金额({})
erp.purchase.return.process.fail.exists.refund=反审核失败，已存在对应的退款单

# ========== ERP 客户（1-030-200-000）==========
erp.customer.not.exists=客户不存在
erp.customer.not.enable=客户({})未启用

# ========== ERP 销售订单（1-030-201-000） ==========
erp.sale.order.not.exists=销售订单不存在
erp.sale.order.delete.fail.approve=销售订单({})已审核，无法删除
erp.sale.order.process.fail=反审核失败，只有已审核的销售订单才能反审核
erp.sale.order.approve.fail=审核失败，只有未审核的销售订单才能审核
erp.sale.order.no.exists=生成销售单号失败，请重新提交
erp.sale.order.update.fail.approve=销售订单({})已审核，无法修改
erp.sale.order.not.approve=销售订单未审核，无法操作
erp.sale.order.item.out.fail.product.exceed=销售订单项({})超过最大允许出库数量({})
erp.sale.order.process.fail.exists.out=反审核失败，已存在对应的销售出库单
erp.sale.order.item.return.fail.out.exceed=销售订单项({})超过最大允许退货数量({})
erp.sale.order.process.fail.exists.return=反审核失败，已存在对应的销售退货单

# ========== ERP 销售出库（1-030-202-000） ==========
erp.sale.out.not.exists=销售出库单不存在
erp.sale.out.delete.fail.approve=销售出库单({})已审核，无法删除
erp.sale.out.process.fail=反审核失败，只有已审核的出库单才能反审核
erp.sale.out.approve.fail=审核失败，只有未审核的出库单才能审核
erp.sale.out.no.exists=生成出库单失败，请重新提交
erp.sale.out.update.fail.approve=销售出库单({})已审核，无法修改
erp.sale.out.not.approve=销售出库单未审核，无法操作
erp.sale.out.fail.receipt.price.exceed=收款金额({})超过销售出库单总金额({})
erp.sale.out.process.fail.exists.receipt=反审核失败，已存在对应的收款单

# ========== ERP 销售退货（1-030-203-000） ==========
erp.sale.return.not.exists=销售退货单不存在
erp.sale.return.delete.fail.approve=销售退货单({})已审核，无法删除
erp.sale.return.process.fail=反审核失败，只有已审核的退货单才能反审核
erp.sale.return.approve.fail=审核失败，只有未审核的退货单才能审核
erp.sale.return.no.exists=生成退货单失败，请重新提交
erp.sale.return.update.fail.approve=销售退货单({})已审核，无法修改
erp.sale.return.not.approve=销售退货单未审核，无法操作
erp.sale.return.fail.refund.price.exceed=退款金额({})超过销售退货单总金额({})
erp.sale.return.process.fail.exists.refund=反审核失败，已存在对应的退款单

# ========== ERP 仓库 1-030-400-000 ==========
erp.warehouse.not.exists=仓库不存在
erp.warehouse.not.enable=仓库({})未启用

# ========== ERP 其它入库单 1-030-401-000 ==========
erp.stock.in.not.exists=其它入库单不存在
erp.stock.in.delete.fail.approve=其它入库单({})已审核，无法删除
erp.stock.in.process.fail=反审核失败，只有已审核的入库单才能反审核
erp.stock.in.approve.fail=审核失败，只有未审核的入库单才能审核
erp.stock.in.no.exists=生成入库单失败，请重新提交
erp.stock.in.update.fail.approve=其它入库单({})已审核，无法修改

# ========== ERP 其它出库单 1-030-402-000 ==========
erp.stock.out.not.exists=其它出库单不存在
erp.stock.out.delete.fail.approve=其它出库单({})已审核，无法删除
erp.stock.out.process.fail=反审核失败，只有已审核的出库单才能反审核
erp.stock.out.approve.fail=审核失败，只有未审核的出库单才能审核
erp.stock.out.no.exists=生成出库单失败，请重新提交
erp.stock.out.update.fail.approve=其它出库单({})已审核，无法修改

# ========== ERP 库存调拨单 1-030-403-000 ==========
erp.stock.move.not.exists=库存调拨单不存在
erp.stock.move.delete.fail.approve=库存调拨单({})已审核，无法删除
erp.stock.move.process.fail=反审核失败，只有已审核的调拨单才能反审核
erp.stock.move.approve.fail=审核失败，只有未审核的调拨单才能审核
erp.stock.move.no.exists=生成调拨号失败，请重新提交
erp.stock.move.update.fail.approve=库存调拨单({})已审核，无法修改

# ========== ERP 库存盘点单 1-030-403-000 ==========
erp.stock.check.not.exists=库存盘点单不存在
erp.stock.check.delete.fail.approve=库存盘点单({})已审核，无法删除
erp.stock.check.process.fail=反审核失败，只有已审核的盘点单才能反审核
erp.stock.check.approve.fail=审核失败，只有未审核的盘点单才能审核
erp.stock.check.no.exists=生成盘点号失败，请重新提交
erp.stock.check.update.fail.approve=库存盘点单({})已审核，无法修改

# ========== ERP 产品库存 1-030-404-000 ==========
erp.stock.count.negative=操作失败，产品({})所在仓库({})的库存：{}，小于变更数量：{}
erp.stock.count.negative2=操作失败，产品({})所在仓库({})的库存不足

# ========== ERP 产品 1-030-500-000 ==========
erp.product.not.exists=产品不存在
erp.product.not.enable=产品({})未启用

# ========== ERP 产品分类 1-030-501-000 ==========
erp.product.category.not.exists=产品分类不存在
erp.product.category.exits.children=存在存在子产品分类，无法删除
erp.product.category.parent.not.exits=父级产品分类不存在
erp.product.category.parent.error=不能设置自己为父产品分类
erp.product.category.name.duplicate=已经存在该分类名称的产品分类
erp.product.category.parent.is.child=不能设置自己的子分类为父分类
erp.product.category.exits.product=存在产品使用该分类，无法删除

# ========== ERP 产品单位 1-030-502-000 ==========
erp.product.unit.not.exists=产品单位不存在
erp.product.unit.name.duplicate=已存在该名字的产品单位
erp.product.unit.exits.product=存在产品使用该单位，无法删除

# ========== ERP 结算账户 1-030-600-000 ==========
erp.account.not.exists=结算账户不存在
erp.account.not.enable=结算账户({})未启用

# ========== ERP 付款单 1-030-601-000 ==========
erp.finance.payment.not.exists=付款单不存在
erp.finance.payment.delete.fail.approve=付款单({})已审核，无法删除
erp.finance.payment.process.fail=反审核失败，只有已审核的付款单才能反审核
erp.finance.payment.approve.fail=审核失败，只有未审核的付款单才能审核
erp.finance.payment.no.exists=生成付款单号失败，请重新提交
erp.finance.payment.update.fail.approve=付款单({})已审核，无法修改

# ========== ERP 收款单 1-030-602-000 ==========
erp.finance.receipt.not.exists=收款单不存在
erp.finance.receipt.delete.fail.approve=收款单({})已审核，无法删除
erp.finance.receipt.process.fail=反审核失败，只有已审核的收款单才能反审核
erp.finance.receipt.approve.fail=审核失败，只有未审核的收款单才能审核
erp.finance.receipt.no.exists=生成收款单号失败，请重新提交
erp.finance.receipt.update.fail.approve=收款单({})已审核，无法修改
