package cn.iocoder.yudao.module.erp.controller.admin.purchase.vo.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @program: ruoyi-vue-pro
 * @description: Erp采购订单快递发货信息VO
 * @author: DingXiao
 * @create: 2025-04-16 19:45
 **/
@Schema(description = "管理后台 - Erp采购订单快递发货信息 Request VO")
@Data
public class ErpPurchaseOrderDeliveryReqVO {

    @Schema(description = "采购订单项编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "采购订单项编号不能为空")
    private Long id;

    @Schema(description = "发货物流公司编号", example = "1")
    @NotNull(message = "发货物流公司不能为空")
    private Long logisticsId;

    @Schema(description = "发货物流单号", example = "SF123456789")
    private String logisticsNo;
}
