# 支付密码安全防护功能说明

## 功能概述

支付密码安全防护功能是为了防止恶意用户通过暴力破解的方式获取用户支付密码而设计的安全机制。该功能通过限制错误尝试次数、账户锁定、自动重置等方式来保护用户账户安全。

## 核心特性

### 1. 错误次数限制
- 系统会记录每个用户的支付密码错误尝试次数
- 当错误次数达到配置的上限时，自动锁定用户账户
- 错误次数存储在Redis中，支持分布式部署

### 2. 账户锁定机制
- 达到最大错误次数后，用户账户将被锁定指定时间
- 锁定期间，用户无法进行任何支付密码验证操作
- 锁定状态存储在Redis中，具有自动过期功能

### 3. 自动重置机制
- 在指定时间内没有错误尝试，错误计数将自动重置为0
- 支持灵活配置重置时间间隔

### 4. 日志审计
- 记录所有支付密码错误尝试的详细日志
- 记录账户锁定事件的详细日志
- 日志包含用户信息、IP地址、时间戳等关键信息

### 5. 管理员干预
- 管理员可以手动解锁被锁定的用户账户
- 提供管理后台接口进行用户解锁操作

## 配置说明

### 配置文件位置
配置位于 `application.yaml` 文件中：

```yaml
yudao:
  member:
    pay-password-security:
      enabled: true # 是否启用支付密码安全防护
      max-error-count: 5 # 最大错误次数，超过此次数将被锁定
      lock-time-minutes: 30 # 锁定时间，单位：分钟
      reset-time-minutes: 60 # 错误计数重置时间，单位：分钟
```

### 配置参数详解

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| enabled | Boolean | true | 是否启用支付密码安全防护功能 |
| max-error-count | Integer | 5 | 最大错误次数，超过此次数将被锁定 |
| lock-time-minutes | Integer | 30 | 锁定时间，单位：分钟 |
| reset-time-minutes | Integer | 60 | 错误计数重置时间，单位：分钟 |

### 配置建议

#### 生产环境推荐配置
```yaml
yudao:
  member:
    pay-password-security:
      enabled: true
      max-error-count: 5
      lock-time-minutes: 30
      reset-time-minutes: 60
```

#### 开发环境配置
```yaml
yudao:
  member:
    pay-password-security:
      enabled: false # 开发环境可以关闭以便测试
      max-error-count: 3
      lock-time-minutes: 5
      reset-time-minutes: 10
```

## Redis存储结构

### 错误次数缓存
- **Key格式**: `pay_password_error_count:{tenantId}:{userId}`
- **Value格式**: 错误次数（数字）
- **过期时间**: 根据 `reset-time-minutes` 配置

### 锁定状态缓存
- **Key格式**: `pay_password_locked:{tenantId}:{userId}`
- **Value格式**: 锁定时间戳
- **过期时间**: 根据 `lock-time-minutes` 配置

## 工作流程

### 支付密码验证流程
1. 检查用户是否存在
2. 检查是否已设置支付密码
3. **检查用户是否被锁定**
4. 验证支付密码
5. 根据验证结果处理：
   - **成功**: 清除错误记录
   - **失败**: 增加错误次数，检查是否需要锁定

### 错误处理流程
1. 记录错误尝试日志
2. 增加Redis中的错误计数
3. 检查是否达到最大错误次数
4. 如果达到上限：
   - 锁定用户账户
   - 记录锁定事件日志
   - 返回锁定错误信息

## 日志格式

### 错误尝试日志
```
[支付密码验证失败] 用户: ID:123,Mobile:13800138000, 当前错误次数: 3/5, IP: *************
```

### 账户锁定日志
```
[支付密码账户锁定] 用户: ID:123,Mobile:13800138000, 错误次数达到上限: 5, 锁定时间: 30分钟, IP: *************
```

### 手动解锁日志
```
[支付密码手动解锁] 用户ID: 123
```

## 管理员操作

### 解锁用户账户
**接口地址**: `PUT /member/user/unlock-pay-password?id={userId}`

**权限要求**: `member:user:update`

**请求示例**:
```bash
curl -X PUT "http://localhost:8080/member/user/unlock-pay-password?id=123" \
  -H "Authorization: Bearer {adminToken}"
```

## 监控建议

### 关键指标监控
1. **错误尝试频率**: 监控支付密码错误尝试的频率
2. **锁定账户数量**: 监控被锁定的账户数量
3. **解锁操作频率**: 监控管理员解锁操作的频率

### 告警设置
1. 单个用户短时间内多次错误尝试
2. 大量用户账户被锁定
3. 频繁的管理员解锁操作

## 安全建议

### 配置安全
1. 生产环境必须启用此功能
2. 根据业务需求合理设置错误次数上限
3. 锁定时间不宜过短，建议至少15分钟

### 运维安全
1. 定期检查错误尝试日志，识别异常行为
2. 监控Redis中的相关缓存数据
3. 建立用户申诉和解锁流程

### 用户体验
1. 在前端明确提示剩余尝试次数
2. 提供忘记密码的重置渠道
3. 在用户被锁定时提供明确的解锁指引

## 故障排查

### 常见问题

#### 1. 功能未生效
- 检查配置文件中 `enabled` 是否为 `true`
- 检查Redis连接是否正常
- 检查相关Bean是否正确注入

#### 2. 锁定时间不准确
- 检查系统时间是否正确
- 检查Redis过期时间设置
- 检查配置文件中的时间单位

#### 3. 错误次数统计异常
- 检查Redis中的计数器是否正常
- 检查多实例部署时的数据一致性
- 检查租户隔离是否正确

### 调试命令

#### 查看用户错误次数
```bash
redis-cli get "pay_password_error_count:1:123"
```

#### 查看用户锁定状态
```bash
redis-cli get "pay_password_locked:1:123"
```

#### 手动清除用户状态
```bash
redis-cli del "pay_password_error_count:1:123"
redis-cli del "pay_password_locked:1:123"
```

## 版本更新说明

### v1.0.0
- 初始版本，支持基本的错误次数限制和账户锁定功能
- 支持Redis存储和多租户隔离
- 提供管理员解锁功能
- 完整的日志审计功能
