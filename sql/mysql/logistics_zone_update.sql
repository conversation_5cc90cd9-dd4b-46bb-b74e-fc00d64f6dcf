-- 优化物流分区表结构，支持层级行政区划
ALTER TABLE agent_logistics_zone
ADD COLUMN state_province VARCHAR(100) COMMENT '一级行政区划 (州/省/自治区等)' AFTER zone_name,
ADD COLUMN city VARCHAR(100) COMMENT '二级行政区划 (城市/地区等)' AFTER state_province,
ADD COLUMN district VARCHAR(100) COMMENT '三级行政区划 (区/县等)' AFTER city,
ADD COLUMN special_area_type VARCHAR(20) COMMENT '特殊区域类型 (ISLAND-岛屿, TERRITORY-领土, MILITARY-军事基地等)' AFTER district,
ADD COLUMN full_area_name VARCHAR(300) COMMENT '完整区域描述 (用于显示和搜索)' AFTER special_area_type,
ADD COLUMN restriction_type VARCHAR(20) DEFAULT 'NORMAL' COMMENT '限制类型 (FORBIDDEN-禁止配送, REMOTE_FEE-偏远地区费, NORMAL-正常)' AFTER postal_codes,
ADD COLUMN fee_formula VARCHAR(500) COMMENT '附加费公式 (如：3*weight_kg,min:48 表示每公斤3元，最低48元)' AFTER restriction_type,
ADD COLUMN remark VARCHAR(500) COMMENT '备注说明' AFTER fee_formula,
ADD COLUMN sort INT DEFAULT 0 COMMENT '排序' AFTER remark;

-- 修改现有字段注释
ALTER TABLE agent_logistics_zone 
MODIFY COLUMN zone_code VARCHAR(50) COMMENT '分区编码 (FORBIDDEN-不可到达, REMOTE-偏远地区, NORMAL-正常区域)',
MODIFY COLUMN postal_codes TEXT COMMENT '邮编配置 JSON格式存储邮编范围或列表';

-- 添加索引优化查询性能
CREATE INDEX idx_country_product ON agent_logistics_zone(country_code, product_id);
CREATE INDEX idx_state_province ON agent_logistics_zone(state_province);
CREATE INDEX idx_city ON agent_logistics_zone(city);
CREATE INDEX idx_special_area_type ON agent_logistics_zone(special_area_type);
CREATE INDEX idx_restriction_type ON agent_logistics_zone(restriction_type);
CREATE INDEX idx_full_area_name ON agent_logistics_zone(full_area_name);

-- 云途物流分区示例数据（层级结构）
INSERT INTO agent_logistics_zone (country_code, product_id, zone_code, zone_name, state_province, city, district, special_area_type, full_area_name, postal_codes, restriction_type, fee_formula, remark, sort, status) VALUES

-- 美国州级限制
('US', 1, 'FORBIDDEN', '禁止配送区域', 'Alaska', NULL, NULL, NULL, 'Alaska', '["99500-99999"]', 'FORBIDDEN', NULL, '阿拉斯加州禁止配送', 1, 1),
('US', 1, 'FORBIDDEN', '禁止配送区域', NULL, NULL, NULL, 'MILITARY', 'Armed Forces Americas', '["34000-34099"]', 'FORBIDDEN', NULL, '美军基地禁止配送', 2, 1),
('US', 1, 'FORBIDDEN', '禁止配送区域', NULL, NULL, NULL, 'MILITARY', 'Armed Forces Europe', '["09000-09999"]', 'FORBIDDEN', NULL, '欧洲美军基地禁止配送', 3, 1),

-- 美国城市级限制示例
('US', 1, 'REMOTE', '偏远地区', 'Hawaii', 'Honolulu', NULL, 'ISLAND', 'Hawaii Honolulu', '["96700-96899"]', 'REMOTE_FEE', '8*weight_kg,min:50', '夏威夷偏远费', 4, 1),
('US', 1, 'REMOTE', '偏远地区', 'California', 'Los Angeles', 'Beverly Hills', NULL, 'California Los Angeles Beverly Hills', '["90210"]', 'REMOTE_FEE', '3*weight_kg,min:25', '比佛利山庄偏远费', 5, 1),

-- 葡萄牙地区限制
('PT', 1, 'FORBIDDEN', '禁止配送区域', NULL, NULL, NULL, 'ISLAND', 'Azores', '["9000-9999"]', 'FORBIDDEN', NULL, '亚速尔群岛禁止配送', 1, 1),

-- 西班牙地区限制
('ES', 1, 'REMOTE', '偏远地区', NULL, NULL, NULL, 'ISLAND', 'Canary Islands', '["35000-35999", "38000-38999"]', 'REMOTE_FEE', '5*weight_kg,min:30', '加纳利群岛偏远费', 1, 1),

-- 澳大利亚邮编分区
('AU', 1, 'ZONE1', '澳洲1区', 'New South Wales', 'Sydney', NULL, NULL, 'New South Wales Sydney', '["2000-2999"]', 'NORMAL', NULL, '悉尼主要区域', 1, 1),
('AU', 1, 'ZONE1', '澳洲1区', 'Victoria', 'Melbourne', NULL, NULL, 'Victoria Melbourne', '["3000-3999"]', 'NORMAL', NULL, '墨尔本主要区域', 2, 1),
('AU', 1, 'ZONE2', '澳洲2区', 'South Australia', 'Adelaide', NULL, NULL, 'South Australia Adelaide', '["5000-5999"]', 'NORMAL', NULL, '阿德莱德地区', 3, 1),
('AU', 1, 'ZONE3', '澳洲3区', 'Tasmania', NULL, NULL, 'ISLAND', 'Tasmania', '["7000-7999"]', 'REMOTE_FEE', '2*weight_kg,min:20', '塔斯马尼亚岛偏远费', 4, 1),
('AU', 1, 'FORBIDDEN', '禁止配送区域', NULL, NULL, NULL, 'TERRITORY', 'External Territories', '["2540", "6798", "7151"]', 'FORBIDDEN', NULL, '外部领土禁止配送', 5, 1);
