-- 为物流产品价格表添加结构化尺寸限制字段
ALTER TABLE agent_logistics_product_price
ADD COLUMN size_restrictions TEXT COMMENT '尺寸限制配置 JSON格式，单位：cm，针对该国家/分区的特殊尺寸限制' AFTER tariff_rate;

-- 添加索引
CREATE INDEX idx_product_country ON agent_logistics_product_price(product_id, country_code);
CREATE INDEX idx_country_zone ON agent_logistics_product_price(country_code, zone_code);

-- 示例数据：针对不同国家的尺寸限制

-- 法国0区 - 更严格的尺寸限制
UPDATE agent_logistics_product_price SET size_restrictions = '{"maxLength":100,"maxWidth":50,"maxHeight":50,"maxGirth":250,"maxSingleSide":100}'
WHERE product_id = 1 AND country_code = 'FR' AND zone_code = 'ZONE0';

-- 欧洲1区国家 - 标准尺寸限制
UPDATE agent_logistics_product_price SET size_restrictions = '{"maxLength":120,"maxWidth":60,"maxHeight":60,"maxGirth":300,"maxSingleSide":120}'
WHERE product_id = 1 AND country_code IN ('DE', 'IT', 'ES', 'NL', 'BE') AND zone_code = 'ZONE1';

-- 欧洲2区国家 - 标准尺寸限制
UPDATE agent_logistics_product_price SET size_restrictions = '{"maxLength":120,"maxWidth":60,"maxHeight":60,"maxGirth":300,"maxSingleSide":120}'
WHERE product_id = 1 AND country_code IN ('AT', 'CH', 'DK', 'SE', 'NO') AND zone_code = 'ZONE2';

-- 欧洲3区国家 - 标准尺寸限制
UPDATE agent_logistics_product_price SET size_restrictions = '{"maxLength":120,"maxWidth":60,"maxHeight":60,"maxGirth":300,"maxSingleSide":120}'
WHERE product_id = 1 AND country_code IN ('PL', 'CZ', 'HU', 'SK', 'SI') AND zone_code = 'ZONE3';
