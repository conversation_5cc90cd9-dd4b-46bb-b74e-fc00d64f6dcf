-- 添加阶梯递增价格配置字段
-- 执行时间：2025-07-22

-- 1. 检查字段是否已存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ruoyi_vue_pro' 
  AND TABLE_NAME = 'agent_logistics_product_price' 
  AND COLUMN_NAME = 'tiered_incremental_prices';

-- 2. 如果字段不存在，则添加字段
ALTER TABLE agent_logistics_product_price 
ADD COLUMN tiered_incremental_prices TEXT COMMENT '阶梯递增价格配置(JSON格式)' 
AFTER tiered_prices;

-- 3. 验证字段是否添加成功
DESCRIBE agent_logistics_product_price;

-- 4. 查看表结构中的新字段
SHOW FULL COLUMNS FROM agent_logistics_product_price WHERE Field = 'tiered_incremental_prices';

-- 5. 创建测试数据验证
INSERT INTO agent_logistics_product_price (
    product_id, country_code, 
    price_type, tiered_incremental_prices,
    status, create_time, update_time, creator, updater
) VALUES (
    999, 'TEST',
    'TIERED_INCREMENTAL', 
    '[{"tierStart":1,"tierEnd":15000,"firstWeight":100,"firstPrice":14210,"additionalWeight":100,"additionalPrice":2610,"registrationFee":0}]',
    1, NOW(), NOW(), 'system', 'system'
);

-- 6. 验证数据插入
SELECT id, product_id, country_code, price_type, tiered_incremental_prices 
FROM agent_logistics_product_price 
WHERE country_code = 'TEST';

-- 7. 清理测试数据
DELETE FROM agent_logistics_product_price WHERE country_code = 'TEST';
