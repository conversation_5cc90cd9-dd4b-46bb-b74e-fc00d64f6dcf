-- 测试价格规则时间条件功能
-- 执行前请确保已连接到正确的数据库

-- 1. 清理测试数据
DELETE FROM agent_logistics_product_price WHERE country_code = 'TIME_TEST';

-- 2. 插入测试数据
INSERT INTO agent_logistics_product_price (
    product_id, country_code, price_type,
    first_unit, first_price, additional_unit, additional_price,
    effective_time, expire_time,
    status, create_time, update_time, creator, updater
) VALUES 
-- 测试用例1: 未来生效的价格（应该被过滤）
(1, 'TIME_TEST', 'INCREMENTAL', 500, 10000, 500, 5000,
 DATE_ADD(NOW(), INTERVAL 1 DAY), NULL, 
 1, NOW(), NOW(), 'test', 'test'),

-- 测试用例2: 已过期的价格（应该被过滤）
(1, 'TIME_TEST', 'INCREMENTAL', 500, 8000, 500, 4000,
 DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 
 1, NOW(), NOW(), 'test', 'test'),

-- 测试用例3: 当前有效的价格（应该被返回）
(1, 'TIME_TEST', 'INCREMENTAL', 500, 12000, 500, 6000,
 DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 1 DAY), 
 1, NOW(), NOW(), 'test', 'test'),

-- 测试用例4: 无时间限制的价格（应该被返回）
(1, 'TIME_TEST', 'INCREMENTAL', 500, 15000, 500, 7500,
 NULL, NULL, 
 1, NOW(), NOW(), 'test', 'test'),

-- 测试用例5: 只设置生效时间，已生效（应该被返回）
(2, 'TIME_TEST', 'INCREMENTAL', 500, 13000, 500, 6500,
 DATE_SUB(NOW(), INTERVAL 1 HOUR), NULL, 
 1, NOW(), NOW(), 'test', 'test'),

-- 测试用例6: 只设置失效时间，未失效（应该被返回）
(2, 'TIME_TEST', 'INCREMENTAL', 500, 14000, 500, 7000,
 NULL, DATE_ADD(NOW(), INTERVAL 1 HOUR), 
 1, NOW(), NOW(), 'test', 'test');

-- 3. 查看插入的测试数据
SELECT 
    id, product_id, first_price,
    effective_time,
    expire_time,
    CASE 
        WHEN effective_time IS NULL THEN '无生效时间限制'
        WHEN effective_time <= NOW() THEN '已生效'
        ELSE '未生效'
    END as effective_status,
    CASE 
        WHEN expire_time IS NULL THEN '无失效时间限制'
        WHEN expire_time > NOW() THEN '未失效'
        ELSE '已失效'
    END as expire_status,
    CASE 
        WHEN (effective_time IS NULL OR effective_time <= NOW()) 
         AND (expire_time IS NULL OR expire_time > NOW()) 
        THEN '有效'
        ELSE '无效'
    END as overall_status
FROM agent_logistics_product_price 
WHERE country_code = 'TIME_TEST'
ORDER BY id;

-- 4. 模拟系统查询（应该只返回有效的价格规则）
SELECT 
    id, product_id, first_price, effective_time, expire_time,
    '应该被返回' as note
FROM agent_logistics_product_price 
WHERE country_code = 'TIME_TEST'
  AND status = 1
  AND (effective_time IS NULL OR effective_time <= NOW())
  AND (expire_time IS NULL OR expire_time > NOW())
ORDER BY product_id, create_time DESC;

-- 5. 验证过滤掉的价格规则
SELECT 
    id, product_id, first_price, effective_time, expire_time,
    CASE 
        WHEN effective_time > NOW() THEN '未来生效，被过滤'
        WHEN expire_time <= NOW() THEN '已过期，被过滤'
        ELSE '其他原因'
    END as filter_reason
FROM agent_logistics_product_price 
WHERE country_code = 'TIME_TEST'
  AND status = 1
  AND NOT (
    (effective_time IS NULL OR effective_time <= NOW())
    AND (expire_time IS NULL OR expire_time > NOW())
  )
ORDER BY id;

-- 6. 统计信息
SELECT 
    COUNT(*) as total_rules,
    SUM(CASE WHEN (effective_time IS NULL OR effective_time <= NOW()) 
             AND (expire_time IS NULL OR expire_time > NOW()) 
        THEN 1 ELSE 0 END) as valid_rules,
    SUM(CASE WHEN effective_time > NOW() THEN 1 ELSE 0 END) as future_rules,
    SUM(CASE WHEN expire_time <= NOW() THEN 1 ELSE 0 END) as expired_rules
FROM agent_logistics_product_price 
WHERE country_code = 'TIME_TEST' AND status = 1;

-- 7. 清理测试数据（可选，测试完成后执行）
-- DELETE FROM agent_logistics_product_price WHERE country_code = 'TIME_TEST';
