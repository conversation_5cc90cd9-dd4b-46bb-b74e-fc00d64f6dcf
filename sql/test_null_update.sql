-- 测试时间字段null值更新功能
-- 执行前请确保已连接到正确的数据库

-- 1. 创建测试数据
INSERT INTO agent_logistics_product_price (
    product_id, country_code, price_type,
    first_unit, first_price,
    effective_time, expire_time,
    status, create_time, update_time, creator, updater
) VALUES (
    999, 'NULL_TEST', 'INCREMENTAL',
    500, 10000,
    '2024-12-01 00:00:00', '2024-12-31 23:59:59',
    1, NOW(), NOW(), 'test', 'test'
);

-- 2. 查看初始数据
SELECT id, product_id, country_code, effective_time, expire_time, create_time
FROM agent_logistics_product_price 
WHERE country_code = 'NULL_TEST';

-- 记录测试数据的ID（替换下面的ID）
SET @test_id = (SELECT id FROM agent_logistics_product_price WHERE country_code = 'NULL_TEST' LIMIT 1);

-- 3. 模拟MyBatis-Plus的updateById操作（修复前的行为）
-- 这个更新应该忽略NULL值，不会清空时间字段
UPDATE agent_logistics_product_price 
SET first_price = 12000,
    update_time = NOW()
WHERE id = @test_id;
-- 注意：这里没有设置 effective_time = NULL, expire_time = NULL

-- 4. 查看更新后的数据（修复前：时间字段不会被清空）
SELECT id, first_price, effective_time, expire_time, update_time
FROM agent_logistics_product_price 
WHERE id = @test_id;

-- 5. 模拟修复后的行为：强制更新NULL值
-- 这模拟了使用 FieldStrategy.IGNORED 策略的效果
UPDATE agent_logistics_product_price 
SET first_price = 15000,
    effective_time = NULL,
    expire_time = NULL,
    update_time = NOW()
WHERE id = @test_id;

-- 6. 查看修复后的数据（修复后：时间字段被正确清空）
SELECT id, first_price, effective_time, expire_time, update_time
FROM agent_logistics_product_price 
WHERE id = @test_id;

-- 7. 测试设置新的时间值
UPDATE agent_logistics_product_price 
SET effective_time = '2025-01-01 00:00:00',
    expire_time = '2025-01-31 23:59:59',
    update_time = NOW()
WHERE id = @test_id;

-- 8. 查看设置时间后的数据
SELECT id, effective_time, expire_time, update_time
FROM agent_logistics_product_price 
WHERE id = @test_id;

-- 9. 测试部分时间清空
UPDATE agent_logistics_product_price 
SET effective_time = NULL,
    -- expire_time 保持不变
    update_time = NOW()
WHERE id = @test_id;

-- 10. 查看部分清空后的数据
SELECT id, effective_time, expire_time, update_time
FROM agent_logistics_product_price 
WHERE id = @test_id;

-- 11. 验证时间条件查询
-- 这个查询应该返回我们的测试数据（因为effective_time为NULL）
SELECT id, country_code, effective_time, expire_time,
       '应该被返回（无生效时间限制）' as note
FROM agent_logistics_product_price 
WHERE country_code = 'NULL_TEST'
  AND status = 1
  AND (effective_time IS NULL OR effective_time <= NOW())
  AND (expire_time IS NULL OR expire_time > NOW());

-- 12. 清理测试数据
DELETE FROM agent_logistics_product_price WHERE country_code = 'NULL_TEST';

-- 13. 确认清理完成
SELECT COUNT(*) as remaining_test_data
FROM agent_logistics_product_price 
WHERE country_code = 'NULL_TEST';
