# ========== AUTH 模块 1-002-000-000 ==========
system.auth.login.bad_credentials=Échec de la connexion, nom d'utilisateur ou mot de passe incorrect
system.auth.login.user_disabled=Échec de la connexion, le compte est désactivé
system.auth.login.captcha_code_error=Code de captcha incorrect, raison : {}
system.auth.third_login.not_bind=Compte non lié, liaison requise
system.auth.mobile.not_exists=Le numéro de mobile n'existe pas
system.auth.register.captcha_code_error=Code de captcha incorrect, raison : {}

# ========== 菜单模块 1-002-001-000 ==========
system.menu.name.duplicate=Un menu avec ce nom existe déjà
system.menu.parent.not_exists=Le menu parent n'existe pas
system.menu.parent.error=Impossible de définir lui-même comme menu parent
system.menu.not_exists=Le menu n'existe pas
system.menu.exists_children=Impossible de supprimer, des sous-menus existent
system.menu.parent.not_dir_or_menu=Le type de menu parent doit être un répertoire ou un menu

# ========== 角色模块 1-002-002-000 ==========
system.role.not_exists=Le rôle n'existe pas
system.role.name.duplicate=Un rôle avec le nom [{}] existe déjà
system.role.code.duplicate=Un rôle avec l'identifiant [{}] existe déjà
system.role.can_not_update_system_type=Impossible d'opérer sur des rôles de type système
system.role.is_disable=Le rôle [{}] est désactivé
system.role.admin_code_error=L'identifiant [{}] ne peut pas être utilisé

# ========== 用户模块 1-002-003-000 ==========
system.user.username.exists=Le nom d'utilisateur existe déjà
system.user.mobile.exists=Le numéro de mobile existe déjà
system.user.email.exists=L'email existe déjà
system.user.not_exists=L'utilisateur n'existe pas
system.user.import_list_is_empty=Les données d'utilisateur importées ne peuvent pas être vides !
system.user.password.failed=Échec de la validation du mot de passe utilisateur
system.user.is_disable=L'utilisateur [{}] est désactivé
system.user.count.max=Échec de la création de l'utilisateur : quota maximum du locataire dépassé ({}) !
system.user.import.init_password=Le mot de passe initial ne peut pas être vide

# ========== 部门模块 1-002-004-000 ==========
system.dept.name.duplicate=Un département avec ce nom existe déjà
system.dept.parent.not_exits=Le département parent n'existe pas
system.dept.not_found=Le département n'existe pas
system.dept.exits_children=Impossible de supprimer, des sous-départements existent
system.dept.parent.error=Impossible de définir lui-même comme département parent
system.dept.not_enable=Le département ({}) n'est pas activé, sélection non autorisée
system.dept.parent.is_child=Impossible de définir un sous-département comme parent

# ========== 岗位模块 1-002-005-000 ==========
system.post.not_found=Le poste n'existe pas
system.post.not_enable=Le poste ({}) n'est pas activé, sélection non autorisée
system.post.name.duplicate=Un poste avec ce nom existe déjà
system.post.code.duplicate=Un poste avec cet identifiant existe déjà

# ========== 字典类型 1-002-006-000 ==========
system.dict.type.not_exists=Le type de dictionnaire n'existe pas
system.dict.type.not_enable=Le type de dictionnaire n'est pas activé, sélection non autorisée
system.dict.type.name.duplicate=Un type de dictionnaire avec ce nom existe déjà
system.dict.type.type.duplicate=Un type de dictionnaire avec ce type existe déjà
system.dict.type.has_children=Impossible de supprimer, le type de dictionnaire a encore des données enfants

# ========== 字典数据 1-002-007-000 ==========
system.dict.data.not_exists=Les données du dictionnaire n'existent pas
system.dict.data.not_enable=Les données du dictionnaire ({}) ne sont pas activées, sélection non autorisée
system.dict.data.value.duplicate=Des données du dictionnaire avec cette valeur existent déjà

# ========== 通知公告 1-002-008-000 ==========
system.notice.not_found=La notification n'existe pas

# ========== 短信渠道 1-002-011-000 ==========
system.sms.channel.not_exists=Le canal SMS n'existe pas
system.sms.channel.disable=Le canal SMS n'est pas activé, sélection non autorisée
system.sms.channel.has_children=Impossible de supprimer, le canal SMS a encore des modèles associés

# ========== 短信模板 1-002-012-000 ==========
system.sms.template.not_exists=Le modèle SMS n'existe pas
system.sms.template.code.duplicate=Un modèle SMS avec le code [{}] existe déjà
system.sms.template.api_error=Échec de l'appel du modèle API SMS, raison : {}
system.sms.template.api_audit_checking=Le modèle API SMS ne peut pas être utilisé, raison : en cours de révision
system.sms.template.api_audit_fail=Le modèle API SMS ne peut pas être utilisé, raison : rejeté, {}
system.sms.template.api_not_found=Le modèle API SMS ne peut pas être utilisé, raison : modèle introuvable

# ========== 短信发送 1-002-013-000 ==========
system.sms.send.mobile.not_exists=Le numéro de mobile n'existe pas
system.sms.send.template_param_miss=Paramètre de modèle manquant ({})
system.sms.send.template.not_exists=Le modèle SMS n'existe pas

# ========== 短信验证码 1-002-014-000 ==========
system.sms.code.not_found=Le code de vérification n'a pas été trouvé
system.sms.code.expired=Le code de vérification a expiré
system.sms.code.used=Le code de vérification a déjà été utilisé
system.sms.code.exceed_send_maximum_quantity_per_day=Nombre maximum de SMS envoyés par jour dépassé
system.sms.code.send_too_fast=Envoi de SMS trop fréquent

# ========== 租户信息 1-002-015-000 ==========
system.tenant.not_exists=Le locataire n'existe pas
system.tenant.disable=Le locataire [{}] est désactivé
system.tenant.expire=Le locataire [{}] a expiré
system.tenant.can_not_update_system=Impossible de modifier ou supprimer les locataires système !
system.tenant.name.duplicate=Un locataire avec le nom [{}] existe déjà
system.tenant.website.duplicate=Un locataire avec le domaine [{}] existe déjà

# ========== 租户套餐 1-002-016-000 ==========
system.tenant.package.not_exists=Le forfait du locataire n'existe pas
system.tenant.package.used=Le locataire utilise ce forfait. Veuillez attribuer un nouveau forfait avant de le supprimer
system.tenant.package.disable=Le forfait locataire [{}] est désactivé

# ========== 社交用户 1-002-018-000 ==========
system.social_user.auth_failure=Échec de l'autorisation sociale, raison : {}
system.social_user.not_found=Échec de l'autorisation sociale, utilisateur introuvable
system.social_client.weixin_mini_app.phone_code_error=Échec de l'obtention du numéro de téléphone
system.social_client.weixin_mini_app.qrcode_error=Échec de l'obtention du code QR de l'application
system.social_client.weixin_mini_app.subscribe_template_error=Échec de l'obtention du modèle d'abonnement de l'application
system.social_client.weixin_mini_app.subscribe_message_error=Échec de l'envoi du message d'abonnement de l'application
system.social_client.not_exists=Le client social n'existe pas
system.social_client.unique=La configuration du client social existe déjà

# ========== OAuth2 客户端 1-002-020-000 ==========
system.oauth2_client.not_exists=Le client OAuth2 n'existe pas
system.oauth2_client.exists=Le client OAuth2 existe déjà
system.oauth2_client.disable=Le client OAuth2 est désactivé
system.oauth2_client.authorized_grant_type_not_exists=Type d'autorisation non pris en charge
system.oauth2_client.scope_over=Portée de l'autorisation trop grande
system.oauth2_client.redirect_uri_not_match=redirect_uri invalide : {}
system.oauth2_client.client_secret_error=client_secret invalide : {}

# ========== 邮箱账号 1-002-023-000 ==========
system.mail_account.not_exists=Le compte email n'existe pas
system.mail_account.relate_template_exists=Impossible de supprimer, le compte email a des modèles associés

# ========== 邮件模版 1-002-024-000 ==========
system.mail_template.not_exists=Le modèle email n'existe pas
system.mail_template.code_exists=Le code de modèle email ({}) existe déjà

# ========== 邮件发送 1-002-025-000 ==========
system.mail_send.template_param_miss=Paramètre de modèle manquant ({})
system.mail_send.mail_not_exists=L'adresse email n'existe pas

# ========== 站内信模版 1-002-026-000 ==========
system.notify_template.not_exists=Le modèle de notification n'existe pas
system.notify_template.code_duplicate=Un modèle de notification avec le code [{}] existe déjà

# ========== 站内信发送 1-002-028-000 ==========
system.notify_send.template_param_miss=Paramètre de modèle manquant ({})
