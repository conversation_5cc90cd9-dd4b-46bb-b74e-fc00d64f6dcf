# ========== AUTH 模块 1-002-000-000 ==========
system.auth.login.bad_credentials=An<PERSON>du<PERSON> fehlge<PERSON>lagen, Benutzername oder Passwort ist falsch
system.auth.login.user_disabled=An<PERSON><PERSON><PERSON> fehlgeschlagen, Konto ist deaktiviert
system.auth.login.captcha_code_error=Captcha-Code ist falsch, Grund: {}
system.auth.third_login.not_bind=Konto nicht verknüpft, Verknüpfung erforderlich
system.auth.mobile.not_exists=Handynummer existiert nicht
system.auth.register.captcha_code_error=Captcha-Code ist falsch, Grund: {}

# ========== 菜单模块 1-002-001-000 ==========
system.menu.name.duplicate=Ein Menü mit diesem Namen existiert bereits
system.menu.parent.not_exists=Übergeordnetes Menü existiert nicht
system.menu.parent.error=Kann sich nicht selbst als übergeordnetes Menü festlegen
system.menu.not_exists=Menü existiert nicht
system.menu.exists_children=Kann nicht gelöscht werden, untergeordnete Menüs existieren
system.menu.parent.not_dir_or_menu=Der Typ des übergeordneten Menüs muss ein Verzeichnis oder ein Menü sein

# ========== 角色模块 1-002-002-000 ==========
system.role.not_exists=Rolle existiert nicht
system.role.name.duplicate=Eine Rolle mit dem Namen [{}] existiert bereits
system.role.code.duplicate=Eine Rolle mit dem Code [{}] existiert bereits
system.role.can_not_update_system_type=Operationen auf systemeigenen Rollen sind nicht zulässig
system.role.is_disable=Die Rolle [{}] ist deaktiviert
system.role.admin_code_error=Der Code [{}] kann nicht verwendet werden

# ========== 用户模块 1-002-003-000 ==========
system.user.username.exists=Benutzername existiert bereits
system.user.mobile.exists=Handynummer existiert bereits
system.user.email.exists=E-Mail existiert bereits
system.user.not_exists=Benutzer existiert nicht
system.user.import_list_is_empty=Importierte Benutzerdaten dürfen nicht leer sein!
system.user.password.failed=Benutzerpasswortprüfung fehlgeschlagen
system.user.is_disable=Benutzer [{}] ist deaktiviert
system.user.count.max=Benutzererstellung fehlgeschlagen: Maximales Mieterlimit überschritten ({})!
system.user.import.init_password=Initiales Passwort darf nicht leer sein

# ========== 部门模块 1-002-004-000 ==========
system.dept.name.duplicate=Eine Abteilung mit diesem Namen existiert bereits
system.dept.parent.not_exits=Übergeordnete Abteilung existiert nicht
system.dept.not_found=Die Abteilung existiert nicht
system.dept.exits_children=Kann nicht gelöscht werden, untergeordnete Abteilungen existieren
system.dept.parent.error=Kann sich nicht selbst als übergeordnete Abteilung festlegen
system.dept.not_enable=Abteilung ({}) ist nicht aktiviert, Auswahl nicht zulässig
system.dept.parent.is_child=Kann eine untergeordnete Abteilung nicht als übergeordnete festlegen

# ========== 岗位模块 1-002-005-000 ==========
system.post.not_found=Position existiert nicht
system.post.not_enable=Position ({}) ist nicht aktiviert, Auswahl nicht zulässig
system.post.name.duplicate=Eine Position mit diesem Namen existiert bereits
system.post.code.duplicate=Eine Position mit diesem Code existiert bereits

# ========== 字典类型 1-002-006-000 ==========
system.dict.type.not_exists=Der Wörterbuchtyp existiert nicht
system.dict.type.not_enable=Der Wörterbuchtyp ist nicht aktiviert, Auswahl nicht zulässig
system.dict.type.name.duplicate=Ein Wörterbuchtyp mit diesem Namen existiert bereits
system.dict.type.type.duplicate=Ein Wörterbuchtyp mit diesem Typ existiert bereits
system.dict.type.has_children=Kann nicht gelöscht werden, der Wörterbuchtyp hat noch Daten

# ========== 字典数据 1-002-007-000 ==========
system.dict.data.not_exists=Die Wörterbuchdaten existieren nicht
system.dict.data.not_enable=Die Wörterbuchdaten ({}) sind nicht aktiviert, Auswahl nicht zulässig
system.dict.data.value.duplicate=Ein Eintrag mit diesem Wert existiert bereits

# ========== 通知公告 1-002-008-000 ==========
system.notice.not_found=Die Benachrichtigung existiert nicht

# ========== 短信渠道 1-002-011-000 ==========
system.sms.channel.not_exists=SMS-Kanal existiert nicht
system.sms.channel.disable=SMS-Kanal ist nicht aktiviert, Auswahl nicht zulässig
system.sms.channel.has_children=Kann nicht gelöscht werden, der SMS-Kanal hat noch zugehörige Vorlagen

# ========== 短信模板 1-002-012-000 ==========
system.sms.template.not_exists=Die SMS-Vorlage existiert nicht
system.sms.template.code.duplicate=Eine SMS-Vorlage mit dem Code [{}] existiert bereits
system.sms.template.api_error=Aufruf der SMS-API-Vorlage fehlgeschlagen, Grund: {}
system.sms.template.api_audit_checking=Die SMS-API-Vorlage kann nicht verwendet werden, Grund: in Prüfung
system.sms.template.api_audit_fail=Die SMS-API-Vorlage kann nicht verwendet werden, Grund: Prüfung nicht bestanden, {}
system.sms.template.api_not_found=Die SMS-API-Vorlage kann nicht verwendet werden, Grund: Vorlage nicht gefunden

# ========== 短信发送 1-002-013-000 ==========
system.sms.send.mobile.not_exists=Handynummer existiert nicht
system.sms.send.template_param_miss=Vorlagenparameter fehlen ({})
system.sms.send.template.not_exists=Die SMS-Vorlage existiert nicht

# ========== 短信验证码 1-002-014-000 ==========
system.sms.code.not_found=Verifizierungscode nicht gefunden
system.sms.code.expired=Verifizierungscode ist abgelaufen
system.sms.code.used=Verifizierungscode wurde bereits verwendet
system.sms.code.exceed_send_maximum_quantity_per_day=Maximale Anzahl an SMS pro Tag überschritten
system.sms.code.send_too_fast=SMS wird zu häufig gesendet

# ========== 租户信息 1-002-015-000 ==========
system.tenant.not_exists=Mieter existiert nicht
system.tenant.disable=Mieter [{}] ist deaktiviert
system.tenant.expire=Mieter [{}] ist abgelaufen
system.tenant.can_not_update_system=Systemmieter können nicht geändert oder gelöscht werden!
system.tenant.name.duplicate=Ein Mieter mit dem Namen [{}] existiert bereits
system.tenant.website.duplicate=Ein Mieter mit der Domain [{}] existiert bereits

# ========== 租户套餐 1-002-016-000 ==========
system.tenant.package.not_exists=Mietpaket existiert nicht
system.tenant.package.used=Das Mietpaket wird vom Mieter verwendet. Bitte vor dem Löschen ein neues Paket zuweisen
system.tenant.package.disable=Das Mietpaket [{}] ist deaktiviert

# ========== 社交用户 1-002-018-000 ==========
system.social_user.auth_failure=Soziale Autorisierung fehlgeschlagen, Grund: {}
system.social_user.not_found=Soziale Autorisierung fehlgeschlagen, Benutzer nicht gefunden
system.social_client.weixin_mini_app.phone_code_error=Handynummer konnte nicht abgerufen werden
system.social_client.weixin_mini_app.qrcode_error=QR-Code der Mini-App konnte nicht abgerufen werden
system.social_client.weixin_mini_app.subscribe_template_error=Vorlage für Mini-App-Abonnement konnte nicht abgerufen werden
system.social_client.weixin_mini_app.subscribe_message_error=Nachricht des Mini-App-Abonnements konnte nicht gesendet werden
system.social_client.not_exists=Sozialer Client existiert nicht
system.social_client.unique=Die Konfiguration des sozialen Clients existiert bereits

# ========== OAuth2 客户端 1-002-020-000 ==========
system.oauth2_client.not_exists=OAuth2-Client existiert nicht
system.oauth2_client.exists=OAuth2-Client existiert bereits
system.oauth2_client.disable=OAuth2-Client ist deaktiviert
system.oauth2_client.authorized_grant_type_not_exists=Autorisierungstyp wird nicht unterstützt
system.oauth2_client.scope_over=Autorisierungsbereich zu groß
system.oauth2_client.redirect_uri_not_match=Ungültige redirect_uri: {}
system.oauth2_client.client_secret_error=Ungültige client_secret: {}

# ========== 邮箱账号 1-002-023-000 ==========
system.mail_account.not_exists=E-Mail-Konto existiert nicht
system.mail_account.relate_template_exists=Kann nicht gelöscht werden, da Vorlagen mit dem Konto verknüpft sind

# ========== 邮件模版 1-002-024-000 ==========
system.mail_template.not_exists=Die E-Mail-Vorlage existiert nicht
system.mail_template.code_exists=Der Code der E-Mail-Vorlage ({}) existiert bereits

# ========== 邮件发送 1-002-025-000 ==========
system.mail_send.template_param_miss=Vorlagenparameter fehlen ({})
system.mail_send.mail_not_exists=E-Mail-Adresse existiert nicht

# ========== 站内信模版 1-002-026-000 ==========
system.notify_template.not_exists=Die Benachrichtigungsvorlage existiert nicht
system.notify_template.code_duplicate=Eine Benachrichtigungsvorlage mit dem Code [{}] existiert bereits

# ========== 站内信发送 1-002-028-000 ==========
system.notify_send.template_param_miss=Vorlagenparameter fehlen ({})
