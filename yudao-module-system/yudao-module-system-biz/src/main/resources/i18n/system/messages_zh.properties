# AUTH 模块
system.auth.login.bad_credentials=登录失败，账号密码不正确
system.auth.login.user_disabled=登录失败，账号被禁用
system.auth.login.captcha_code_error=验证码不正确，原因：{}
system.auth.third_login.not_bind=未绑定账号，需要进行绑定
system.auth.mobile.not_exists=手机号不存在
system.auth.register.captcha_code_error=验证码不正确，原因：{}

# 菜单模块
system.menu.name.duplicate=已经存在该名字的菜单
system.menu.parent.not_exists=父菜单不存在
system.menu.parent.error=不能设置自己为父菜单
system.menu.not_exists=菜单不存在
system.menu.exists_children=存在子菜单，无法删除
system.menu.parent.not_dir_or_menu=父菜单的类型必须是目录或者菜单

# 角色模块
system.role.not_exists=角色不存在
system.role.name.duplicate=已经存在名为【{}】的角色
system.role.code.duplicate=已经存在标识为【{}】的角色
system.role.can_not_update_system_type=不能操作类型为系统内置的角色
system.role.is_disable=名字为【{}】的角色已被禁用
system.role.admin_code_error=标识【{}】不能使用

# 用户模块
system.user.username.exists=用户账号已经存在
system.user.mobile.exists=手机号已经存在
system.user.email.exists=邮箱已经存在
system.user.not_exists=用户不存在
system.user.import_list_is_empty=导入用户数据不能为空！
system.user.password.failed=用户密码校验失败
system.user.is_disable=名字为【{}】的用户已被禁用
system.user.count.max=创建用户失败，原因：超过租户最大租户配额({})！
system.user.import.init_password=初始密码不能为空

# 部门模块
system.dept.name.duplicate=已经存在该名字的部门
system.dept.parent.not_exits=父级部门不存在
system.dept.not_found=当前部门不存在
system.dept.exits_children=存在子部门，无法删除
system.dept.parent.error=不能设置自己为父部门
system.dept.not_enable=部门({})不处于开启状态，不允许选择
system.dept.parent.is_child=不能设置自己的子部门为父部门

# 岗位模块
system.post.not_found=当前岗位不存在
system.post.not_enable=岗位({}) 不处于开启状态，不允许选择
system.post.name.duplicate=已经存在该名字的岗位
system.post.code.duplicate=已经存在该标识的岗位

# 字典类型
system.dict.type.not_exists=当前字典类型不存在
system.dict.type.not_enable=字典类型不处于开启状态，不允许选择
system.dict.type.name.duplicate=已经存在该名字的字典类型
system.dict.type.type.duplicate=已经存在该类型的字典类型
system.dict.type.has_children=无法删除，该字典类型还有字典数据

# 字典数据
system.dict.data.not_exists=当前字典数据不存在
system.dict.data.not_enable=字典数据({})不处于开启状态，不允许选择
system.dict.data.value.duplicate=已经存在该值的字典数据

# 通知公告
system.notice.not_found=当前通知公告不存在

# 短信渠道
system.sms.channel.not_exists=短信渠道不存在
system.sms.channel.disable=短信渠道不处于开启状态，不允许选择
system.sms.channel.has_children=无法删除，该短信渠道还有短信模板

# 短信模板
system.sms.template.not_exists=短信模板不存在
system.sms.template.code.duplicate=已经存在编码为【{}】的短信模板
system.sms.template.api_error=短信 API 模板调用失败，原因是：{}
system.sms.template.api_audit_checking=短信 API 模版无法使用，原因：审批中
system.sms.template.api_audit_fail=短信 API 模版无法使用，原因：审批不通过，{}
system.sms.template.api_not_found=短信 API 模版无法使用，原因：模版不存在

# 短信发送
system.sms.send.mobile.not_exists=手机号不存在
system.sms.send.template_param_miss=模板参数({})缺失
system.sms.send.template.not_exists=短信模板不存在

# 短信验证码
system.sms.code.not_found=验证码不存在
system.sms.code.expired=验证码已过期
system.sms.code.used=验证码已使用
system.sms.code.exceed_send_maximum_quantity_per_day=超过每日短信发送数量
system.sms.code.send_too_fast=短信发送过于频繁

# 租户信息
system.tenant.not_exists=租户不存在
system.tenant.disable=名字为【{}】的租户已被禁用
system.tenant.expire=名字为【{}】的租户已过期
system.tenant.can_not_update_system=系统租户不能进行修改、删除等操作！
system.tenant.name.duplicate=名字为【{}】的租户已存在
system.tenant.website.duplicate=域名为【{}】的租户已存在

# 租户套餐
system.tenant.package.not_exists=租户套餐不存在
system.tenant.package.used=租户正在使用该套餐，请给租户重新设置套餐后再尝试删除
system.tenant.package.disable=名字为【{}】的租户套餐已被禁用

# 社交用户
system.social_user.auth_failure=社交授权失败，原因是：{}
system.social_user.not_found=社交授权失败，找不到对应的用户
system.social_client.weixin_mini_app.phone_code_error=获得手机号失败
system.social_client.weixin_mini_app.qrcode_error=获得小程序码失败
system.social_client.weixin_mini_app.subscribe_template_error=获得小程序订阅消息模版失败
system.social_client.weixin_mini_app.subscribe_message_error=发送小程序订阅消息失败
system.social_client.not_exists=社交客户端不存在
system.social_client.unique=社交客户端已存在配置

# OAuth2 客户端
system.oauth2_client.not_exists=OAuth2 客户端不存在
system.oauth2_client.exists=OAuth2 客户端编号已存在
system.oauth2_client.disable=OAuth2 客户端已禁用
system.oauth2_client.authorized_grant_type_not_exists=不支持该授权类型
system.oauth2_client.scope_over=授权范围过大
system.oauth2_client.redirect_uri_not_match=无效 redirect_uri: {}
system.oauth2_client.client_secret_error=无效 client_secret: {}

# OAuth2 授权
system.oauth2_grant.client_id_mismatch=client_id 不匹配
system.oauth2_grant.redirect_uri_mismatch=redirect_uri 不匹配
system.oauth2_grant.state_mismatch=state 不匹配

# OAuth2 code
system.oauth2_code.not_exists=code 不存在
system.oauth2_code.expire=code 已过期

# 邮箱账号
system.mail_account.not_exists=邮箱账号不存在
system.mail_account.relate_template_exists=无法删除，该邮箱账号还有邮件模板

# 邮件模板
system.mail_template.not_exists=邮件模版不存在
system.mail_template.code_exists=邮件模版 code({}) 已存在

# 邮件发送
system.mail_send.template_param_miss=模板参数({})缺失
system.mail_send.mail_not_exists=邮箱不存在

# 站内信模板
system.notify_template.not_exists=站内信模版不存在
system.notify_template.code_duplicate=已经存在编码为【{}】的站内信模板

# 站内信发送
system.notify_send.template_param_miss=模板参数({})缺失

# 邮件验证码
system.mail.code.not_found=验证码不存在
system.mail.code.expired=验证码已过期
system.mail.code.used=验证码已使用
system.mail.code.exceed_send_maximum_quantity_per_day=超过每日邮件发送数量
system.mail.code.send_too_fast=邮件发送过于频繁
