# ========== AUTH 模块 1-002-000-000 ==========
system.auth.login.bad_credentials=Inicio de sesión fallido, nombre de usuario o contraseña incorrectos
system.auth.login.user_disabled=Inicio de sesión fallido, cuenta deshabilitada
system.auth.login.captcha_code_error=Código captcha incorrecto, razón: {}
system.auth.third_login.not_bind=Cuenta no vinculada, necesita vinculación
system.auth.mobile.not_exists=El número de teléfono no existe
system.auth.register.captcha_code_error=Código captcha incorrecto, razón: {}

# ========== 菜单模块 1-002-001-000 ==========
system.menu.name.duplicate=Ya existe un menú con este nombre
system.menu.parent.not_exists=El menú principal no existe
system.menu.parent.error=No puede establecerse a sí mismo como menú principal
system.menu.not_exists=El menú no existe
system.menu.exists_children=No se puede eliminar, existen submenús
system.menu.parent.not_dir_or_menu=El tipo del menú principal debe ser directorio o menú

# ========== 角色模块 1-002-002-000 ==========
system.role.not_exists=El rol no existe
system.role.name.duplicate=Ya existe un rol con el nombre [{}]
system.role.code.duplicate=Ya existe un rol con el código [{}]
system.role.can_not_update_system_type=No se pueden realizar operaciones en roles internos del sistema
system.role.is_disable=El rol [{}] está deshabilitado
system.role.admin_code_error=El código [{}] no se puede usar

# ========== 用户模块 1-002-003-000 ==========
system.user.username.exists=El nombre de usuario ya existe
system.user.mobile.exists=El número de teléfono ya existe
system.user.email.exists=El correo electrónico ya existe
system.user.not_exists=El usuario no existe
system.user.import_list_is_empty=¡Los datos de usuarios importados no pueden estar vacíos!
system.user.password.failed=La validación de la contraseña del usuario falló
system.user.is_disable=El usuario [{}] está deshabilitado
system.user.count.max=Error al crear el usuario: ¡se excedió el límite máximo de usuarios del inquilino ({})!
system.user.import.init_password=La contraseña inicial no puede estar vacía

# ========== 部门模块 1-002-004-000 ==========
system.dept.name.duplicate=Ya existe un departamento con este nombre
system.dept.parent.not_exits=El departamento principal no existe
system.dept.not_found=El departamento actual no existe
system.dept.exits_children=No se puede eliminar, existen subdepartamentos
system.dept.parent.error=No puede establecerse a sí mismo como departamento principal
system.dept.not_enable=El departamento ({}) no está habilitado, selección no permitida
system.dept.parent.is_child=No se puede establecer un subdepartamento como principal

# ========== 岗位模块 1-002-005-000 ==========
system.post.not_found=El puesto actual no existe
system.post.not_enable=El puesto ({}) no está habilitado, selección no permitida
system.post.name.duplicate=Ya existe un puesto con este nombre
system.post.code.duplicate=Ya existe un puesto con este código

# ========== 字典类型 1-002-006-000 ==========
system.dict.type.not_exists=El tipo de diccionario no existe
system.dict.type.not_enable=El tipo de diccionario no está habilitado, selección no permitida
system.dict.type.name.duplicate=Ya existe un tipo de diccionario con este nombre
system.dict.type.type.duplicate=Ya existe un tipo de diccionario con este tipo
system.dict.type.has_children=No se puede eliminar, este tipo de diccionario tiene datos

# ========== 字典数据 1-002-007-000 ==========
system.dict.data.not_exists=Los datos del diccionario no existen
system.dict.data.not_enable=Los datos del diccionario ({}) no están habilitados, selección no permitida
system.dict.data.value.duplicate=Ya existe un dato con este valor

# ========== 通知公告 1-002-008-000 ==========
system.notice.not_found=El aviso actual no existe

# ========== 短信渠道 1-002-011-000 ==========
system.sms.channel.not_exists=El canal SMS no existe
system.sms.channel.disable=El canal SMS no está habilitado, selección no permitida
system.sms.channel.has_children=No se puede eliminar, el canal SMS tiene plantillas asociadas

# ========== 短信模板 1-002-012-000 ==========
system.sms.template.not_exists=La plantilla SMS no existe
system.sms.template.code.duplicate=Ya existe una plantilla SMS con el código [{}]
system.sms.template.api_error=Falló la llamada a la API de la plantilla SMS, razón: {}
system.sms.template.api_audit_checking=La plantilla API SMS no se puede usar, razón: en revisión
system.sms.template.api_audit_fail=La plantilla API SMS no se puede usar, razón: revisión no aprobada, {}
system.sms.template.api_not_found=La plantilla API SMS no se puede usar, razón: plantilla no encontrada

# ========== 短信发送 1-002-013-000 ==========
system.sms.send.mobile.not_exists=El número de teléfono no existe
system.sms.send.template_param_miss=Faltan parámetros de la plantilla ({})
system.sms.send.template.not_exists=La plantilla SMS no existe

# ========== 短信验证码 1-002-014-000 ==========
system.sms.code.not_found=El código de verificación no se encontró
system.sms.code.expired=El código de verificación ha expirado
system.sms.code.used=El código de verificación ya se ha usado
system.sms.code.exceed_send_maximum_quantity_per_day=Se ha excedido la cantidad máxima diaria de SMS enviados
system.sms.code.send_too_fast=Los SMS se están enviando con demasiada frecuencia

# ========== 租户信息 1-002-015-000 ==========
system.tenant.not_exists=El inquilino no existe
system.tenant.disable=El inquilino [{}] está deshabilitado
system.tenant.expire=El inquilino [{}] ha expirado
system.tenant.can_not_update_system=No se pueden modificar o eliminar inquilinos del sistema
system.tenant.name.duplicate=Ya existe un inquilino con el nombre [{}]
system.tenant.website.duplicate=Ya existe un inquilino con el dominio [{}]

# ========== 租户套餐 1-002-016-000 ==========
system.tenant.package.not_exists=El paquete del inquilino no existe
system.tenant.package.used=El paquete del inquilino está en uso, asigne un nuevo paquete antes de eliminar
system.tenant.package.disable=El paquete del inquilino [{}] está deshabilitado

# ========== 社交用户 1-002-018-000 ==========
system.social_user.auth_failure=Falló la autenticación social, razón: {}
system.social_user.not_found=Falló la autenticación social, no se encontró el usuario
system.social_client.weixin_mini_app.phone_code_error=No se pudo obtener el número de teléfono
system.social_client.weixin_mini_app.qrcode_error=No se pudo obtener el código QR de la mini app
system.social_client.weixin_mini_app.subscribe_template_error=No se pudo obtener la plantilla de suscripción de la mini app
system.social_client.weixin_mini_app.subscribe_message_error=Falló el envío del mensaje de suscripción de la mini app
system.social_client.not_exists=El cliente social no existe
system.social_client.unique=La configuración del cliente social ya existe

# ========== OAuth2 客户端 1-002-020-000 ==========
system.oauth2_client.not_exists=El cliente OAuth2 no existe
system.oauth2_client.exists=El cliente OAuth2 ya existe
system.oauth2_client.disable=El cliente OAuth2 está deshabilitado
system.oauth2_client.authorized_grant_type_not_exists=Tipo de autorización no compatible
system.oauth2_client.scope_over=El alcance de autorización es demasiado amplio
system.oauth2_client.redirect_uri_not_match=URI de redirección inválido: {}
system.oauth2_client.client_secret_error=Client_secret inválido: {}

# ========== 邮箱账号 1-002-023-000 ==========
system.mail_account.not_exists=La cuenta de correo electrónico no existe
system.mail_account.relate_template_exists=No se puede eliminar, hay plantillas asociadas con esta cuenta

# ========== 邮件模版 1-002-024-000 ==========
system.mail_template.not_exists=La plantilla de correo electrónico no existe
system.mail_template.code_exists=El código de la plantilla de correo electrónico ({}) ya existe

# ========== 邮件发送 1-002-025-000 ==========
system.mail_send.template_param_miss=Faltan parámetros de la plantilla ({})
system.mail_send.mail_not_exists=El correo electrónico no existe

# ========== 站内信模版 1-002-026-000 ==========
system.notify_template.not_exists=La plantilla de mensaje interno no existe
system.notify_template.code_duplicate=Ya existe una plantilla con el código [{}]

# ========== 站内信发送 1-002-028-000 ==========
system.notify_send.template_param_miss=Faltan parámetros de la plantilla ({})
