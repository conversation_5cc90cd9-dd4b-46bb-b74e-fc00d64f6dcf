# AUTH Module
system.auth.login.bad_credentials=<PERSON>gin failed, incorrect username or password
system.auth.login.user_disabled=Login failed, account is disabled
system.auth.login.captcha_code_error=Incorrect captcha, reason: {}
system.auth.third_login.not_bind=Account not bound, binding required
system.auth.mobile.not_exists=Mobile number does not exist
system.auth.register.captcha_code_error=Incorrect captcha, reason: {}

# Menu Module
system.menu.name.duplicate=Menu with this name already exists
system.menu.parent.not_exists=Parent menu does not exist
system.menu.parent.error=Cannot set itself as the parent menu
system.menu.not_exists=Menu does not exist
system.menu.exists_children=Cannot delete, child menus exist
system.menu.parent.not_dir_or_menu=Parent menu type must be directory or menu

# Role Module
system.role.not_exists=Role does not exist
system.role.name.duplicate=A role with the name 【{}】 already exists
system.role.code.duplicate=A role with the identifier 【{0}】 already exists
system.role.can_not_update_system_type=Cannot operate on roles of system type
system.role.is_disable=The role 【{}】 is disabled
system.role.admin_code_error=Identifier 【{}】 cannot be used

# User Module
system.user.username.exists=Username already exists
system.user.mobile.exists=Mobile number already exists
system.user.email.exists=Email already exists
system.user.not_exists=User does not exist
system.user.import_list_is_empty=Imported user data cannot be empty!
system.user.password.failed=User password validation failed
system.user.is_disable=The user 【{}】 is disabled
system.user.count.max=Failed to create user: exceeded maximum tenant quota ({})!
system.user.import.init_password=Initial password cannot be empty

# Department Module
system.dept.name.duplicate=Department with this name already exists
system.dept.parent.not_exits=Parent department does not exist
system.dept.not_found=The department does not exist
system.dept.exits_children=Cannot delete, child departments exist
system.dept.parent.error=Cannot set itself as the parent department
system.dept.not_enable=Department ({}) is not enabled, selection not allowed
system.dept.parent.is_child=Cannot set a child department as parent

# Position Module
system.post.not_found=The position does not exist
system.post.not_enable=Position ({}) is not enabled, selection not allowed
system.post.name.duplicate=Position with this name already exists
system.post.code.duplicate=Position with this identifier already exists

# Dictionary Type
system.dict.type.not_exists=Dictionary type does not exist
system.dict.type.not_enable=Dictionary type is not enabled, selection not allowed
system.dict.type.name.duplicate=Dictionary type with this name already exists
system.dict.type.type.duplicate=Dictionary type with this type already exists
system.dict.type.has_children=Cannot delete, the dictionary type has children

# Dictionary Data
system.dict.data.not_exists=Dictionary data does not exist
system.dict.data.not_enable=Dictionary data ({}) is not enabled, selection not allowed
system.dict.data.value.duplicate=Dictionary data with this value already exists

# Notification
system.notice.not_found=Notification does not exist

# SMS Channel
system.sms.channel.not_exists=SMS channel does not exist
system.sms.channel.disable=SMS channel is not enabled, selection not allowed
system.sms.channel.has_children=Cannot delete, the SMS channel has associated templates

# SMS Template
system.sms.template.not_exists=SMS template does not exist
system.sms.template.code.duplicate=An SMS template with the code 【{}】 already exists
system.sms.template.api_error=SMS API template call failed, reason: {}
system.sms.template.api_audit_checking=SMS API template cannot be used, reason: under review
system.sms.template.api_audit_fail=SMS API template cannot be used, reason: rejected, {}
system.sms.template.api_not_found=SMS API template cannot be used, reason: template not found

# SMS Sending
system.sms.send.mobile.not_exists=Mobile number does not exist
system.sms.send.template_param_miss=Missing template parameter ({})
system.sms.send.template.not_exists=SMS template does not exist

# SMS Verification Code
system.sms.code.not_found=Verification code not found
system.sms.code.expired=Verification code has expired
system.sms.code.used=Verification code already used
system.sms.code.exceed_send_maximum_quantity_per_day=Exceeded maximum daily SMS sending limit
system.sms.code.send_too_fast=SMS sending too frequent

# Tenant Information
system.tenant.not_exists=Tenant does not exist
system.tenant.disable=The tenant 【{}】 is disabled
system.tenant.expire=The tenant 【{}】 has expired
system.tenant.can_not_update_system=System tenants cannot be modified or deleted!
system.tenant.name.duplicate=A tenant with the name 【{}】 already exists
system.tenant.website.duplicate=A tenant with the domain 【{}】 already exists

# Tenant Package
system.tenant.package.not_exists=Tenant package does not exist
system.tenant.package.used=Tenant is using this package. Please assign a new package before deleting
system.tenant.package.disable=The tenant package 【{}】 is disabled

# Social User
system.social_user.auth_failure=Social authorization failed, reason: {}
system.social_user.not_found=Social authorization failed, user not found
system.social_client.weixin_mini_app.phone_code_error=Failed to retrieve phone number
system.social_client.weixin_mini_app.qrcode_error=Failed to retrieve mini app QR code
system.social_client.weixin_mini_app.subscribe_template_error=Failed to retrieve mini app subscription template
system.social_client.weixin_mini_app.subscribe_message_error=Failed to send mini app subscription message
system.social_client.not_exists=Social client does not exist
system.social_client.unique=Social client configuration already exists

# OAuth2 Client
system.oauth2_client.not_exists=OAuth2 client does not exist
system.oauth2_client.exists=OAuth2 client identifier already exists
system.oauth2_client.disable=OAuth2 client is disabled
system.oauth2_client.authorized_grant_type_not_exists=Unsupported authorization type
system.oauth2_client.scope_over=Authorization scope is too large
system.oauth2_client.redirect_uri_not_match=Invalid redirect_uri: {}
system.oauth2_client.client_secret_error=Invalid client_secret: {}

# OAuth2 Grant
system.oauth2_grant.client_id_mismatch=client_id mismatch
system.oauth2_grant.redirect_uri_mismatch=redirect_uri mismatch
system.oauth2_grant.state_mismatch=state mismatch

# OAuth2 Code
system.oauth2_code.not_exists=code does not exist
system.oauth2_code.expire=code has expired

# Mail Account
system.mail_account.not_exists=Mail account does not exist
system.mail_account.relate_template_exists=Cannot delete, mail account has associated templates

# Mail Template
system.mail_template.not_exists=Mail template does not exist
system.mail_template.code_exists=Mail template code ({}) already exists

# Mail Sending
system.mail_send.template_param_miss=Missing template parameter ({})
system.mail_send.mail_not_exists=Mail address does not exist

# Notification Template
system.notify_template.not_exists=Notification template does not exist
system.notify_template.code_duplicate=A notification template with the code 【{}】 already exists

# Notification Sending
system.notify_send.template_param_miss=Missing template parameter ({})
