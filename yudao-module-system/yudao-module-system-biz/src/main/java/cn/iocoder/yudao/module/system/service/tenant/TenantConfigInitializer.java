package cn.iocoder.yudao.module.system.service.tenant;

import cn.iocoder.yudao.framework.tenant.config.TenantConfig;
import cn.iocoder.yudao.framework.tenant.core.context.TenantConfigManager;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 租户配置初始化
 * @author: DingXiao
 * @create: 2024-11-19 12:58
 **/
@Component
@Slf4j
public class TenantConfigInitializer {

    @Resource
    private TenantService tenantService;

    @PostConstruct
    public void initTenantConfigs() {
        // 从数据库加载所有租户配置
        List<TenantDO> tenantList = tenantService.getTenantList();
        for (TenantDO tenantDO : tenantList) {
            if(null!=tenantDO.getConfig()){
                TenantConfig config = tenantDO.getConfig();
                config.setType(tenantDO.getType());
                TenantConfigManager.initTenantConfig(tenantDO.getId(), config);
            }
        }
        log.info("加载租户配置完毕。");
    }
}
