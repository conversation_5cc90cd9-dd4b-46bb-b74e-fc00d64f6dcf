package cn.iocoder.yudao.module.system.service.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.tenant.config.TenantConfig;
import cn.iocoder.yudao.framework.tenant.core.context.TenantConfigManager;
import cn.iocoder.yudao.module.system.dal.dataobject.mail.MailAccountDO;
import cn.iocoder.yudao.module.system.dal.dataobject.mail.MailTemplateDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.mq.message.mail.MailSendMessage;
import cn.iocoder.yudao.module.system.mq.producer.mail.MailProducer;
import cn.iocoder.yudao.module.system.service.member.MemberService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants.UNSUBSCRIBE_TOKEN;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;

/**
 * 邮箱发送 Service 实现类
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Service
@Validated
@Slf4j
public class MailSendServiceImpl implements MailSendService {

    public static final Integer EXPIRED_TIME = 60 * 60 * 24 * 7; //取消订阅token过期时间 7天

    @Resource
    private AdminUserService adminUserService;
    @Resource
    private MemberService memberService;

    @Resource
    private MailAccountService mailAccountService;
    @Resource
    private MailTemplateService mailTemplateService;

    @Resource
    private MailLogService mailLogService;
    @Resource
    private MailProducer mailProducer;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Override
    public Long sendSingleMailToAdmin(String mail, Long userId,
                                      String templateCode, Map<String, Object> templateParams) {
        // 如果 mail 为空，则加载用户编号对应的邮箱
        if (StrUtil.isEmpty(mail)) {
            AdminUserDO user = adminUserService.getUser(userId);
            if (user != null) {
                mail = user.getEmail();
            }
        }
        // 执行发送
        return sendSingleMail(mail, userId, UserTypeEnum.ADMIN.getValue(), templateCode, templateParams);
    }

    @Override
    public Long sendSingleMailToMember(String mail, Long userId,
                                       String templateCode, Map<String, Object> templateParams) {
        // 如果 mail 为空，则加载用户编号对应的邮箱
        if (StrUtil.isEmpty(mail)) {
            mail = memberService.getMemberUserEmail(userId);
        }
        // 执行发送
        return sendSingleMail(mail, userId, UserTypeEnum.MEMBER.getValue(), templateCode, templateParams);
    }

    @Override
    public Long sendSingleMail(String mail, Long userId, Integer userType,
                               String templateCode, Map<String, Object> templateParams) {
        // 校验邮箱模版是否合法
        MailTemplateDO template = validateMailTemplate(templateCode);
        // 校验邮箱账号是否合法
        MailAccountDO account = validateMailAccount(template.getAccountId());

        // 校验邮箱是否存在
        mail = validateMail(mail);
        // 通用参数赋值 ding
        assignmentParams(template,templateParams,mail);

        validateTemplateParams(template, templateParams);

        // 创建发送日志。如果模板被禁用，则不发送短信，只记录日志
        Boolean isSend = CommonStatusEnum.ENABLE.getStatus().equals(template.getStatus());
        String title = mailTemplateService.formatMailTemplateContent(template.getTitle(), templateParams);
        String content = mailTemplateService.formatMailTemplateContent(template.getContent(), templateParams);
        Long sendLogId = mailLogService.createMailLog(userId, userType, mail,
                account, template, content, templateParams, isSend);
        // 发送 MQ 消息，异步执行发送短信
        if (isSend) {
            mailProducer.sendMailSendMessage(sendLogId, mail, account.getId(),
                    template.getNickname(), title, content);
        }
        return sendLogId;
    }


    @Override
    public void doSendMail(MailSendMessage message) {
        // 1. 创建发送账号
        MailAccountDO account = validateMailAccount(message.getAccountId());
        MailAccount mailAccount  = buildMailAccount(account, message.getNickname());
        // 2. 发送邮件
        try {
            String messageId = MailUtil.send(mailAccount, message.getMail(),
                    message.getTitle(), message.getContent(), true);
            // 3. 更新结果（成功）
            mailLogService.updateMailSendResult(message.getLogId(), messageId, null);
        } catch (Exception e) {
            // 3. 更新结果（异常）
            mailLogService.updateMailSendResult(message.getLogId(), null, e);
        }
    }

    private MailAccount buildMailAccount(MailAccountDO account, String nickname) {
        String from = StrUtil.isNotEmpty(nickname) ? nickname + " <" + account.getMail() + ">" : account.getMail();
        return new MailAccount().setFrom(from).setAuth(true)
                .setUser(account.getUsername()).setPass(account.getPassword())
                .setHost(account.getHost()).setPort(account.getPort())
                .setSslEnable(account.getSslEnable()).setStarttlsEnable(account.getStarttlsEnable());
    }

    @VisibleForTesting
    MailTemplateDO validateMailTemplate(String templateCode) {
        // 获得邮件模板。考虑到效率，从缓存中获取
        MailTemplateDO template = mailTemplateService.getMailTemplateByCodeFromCache(templateCode);
        // 邮件模板不存在
        if (template == null) {
            throw exception(MAIL_TEMPLATE_NOT_EXISTS);
        }
        return template;
    }

    @VisibleForTesting
    MailAccountDO validateMailAccount(Long accountId) {
        // 获得邮箱账号。考虑到效率，从缓存中获取
        MailAccountDO account = mailAccountService.getMailAccountFromCache(accountId);
        // 邮箱账号不存在
        if (account == null) {
            throw exception(MAIL_ACCOUNT_NOT_EXISTS);
        }
        return account;
    }

    @VisibleForTesting
    String validateMail(String mail) {
        if (StrUtil.isEmpty(mail)) {
            throw exception(MAIL_SEND_MAIL_NOT_EXISTS);
        }
        return mail;
    }

    /**
     * 校验邮件参数是否确实
     *
     * @param template 邮箱模板
     * @param templateParams 参数列表
     */
    @VisibleForTesting
    void validateTemplateParams(MailTemplateDO template, Map<String, Object> templateParams) {
        template.getParams().forEach(key -> {
            Object value = templateParams.get(key);
            if (value == null) {
                throw exception(MAIL_SEND_TEMPLATE_PARAM_MISS, key);
            }
        });
    }


    /**
     * 通用占位符参数赋值
     * @param template
     * @param templateParams
     * @param mail
     * @return
     */
    private Map<String, Object> assignmentParams(MailTemplateDO template,Map<String, Object> templateParams,String mail) {

        List<String> definitionParams = template.getParams();
        if (CollectionUtil.isEmpty(definitionParams)){
            return templateParams;
        }

        Long tenantId = Long.valueOf(StrUtil.subBefore(template.getCode(), "-", false));
        TenantConfig tenantConfig = TenantConfigManager.getTenantConfig(tenantId);
        // 如果模板中包含domain参数且未准备这个参数，则赋值域名
        if (definitionParams.contains("domain") && !templateParams.containsKey("domain")){
            templateParams.put("domain",tenantConfig.getDomain());
        }
        if (definitionParams.contains("logoUrl") && !templateParams.containsKey("logoUrl")){
            templateParams.put("logoUrl",tenantConfig.getLogoUrl());
        }
        if (definitionParams.contains("shopName") && !templateParams.containsKey("shopName")){
            templateParams.put("shopName",tenantConfig.getShopName());
        }
        if (definitionParams.contains("bannerUrl") && !templateParams.containsKey("bannerUrl")){
            templateParams.put("bannerUrl",tenantConfig.getBannerUrl());
        }
        if (definitionParams.contains("supportEmail") && !templateParams.containsKey("supportEmail")){
            templateParams.put("supportEmail",tenantConfig.getSupportEmail());
        }
        if (definitionParams.contains("unsubscribeUrl") && !templateParams.containsKey("unsubscribeUrl")){
            templateParams.put("unsubscribeUrl",tenantConfig.getDomain()+generateUnsubscribeUrl(mail,tenantId));
        }
        if (definitionParams.contains("footerLink") && !templateParams.containsKey("footerLink")){
            templateParams.put("footerLink",generateFooter(tenantConfig));
        }

        return templateParams;
    }

    //生成取消订阅链接 保存到缓存 7天有效
    private String generateUnsubscribeUrl(String mail,Long tenantId) {
        String token = IdUtil.simpleUUID();

        String key = UNSUBSCRIBE_TOKEN + tenantId + ":"+token;
        stringRedisTemplate.opsForValue().set(key,mail, EXPIRED_TIME, TimeUnit.SECONDS);
        return "/unsubscribe?email=" + mail+"&ten="+tenantId+"&t="+token;
    }

    private String generateFooter(TenantConfig config) {
        StringBuilder footer = new StringBuilder();

        appendSocialLink(footer, config.getFbUrl(), "Facebook", "fb.png", config.getDomain());
        appendSocialLink(footer, config.getInsUrl(), "Instagram", "ins.png", config.getDomain());
        appendSocialLink(footer, config.getTkUrl(), "Tiktok", "tk.png", config.getDomain());
        appendSocialLink(footer, config.getTtUrl(), "Twitter", "tt.png", config.getDomain());
        appendSocialLink(footer, config.getPinUrl(), "Pinterest", "pin.png", config.getDomain());
        appendSocialLink(footer, config.getYtUrl(), "YouTube", "yt.png", config.getDomain());

        return footer.toString();
    }

    private void appendSocialLink(StringBuilder footer, String url, String alt, String img, String domain) {
        if (url != null && !url.isEmpty()) {
            footer.append("<td >")
                    .append("<a target=\"_blank\" href=\"").append(url).append("\">")
                    .append("<img height=\"32\" width=\"32\" alt=\"").append(alt).append("\" src=\"").append(domain)
                    .append("/images/template/footer/").append(img)
                    .append("\"/></a></td>");
        }
    }


    /*
<td style="padding-right: 10px"><a target="_blank" href="{{fbUrl}}"><img alt="Facebook" src="{{domain}}/images/template/footer/fb.png" height="32" width="32"/></a></td>
<td style="padding-right: 10px"> <a target="_blank" href="{{insurl}}"> <img alt="instagram" src="{{domain}}/images/template/footer/ins.png" height="32" width="32"/> </a> </td>
<td style="padding-right: 10px"> <a target="_blank" href="{{tkUrl}}"> <img alt="Tiktok" src="{{domain}}/images/template/footer/tk.png" height="32" width="32"/> </a> </td>
<td style="padding-right: 10px"> <a target="_blank" href="{{ttUrl}}"> <img alt="Twitter" src="{{domain}}/images/template/footer/tt.png" height="32" width="32"/> </a> </td>
<td style="padding-right: 10px"> <a target="_blank" href="{{pinUrl}}"> <img alt="Pinterest" src="{{domain}}/images/template/footer/pin.png" height="32" width="32"/> </a> </td>
<td style="padding-right: 10px"> <a target="_blank" href="{{ytUrl}}"> <img alt="YouTebe" src="{{domain}}/images/template/footer/yt.png" height="32" width="32"/> </a> </td>

*/

}
