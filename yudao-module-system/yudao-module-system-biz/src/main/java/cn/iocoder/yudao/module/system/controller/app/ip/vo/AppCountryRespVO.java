package cn.iocoder.yudao.module.system.controller.app.ip.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "用户 App - 国家 Response VO")
@Data
public class AppCountryRespVO {


    @Schema(description = "国家简码",  example = "CN")
    private String code;


    @Schema(description = "国家图标", example = "cn~~")
    private String icon;


    @Schema(description = "国家区号", example = "86")
    private String phoneCode;


}
