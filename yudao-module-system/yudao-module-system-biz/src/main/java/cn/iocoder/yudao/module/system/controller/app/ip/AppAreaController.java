package cn.iocoder.yudao.module.system.controller.app.ip;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.ip.core.Area;
import cn.iocoder.yudao.framework.ip.core.enums.AreaTypeEnum;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.framework.ip.core.utils.GeoIPUtils;
import cn.iocoder.yudao.module.system.controller.app.ip.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 地区")
@RestController
@RequestMapping("/system/area")
@Validated
public class AppAreaController {

    @GetMapping("/tree")
    @Operation(summary = "获得地区树")
    @PermitAll
    public CommonResult<List<AppAreaNodeRespVO>> getAreaTree() {
        Area area = AreaUtils.getArea(Area.ID_CHINA);
        Assert.notNull(area, "获取不到中国");
        return success(BeanUtils.toBean(area.getChildren(), AppAreaNodeRespVO.class));
    }

    @GetMapping("/treeByCountryCode")
    @Operation(summary = "获得指定国家的地区树")
    @PermitAll
    public CommonResult<List<AppAreaNodeRespVO>> getAreaTreeByCountryCode(@RequestParam("countryCode") String countryCode) {
        Area area = AreaUtils.getAreaByCountryCode(countryCode);
        return success(BeanUtils.toBean(area.getChildren(), AppAreaNodeRespVO.class));
    }

    @GetMapping("/treeById")
    @Operation(summary = "根据ID获得地区树")
    @PermitAll
    public CommonResult<List<AppAreaNodeRespVO>> getAreaTreeById(@RequestParam("id") Integer id) {
        Area area = AreaUtils.getArea(id);
        Assert.notNull(area, "获取不到指定ID");
        return success(BeanUtils.toBean(area.getChildren(), AppAreaNodeRespVO.class));
    }

    /**
     * 根据id获取父类下的是所有子类
     * @param id
     * @param type
     * @return
     */
    @GetMapping("/treeByChildId")
    @Operation(summary = "根据子ID获得地区树")
    @PermitAll
    public CommonResult<List<AppAreaNodeRespVO>> getAreaTreeByChildId(@RequestParam("id") Integer id,@RequestParam("type") Integer type) {

        Integer parentIdByType = AreaUtils.getParentIdByChildType(id, AreaTypeEnum.getByType(type));
        Area area = AreaUtils.getArea(parentIdByType);
        if(area==null){
            return success(null);
        }
        //Assert.notNull(area, "获取不到指定ID");
        area.getChildren().forEach(obj -> obj.setPId(area.getId())); //把父类的ID设置上去给前端使用
        return success(BeanUtils.toBean(area.getChildren(), AppAreaNodeRespVO.class));
    }

    @GetMapping("/country")
    @Operation(summary = "查询国家")
    @PermitAll
    public CommonResult<List<AppAreaCountryRespVO>> getCountry() {
        Area area = AreaUtils.getCountry();
        return success(BeanUtils.toBean(area.getChildren(), AppAreaCountryRespVO.class));
    }

    @GetMapping("/getIp")
    @Operation(summary = "获取IP")
    @PermitAll
    public CommonResult<AppIpRespVO> getIp() {
        String clientIP = ServletUtils.getClientIP();

        return success(new AppIpRespVO().setIp(clientIP));
    }

    @GetMapping("/getCountryByIp")
    @Operation(summary = "根据IP获取所属国家")
    @PermitAll
    public CommonResult<AppCountryRespVO> getCountry(HttpServletRequest request, @RequestParam(required = false) String ip) {
        // 如果没有手动传入 IP，则从请求头获取用户 IP
        String ipAddress = ip != null ? ip : ServletUtils.getClientIP();
        return success(new AppCountryRespVO().setCode(GeoIPUtils.getCountryByIP(ipAddress)));
    }


    @GetMapping("/state")
    @Operation(summary = "查询州")
    @PermitAll
    public CommonResult<List<AppAreaRespVO>> getState(Integer countryId) {
        List<Area> areas = AreaUtils.getStateByCountryId(countryId);
        return success(BeanUtils.toBean(areas, AppAreaRespVO.class));
    }

    @GetMapping("/city")
    @Operation(summary = "查询城市")
    @PermitAll
    public CommonResult<List<AppAreaRespVO>> getCity(Integer stateId) {
        List<Area> areas = AreaUtils.getCityByStateId(stateId);
        return success(BeanUtils.toBean(areas, AppAreaRespVO.class));
    }

}
