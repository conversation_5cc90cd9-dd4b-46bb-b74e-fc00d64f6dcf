package cn.iocoder.yudao.module.system.controller.app.notice.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.module.system.enums.notice.NoticeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: ruoyi-vue-pro
 * @description: 用户app - 通知公告分页 Request VO
 * @author: DingXiao
 * @create: 2025-04-11 15:58
 **/

@Data
@EqualsAndHashCode(callSuper = true)
public class AppNoticePageReqVO extends PageParam {

    /**
     * 公告类型
     *
     * 枚举 {@link NoticeTypeEnum}
     */
    private Integer type;

    @Schema(description = "展示状态，参见 CommonStatusEnum 枚举类", example = "1")
    private Integer status;

}
