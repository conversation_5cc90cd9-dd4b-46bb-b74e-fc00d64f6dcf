package cn.iocoder.yudao.module.system.service.notice;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.notice.vo.NoticePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.notice.vo.NoticeSaveReqVO;
import cn.iocoder.yudao.module.system.controller.app.notice.vo.AppNoticePageReqVO;
import cn.iocoder.yudao.module.system.controller.app.notice.vo.AppNoticeReqVO;
import cn.iocoder.yudao.module.system.controller.app.notice.vo.AppNoticeRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.notice.NoticeDO;
import cn.iocoder.yudao.module.system.dal.mysql.notice.NoticeMapper;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import com.google.common.annotations.VisibleForTesting;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.NOTICE_NOT_FOUND;

/**
 * 通知公告 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class NoticeServiceImpl implements NoticeService {

    @Resource
    private NoticeMapper noticeMapper;

    @Override
    public Long createNotice(NoticeSaveReqVO createReqVO) {
        NoticeDO notice = BeanUtils.toBean(createReqVO, NoticeDO.class);
        noticeMapper.insert(notice);
        return notice.getId();
    }

    @Override
    public void updateNotice(NoticeSaveReqVO updateReqVO) {
        // 校验是否存在
        validateNoticeExists(updateReqVO.getId());
        // 更新通知公告
        NoticeDO updateObj = BeanUtils.toBean(updateReqVO, NoticeDO.class);
        noticeMapper.updateById(updateObj);
    }

    @Override
    public void deleteNotice(Long id) {
        // 校验是否存在
        validateNoticeExists(id);
        // 删除通知公告
        noticeMapper.deleteById(id);
    }

    @Override
    public PageResult<NoticeDO> getNoticePage(NoticePageReqVO reqVO) {
        return noticeMapper.selectPage(reqVO);
    }

    @Override
    public NoticeDO getNotice(Long id) {
        return noticeMapper.selectById(id);
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.NOTICE_LIST+"#7200", key = "'#language'") //
    public List<AppNoticeRespVO> getList(AppNoticeReqVO reqVO, String language) {
        List<NoticeDO> list = noticeMapper.getList(reqVO);
        // 转换为 VO 并处理多语言
        return convertList(list, notice -> convertToAppNoticeRespVO(notice, language));
    }



    @Override
    @Cacheable(cacheNames = RedisKeyConstants.NOTICE_PAGE + "#7200",key = "{#language,#pageReqVO.type,#pageReqVO.pageNo,#pageReqVO.pageSize}"  )
    public PageResult<AppNoticeRespVO> getAppNoticePage(AppNoticePageReqVO pageReqVO, String language) {
        PageResult<NoticeDO> noticeDoPageResult = noticeMapper.selectAppPage(pageReqVO);
        List<AppNoticeRespVO> appNoticeRespVos = convertList(noticeDoPageResult.getList(), notice -> convertToAppNoticeRespVO(notice, language));
        PageResult<AppNoticeRespVO> result = BeanUtils.toBean(noticeDoPageResult, AppNoticeRespVO.class);
        result.setList(appNoticeRespVos);
        return result;
    }

    @VisibleForTesting
    public void validateNoticeExists(Long id) {
        if (id == null) {
            return;
        }
        NoticeDO notice = noticeMapper.selectById(id);
        if (notice == null) {
            throw exception(NOTICE_NOT_FOUND);
        }
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.NOTICE_DETAIL+"#17200", key = "{#language,#id}")
    public AppNoticeRespVO getNoticeById(Long id, String language) {
        NoticeDO notice = noticeMapper.selectById(id);
        if (notice != null && notice.getStatus().equals(CommonStatusEnum.ENABLE.getStatus())) {
            return convertToAppNoticeRespVO(notice, language);
        }
        return null;
    }

    private AppNoticeRespVO convertToAppNoticeRespVO(NoticeDO notice, String language) {

        AppNoticeRespVO respVO = new AppNoticeRespVO();
        respVO.setId(notice.getId());
        respVO.setType(notice.getType());
        respVO.setPublishTime(notice.getPublishTime());
        if(StrUtil.isEmpty(language)) {
            respVO.setTitle(notice.getTitleEn());
            respVO.setContent(notice.getContentEn());
        }else {
            switch ( language){
                case "zh":
                    respVO.setTitle(notice.getTitleZh());
                    respVO.setContent(notice.getContentZh());
                    break;
                case "fr":
                    respVO.setTitle(notice.getTitleFr());
                    respVO.setContent(notice.getContentFr());
                    break;
                case "de":
                    respVO.setTitle(notice.getTitleDe());
                    respVO.setContent(notice.getContentDe());
                    break;
                case "es":
                    respVO.setTitle(notice.getTitleEs());
                    respVO.setContent(notice.getContentEs());
                    break;
                case "ar":
                    respVO.setTitle(notice.getTitleAr());
                    respVO.setContent(notice.getContentAr());
                    break;
                case "en":
                default:
                    respVO.setTitle(notice.getTitleEn());
                    respVO.setContent(notice.getContentEn());
                    break;
            }
        }
        return respVO;
    }

}
