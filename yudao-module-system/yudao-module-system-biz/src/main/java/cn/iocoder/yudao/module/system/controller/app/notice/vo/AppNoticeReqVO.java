package cn.iocoder.yudao.module.system.controller.app.notice.vo;

import cn.iocoder.yudao.module.system.enums.notice.NoticeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @program: ruoyi-vue-pro
 * @description: 通知公告 Request
 * @author: DingXiao
 * @create: 2025-04-11 15:08
 **/
@Data
public class AppNoticeReqVO {


    /**
     * 公告类型
     *
     * 枚举 {@link NoticeTypeEnum}
     */
    private Integer type;

    /**
     * 显示条数
     */
    private Integer size;

    /**
     * 显示状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "展示状态，参见 CommonStatusEnum 枚举类", example = "1")
    private Integer status;

}
