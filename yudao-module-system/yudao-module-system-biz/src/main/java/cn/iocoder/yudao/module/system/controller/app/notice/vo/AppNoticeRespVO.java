package cn.iocoder.yudao.module.system.controller.app.notice.vo;

import cn.iocoder.yudao.module.system.enums.notice.NoticeTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: ruoyi-vue-pro
 * @description: 通知公告Respons
 * @author: Ding<PERSON><PERSON><PERSON>
 * @create: 2025-04-11 15:08
 **/
@Data
public class AppNoticeRespVO {

    private Long id;
    /**
     * 公告标题
     */
    private String title;
    /**
     * 公告类型
     *
     * 枚举 {@link NoticeTypeEnum}
     */
    private Integer type;
    /**
     * 公告内容
     */
    private String content;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
}
