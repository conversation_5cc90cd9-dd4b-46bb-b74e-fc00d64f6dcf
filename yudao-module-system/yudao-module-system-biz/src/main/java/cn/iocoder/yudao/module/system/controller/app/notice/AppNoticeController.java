package cn.iocoder.yudao.module.system.controller.app.notice;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.notice.vo.NoticePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.notice.vo.NoticeRespVO;
import cn.iocoder.yudao.module.system.controller.app.notice.vo.AppNoticePageReqVO;
import cn.iocoder.yudao.module.system.controller.app.notice.vo.AppNoticeReqVO;
import cn.iocoder.yudao.module.system.controller.app.notice.vo.AppNoticeRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.notice.NoticeDO;
import cn.iocoder.yudao.module.system.service.notice.NoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLanguage;

/**
 * @program: ruoyi-vue-pro
 * @description: 用户APP - 通知公告
 * @author: DingXiao
 * @create: 2025-04-11 14:33
 **/
@Tag(name = "用户 APP - 通知公告")
@RestController
@RequestMapping("/system/notice")
@Validated
@Slf4j
public class AppNoticeController {

    @Resource
    private NoticeService noticeService;


    @GetMapping("/list")
    @Operation(summary = "获取通知公告")
    @PermitAll
    public CommonResult<List<AppNoticeRespVO>> getNoticeList(AppNoticeReqVO reqVO) {
        if(reqVO.getStatus() == null) {
            reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        }
        if(reqVO.getSize() == null) {
            reqVO.setSize(5);
        }
        List<AppNoticeRespVO> list = noticeService.getList(reqVO, getCurrentLanguage().getLanguage());
        return success(BeanUtils.toBean(list, AppNoticeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获取通知公告列表分页")
    @PermitAll
    public CommonResult<PageResult<AppNoticeRespVO>> getNoticePage(@Validated AppNoticePageReqVO reqVO) {
        if(reqVO.getStatus() == null) {
            reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        }
        PageResult<AppNoticeRespVO> pageResult = noticeService.getAppNoticePage(reqVO, getCurrentLanguage().getLanguage());
        return success(pageResult);
    }

    @GetMapping("/get")
    @Operation(summary = "获取通知公告详情")
    @PermitAll
    public CommonResult<AppNoticeRespVO> getDetail(Long id) {
        AppNoticeRespVO reqVO = noticeService.getNoticeById(id,getCurrentLanguage().getLanguage());
        return success(reqVO);
    }


}
