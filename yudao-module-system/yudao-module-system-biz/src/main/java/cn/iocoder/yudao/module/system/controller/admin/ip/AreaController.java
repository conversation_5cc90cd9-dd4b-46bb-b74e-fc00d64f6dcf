package cn.iocoder.yudao.module.system.controller.admin.ip;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.ip.core.Area;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.framework.ip.core.utils.IPUtils;
import cn.iocoder.yudao.module.system.controller.admin.ip.vo.AreaNodeRespVO;
import cn.iocoder.yudao.module.system.controller.app.ip.vo.AppAreaNodeRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 地区")
@RestController
@RequestMapping("/system/area")
@Validated
public class AreaController {

    @GetMapping("/tree")
    @Operation(summary = "获得地区树")
    public CommonResult<List<AreaNodeRespVO>> getAreaTree() {
        Area area = AreaUtils.getArea(Area.ID_CHINA); //ding  ID_CHINA 修改为获取全球
        Assert.notNull(area, "获取不到中国");
        return success(BeanUtils.toBean(area.getChildren(), AreaNodeRespVO.class));
    }

    @GetMapping("/country")
    @Operation(summary = "获得地区树")
    public CommonResult<List<AreaNodeRespVO>> getCountry() {
        Area area = AreaUtils.getCountry();
        Assert.notNull(area, "获取不到全球国家");
        return success(BeanUtils.toBean(area.getChildren(), AreaNodeRespVO.class));
    }

    @GetMapping("/get-by-ip")
    @Operation(summary = "获得 IP 对应的地区名")
    @Parameter(name = "ip", description = "IP", required = true)
    public CommonResult<String> getAreaByIp(@RequestParam("ip") String ip) {
        // 获得城市
        //Area area = IPUtils.getArea(ip);
        //IPUtils.getAreaStr(ip);
        //if (area == null) {
        //    return success("未知");
        //}
        //// 格式化返回
        //return success(AreaUtils.format(area.getId()));

        return success(IPUtils.getAreaStr(ip));
    }

    @GetMapping("/treeByCountryCode")
    @Operation(summary = "获得指定国家的地区树")
    @Parameter(name = "countryCode", description = "CountryCode", required = true)
    public CommonResult<List<AppAreaNodeRespVO>> getAreaTreeByCountryCode(@RequestParam("countryCode") String countryCode) {
        Area area = AreaUtils.getAreaByCountryCode(countryCode);
        return success(BeanUtils.toBean(area.getChildren(), AppAreaNodeRespVO.class));
    }


}
