package cn.iocoder.yudao.module.system.framework.mail.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置类，包括短信客户端、短信验证码两部分
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(MailCodeProperties.class)
public class MailConfiguration {

    @Bean
    public MailCodeProperties mailCodeProperties() {
        return new MailCodeProperties();
    }

}
